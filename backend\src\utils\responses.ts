import { Response } from 'express';
import { ApiResponse } from '@/types';

export const sendResponse = <T>(
  res: Response, 
  statusCode: number, 
  message: string, 
  data?: T,
  pagination?: any
): Response => {
  const response: ApiResponse<T> = {
    success: statusCode < 400,
    message,
    ...(data && { data }),
    ...(pagination && { pagination })
  };

  return res.status(statusCode).json(response);
};

export const sendSuccess = <T>(
  res: Response, 
  message: string, 
  data?: T, 
  statusCode: number = 200,
  pagination?: any
): Response => {
  return sendResponse(res, statusCode, message, data, pagination);
};

export const sendError = (
  res: Response, 
  message: string, 
  statusCode: number = 500,
  error?: string
): Response => {
  const response: ApiResponse = {
    success: false,
    message,
    ...(error && { error })
  };

  return res.status(statusCode).json(response);
};

export const sendCreated = <T>(
  res: Response, 
  message: string, 
  data?: T
): Response => {
  return sendSuccess(res, message, data, 201);
};

export const sendNotFound = (
  res: Response, 
  message: string = 'Resource not found'
): Response => {
  return sendError(res, message, 404, 'NOT_FOUND');
};

export const sendBadRequest = (
  res: Response, 
  message: string = 'Bad request'
): Response => {
  return sendError(res, message, 400, 'BAD_REQUEST');
};

export const sendUnauthorized = (
  res: Response, 
  message: string = 'Unauthorized'
): Response => {
  return sendError(res, message, 401, 'UNAUTHORIZED');
};

export const sendForbidden = (
  res: Response, 
  message: string = 'Forbidden'
): Response => {
  return sendError(res, message, 403, 'FORBIDDEN');
};
