import { useTranslation } from 'react-i18next';
import { useAuth } from '@/hooks/useAuth';

const AdminDashboard = () => {
  const { t } = useTranslation();
  const { user } = useAuth();

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          Admin Dashboard - {user?.name}
        </h1>
        <p className="text-gray-600">
          Manage the platform, users, and system settings.
        </p>
      </div>

      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
        <p className="text-gray-500 text-center">
          Admin dashboard is under development.
        </p>
      </div>
    </div>
  );
};

export default AdminDashboard;
