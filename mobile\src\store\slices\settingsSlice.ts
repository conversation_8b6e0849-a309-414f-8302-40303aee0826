import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { UserPreferences } from '@/types';

interface SettingsState {
  theme: 'light' | 'dark';
  language: 'en' | 'hi';
  notifications: {
    push: boolean;
    email: boolean;
    courseUpdates: boolean;
    promotions: boolean;
  };
  video: {
    autoPlay: boolean;
    quality: 'low' | 'medium' | 'high';
    subtitles: boolean;
    playbackSpeed: number;
  };
  download: {
    autoDownload: boolean;
    wifiOnly: boolean;
    maxConcurrent: number;
    quality: 'low' | 'medium' | 'high';
  };
  privacy: {
    analytics: boolean;
    crashReporting: boolean;
    personalization: boolean;
  };
  security: {
    biometric: boolean;
    autoLock: boolean;
    lockTimeout: number;
    screenshotProtection: boolean;
  };
  accessibility: {
    fontSize: 'small' | 'normal' | 'large';
    highContrast: boolean;
    reduceMotion: boolean;
  };
}

const initialState: SettingsState = {
  theme: 'light',
  language: 'en',
  notifications: {
    push: true,
    email: true,
    courseUpdates: true,
    promotions: false,
  },
  video: {
    autoPlay: false,
    quality: 'medium',
    subtitles: false,
    playbackSpeed: 1.0,
  },
  download: {
    autoDownload: false,
    wifiOnly: true,
    maxConcurrent: 3,
    quality: 'medium',
  },
  privacy: {
    analytics: true,
    crashReporting: true,
    personalization: true,
  },
  security: {
    biometric: false,
    autoLock: true,
    lockTimeout: 5, // minutes
    screenshotProtection: true,
  },
  accessibility: {
    fontSize: 'normal',
    highContrast: false,
    reduceMotion: false,
  },
};

const settingsSlice = createSlice({
  name: 'settings',
  initialState,
  reducers: {
    setTheme: (state, action: PayloadAction<'light' | 'dark'>) => {
      state.theme = action.payload;
    },
    setLanguage: (state, action: PayloadAction<'en' | 'hi'>) => {
      state.language = action.payload;
    },
    updateNotificationSettings: (
      state,
      action: PayloadAction<Partial<SettingsState['notifications']>>
    ) => {
      state.notifications = { ...state.notifications, ...action.payload };
    },
    updateVideoSettings: (
      state,
      action: PayloadAction<Partial<SettingsState['video']>>
    ) => {
      state.video = { ...state.video, ...action.payload };
    },
    updateDownloadSettings: (
      state,
      action: PayloadAction<Partial<SettingsState['download']>>
    ) => {
      state.download = { ...state.download, ...action.payload };
    },
    updatePrivacySettings: (
      state,
      action: PayloadAction<Partial<SettingsState['privacy']>>
    ) => {
      state.privacy = { ...state.privacy, ...action.payload };
    },
    updateSecuritySettings: (
      state,
      action: PayloadAction<Partial<SettingsState['security']>>
    ) => {
      state.security = { ...state.security, ...action.payload };
    },
    updateAccessibilitySettings: (
      state,
      action: PayloadAction<Partial<SettingsState['accessibility']>>
    ) => {
      state.accessibility = { ...state.accessibility, ...action.payload };
    },
    resetSettings: (state) => {
      Object.assign(state, initialState);
    },
    importSettings: (state, action: PayloadAction<Partial<SettingsState>>) => {
      Object.assign(state, action.payload);
    },
  },
});

export const {
  setTheme,
  setLanguage,
  updateNotificationSettings,
  updateVideoSettings,
  updateDownloadSettings,
  updatePrivacySettings,
  updateSecuritySettings,
  updateAccessibilitySettings,
  resetSettings,
  importSettings,
} = settingsSlice.actions;

export default settingsSlice.reducer;
