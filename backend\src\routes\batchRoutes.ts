import express from 'express';
import { body } from 'express-validator';
import { batchController } from '@/controllers';
import { authenticate, authorize, validate } from '@/middleware';

const router = express.Router();

const createBatchValidation = [
  body('name')
    .trim()
    .isLength({ min: 3, max: 100 })
    .withMessage('Batch name must be between 3 and 100 characters'),
  body('description')
    .trim()
    .isLength({ min: 10, max: 500 })
    .withMessage('Description must be between 10 and 500 characters'),
  body('institutionId')
    .notEmpty()
    .withMessage('Institution ID is required'),
  body('startDate')
    .isISO8601()
    .withMessage('Please provide a valid start date'),
  body('endDate')
    .isISO8601()
    .withMessage('Please provide a valid end date'),
  body('maxStudents')
    .isInt({ min: 1 })
    .withMessage('Maximum students must be at least 1'),
  body('fee')
    .isNumeric({ no_symbols: false })
    .withMessage('Fee must be a valid number')
];

const updateBatchValidation = [
  body('name')
    .optional()
    .trim()
    .isLength({ min: 3, max: 100 })
    .withMessage('Batch name must be between 3 and 100 characters'),
  body('description')
    .optional()
    .trim()
    .isLength({ min: 10, max: 500 })
    .withMessage('Description must be between 10 and 500 characters'),
  body('startDate')
    .optional()
    .isISO8601()
    .withMessage('Please provide a valid start date'),
  body('endDate')
    .optional()
    .isISO8601()
    .withMessage('Please provide a valid end date'),
  body('maxStudents')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Maximum students must be at least 1'),
  body('fee')
    .optional()
    .isNumeric({ no_symbols: false })
    .withMessage('Fee must be a valid number')
];

const addStudentValidation = [
  body('batchId')
    .isMongoId()
    .withMessage('Invalid batch ID'),
  body('studentId')
    .isMongoId()
    .withMessage('Invalid student ID')
];

const addTeacherValidation = [
  body('batchId')
    .isMongoId()
    .withMessage('Invalid batch ID'),
  body('teacherId')
    .isMongoId()
    .withMessage('Invalid teacher ID')
];

router.use(authenticate);

router.get('/', batchController.getAllBatches);
router.get('/stats', authorize('admin', 'super_admin'), batchController.getBatchStats);
router.get('/:id', batchController.getBatchById);

router.post('/', authorize('admin', 'super_admin'), validate(createBatchValidation), batchController.createBatch);
router.put('/:id', authorize('admin', 'super_admin'), validate(updateBatchValidation), batchController.updateBatch);
router.delete('/:id', authorize('admin', 'super_admin'), batchController.deleteBatch);

router.post('/add-student', authorize('admin', 'super_admin'), validate(addStudentValidation), batchController.addStudentToBatch);
router.post('/remove-student', authorize('admin', 'super_admin'), validate(addStudentValidation), batchController.removeStudentFromBatch);
router.post('/add-teacher', authorize('admin', 'super_admin'), validate(addTeacherValidation), batchController.addTeacherToBatch);
router.post('/remove-teacher', authorize('admin', 'super_admin'), validate(addTeacherValidation), batchController.removeTeacherFromBatch);

export default router;
