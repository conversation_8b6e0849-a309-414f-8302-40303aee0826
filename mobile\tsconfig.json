{"extends": "@tsconfig/react-native/tsconfig.json", "compilerOptions": {"baseUrl": "./src", "paths": {"@/*": ["*"], "@/components/*": ["components/*"], "@/screens/*": ["screens/*"], "@/services/*": ["services/*"], "@/utils/*": ["utils/*"], "@/types/*": ["types/*"], "@/store/*": ["store/*"], "@/hooks/*": ["hooks/*"], "@/constants/*": ["constants/*"], "@/assets/*": ["../assets/*"]}}, "include": ["src/**/*", "index.js"], "exclude": ["node_modules", "android", "ios"]}