{"name": "rjwu-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "test": "jest", "test:watch": "jest --watch"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.1", "react-query": "^3.39.3", "axios": "^1.3.4", "socket.io-client": "^4.6.1", "react-hook-form": "^7.43.5", "react-i18next": "^12.2.0", "i18next": "^22.4.10", "zustand": "^4.3.6", "react-hot-toast": "^2.4.0", "lucide-react": "^0.321.0", "framer-motion": "^10.10.0", "video.js": "^8.0.4", "react-player": "^2.12.0", "date-fns": "^2.29.3", "clsx": "^1.2.1", "tailwind-merge": "^1.10.0"}, "devDependencies": {"@types/react": "^18.0.28", "@types/react-dom": "^18.0.11", "@types/video.js": "^7.3.52", "@typescript-eslint/eslint-plugin": "^5.57.1", "@typescript-eslint/parser": "^5.57.1", "@vitejs/plugin-react": "^4.0.0", "autoprefixer": "^10.4.14", "eslint": "^8.38.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.3.4", "postcss": "^8.4.21", "tailwindcss": "^3.2.7", "typescript": "^5.0.2", "vite": "^4.3.2", "jest": "^29.5.0", "@testing-library/react": "^14.0.0", "@testing-library/jest-dom": "^5.16.5", "@testing-library/user-event": "^14.4.3"}}