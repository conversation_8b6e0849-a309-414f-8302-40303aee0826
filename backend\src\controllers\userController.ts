import { Request, Response, NextFunction } from 'express';
import { User } from '@/models';
import { AuthenticatedRequest } from '@/types';
import { asyncHand<PERSON>, AppError, sendSuccess, sendCreated, sendNotFound, getPaginationOptions, getPaginationData, getSkipValue } from '@/utils';

export const getAllUsers = asyncHandler(async (req: Request, res: Response) => {
  const { page, limit } = getPaginationOptions(req.query.page as string, req.query.limit as string);
  const { role, isActive, search } = req.query;

  const filter: any = {};
  
  if (role) filter.role = role;
  if (isActive !== undefined) filter.isActive = isActive === 'true';
  
  if (search) {
    filter.$or = [
      { firstName: { $regex: search, $options: 'i' } },
      { lastName: { $regex: search, $options: 'i' } },
      { email: { $regex: search, $options: 'i' } }
    ];
  }

  const skip = getSkipValue(page, limit);
  
  const [users, total] = await Promise.all([
    User.find(filter)
      .select('-password -verifyToken -resetPasswordToken')
      .populate('batches', 'name description')
      .populate('subjects', 'name code')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit),
    User.countDocuments(filter)
  ]);

  const pagination = getPaginationData(total, page, limit);

  sendSuccess(res, 'Users retrieved successfully', users, 200, pagination);
});

export const getUserById = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  const { id } = req.params;

  const user = await User.findById(id)
    .select('-password -verifyToken -resetPasswordToken')
    .populate('batches', 'name description startDate endDate')
    .populate('subjects', 'name code description');

  if (!user) {
    return next(new AppError('User not found', 404));
  }

  sendSuccess(res, 'User retrieved successfully', { user });
});

export const createUser = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const { email, password, firstName, lastName, role, phone, institutionId } = req.body;

  const existingUser = await User.findOne({ email });
  if (existingUser) {
    return next(new AppError('User already exists with this email', 400));
  }

  const user = await User.create({
    email,
    password,
    firstName,
    lastName,
    role,
    phone,
    institutionId,
    isVerified: true,
    createdBy: req.user?.id
  });

  sendCreated(res, 'User created successfully', {
    user: {
      id: user._id,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      role: user.role,
      isActive: user.isActive,
      isVerified: user.isVerified
    }
  });
});

export const updateUser = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  const { id } = req.params;
  const updates = req.body;

  delete updates.password;
  delete updates.email;

  const user = await User.findByIdAndUpdate(
    id,
    updates,
    { new: true, runValidators: true }
  ).select('-password -verifyToken -resetPasswordToken');

  if (!user) {
    return next(new AppError('User not found', 404));
  }

  sendSuccess(res, 'User updated successfully', { user });
});

export const deleteUser = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  const { id } = req.params;

  const user = await User.findByIdAndUpdate(
    id,
    { isActive: false },
    { new: true }
  );

  if (!user) {
    return next(new AppError('User not found', 404));
  }

  sendSuccess(res, 'User deactivated successfully');
});

export const activateUser = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  const { id } = req.params;

  const user = await User.findByIdAndUpdate(
    id,
    { isActive: true },
    { new: true }
  );

  if (!user) {
    return next(new AppError('User not found', 404));
  }

  sendSuccess(res, 'User activated successfully', { user });
});

export const assignBatch = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  const { userId, batchId } = req.body;

  const user = await User.findById(userId);
  if (!user) {
    return next(new AppError('User not found', 404));
  }

  if (!user.batches.includes(batchId)) {
    user.batches.push(batchId);
    await user.save();
  }

  sendSuccess(res, 'Batch assigned successfully');
});

export const removeBatch = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  const { userId, batchId } = req.body;

  const user = await User.findById(userId);
  if (!user) {
    return next(new AppError('User not found', 404));
  }

  user.batches = user.batches.filter(batch => batch.toString() !== batchId);
  await user.save();

  sendSuccess(res, 'Batch removed successfully');
});

export const getUserStats = asyncHandler(async (req: Request, res: Response) => {
  const stats = await User.aggregate([
    {
      $group: {
        _id: '$role',
        count: { $sum: 1 },
        active: { $sum: { $cond: ['$isActive', 1, 0] } },
        verified: { $sum: { $cond: ['$isVerified', 1, 0] } }
      }
    }
  ]);

  const totalUsers = await User.countDocuments();
  const activeUsers = await User.countDocuments({ isActive: true });
  const verifiedUsers = await User.countDocuments({ isVerified: true });

  sendSuccess(res, 'User statistics retrieved successfully', {
    total: totalUsers,
    active: activeUsers,
    verified: verifiedUsers,
    byRole: stats
  });
});
