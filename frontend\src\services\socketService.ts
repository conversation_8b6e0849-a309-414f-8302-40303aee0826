import { io, Socket } from 'socket.io-client';
import { authService } from './authService';

class SocketService {
  private socket: Socket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;

  connect(): void {
    if (this.socket?.connected) {
      return;
    }

    const token = authService.getToken();
    if (!token) {
      console.warn('No auth token found, cannot connect to socket');
      return;
    }

    const SOCKET_URL = import.meta.env.VITE_SOCKET_URL || 'http://localhost:5000';

    this.socket = io(SOCKET_URL, {
      auth: {
        token,
      },
      transports: ['websocket', 'polling'],
      timeout: 20000,
    });

    this.setupEventListeners();
  }

  private setupEventListeners(): void {
    if (!this.socket) return;

    this.socket.on('connect', () => {
      console.log('Connected to server');
      this.reconnectAttempts = 0;
    });

    this.socket.on('disconnect', (reason) => {
      console.log('Disconnected from server:', reason);
      
      if (reason === 'io server disconnect') {
        // Server initiated disconnect, try to reconnect
        this.handleReconnect();
      }
    });

    this.socket.on('connect_error', (error) => {
      console.error('Connection error:', error);
      this.handleReconnect();
    });

    this.socket.on('auth_error', (error) => {
      console.error('Authentication error:', error);
      // Handle auth error (maybe redirect to login)
      authService.logout();
      window.location.href = '/login';
    });
  }

  private handleReconnect(): void {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      const delay = Math.pow(2, this.reconnectAttempts) * 1000; // Exponential backoff
      
      setTimeout(() => {
        console.log(`Attempting to reconnect... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
        this.connect();
      }, delay);
    } else {
      console.error('Max reconnection attempts reached');
    }
  }

  disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
  }

  // Event emission methods
  emit(event: string, data?: any): void {
    if (this.socket?.connected) {
      this.socket.emit(event, data);
    } else {
      console.warn('Socket not connected, cannot emit event:', event);
    }
  }

  // Event listening methods
  on(event: string, callback: (data: any) => void): void {
    if (this.socket) {
      this.socket.on(event, callback);
    }
  }

  off(event: string, callback?: (data: any) => void): void {
    if (this.socket) {
      this.socket.off(event, callback);
    }
  }

  // Live class methods
  joinLiveClass(classId: string): void {
    this.emit('join_live_class', { classId });
  }

  leaveLiveClass(classId: string): void {
    this.emit('leave_live_class', { classId });
  }

  sendChatMessage(classId: string, message: string): void {
    this.emit('chat_message', { classId, message });
  }

  raiseHand(classId: string): void {
    this.emit('raise_hand', { classId });
  }

  lowerHand(classId: string): void {
    this.emit('lower_hand', { classId });
  }

  // Real-time notifications
  subscribeToNotifications(userId: string): void {
    this.emit('subscribe_notifications', { userId });
  }

  unsubscribeFromNotifications(userId: string): void {
    this.emit('unsubscribe_notifications', { userId });
  }

  // Course progress updates
  subscribeToCourseUpdates(courseId: string): void {
    this.emit('subscribe_course', { courseId });
  }

  unsubscribeFromCourseUpdates(courseId: string): void {
    this.emit('unsubscribe_course', { courseId });
  }

  // Video synchronization for group watching
  syncVideoTime(roomId: string, time: number): void {
    this.emit('sync_video_time', { roomId, time });
  }

  pauseVideoForAll(roomId: string): void {
    this.emit('pause_video_all', { roomId });
  }

  playVideoForAll(roomId: string): void {
    this.emit('play_video_all', { roomId });
  }

  // Collaborative whiteboard
  drawOnWhiteboard(classId: string, drawData: any): void {
    this.emit('whiteboard_draw', { classId, drawData });
  }

  clearWhiteboard(classId: string): void {
    this.emit('whiteboard_clear', { classId });
  }

  // Screen sharing
  requestScreenShare(classId: string): void {
    this.emit('request_screen_share', { classId });
  }

  stopScreenShare(classId: string): void {
    this.emit('stop_screen_share', { classId });
  }

  // Quiz/Test real-time features
  joinTestSession(testId: string): void {
    this.emit('join_test_session', { testId });
  }

  leaveTestSession(testId: string): void {
    this.emit('leave_test_session', { testId });
  }

  submitTestAnswer(testId: string, questionId: string, answer: any): void {
    this.emit('submit_test_answer', { testId, questionId, answer });
  }

  // Utility methods
  isConnected(): boolean {
    return this.socket?.connected || false;
  }

  getSocket(): Socket | null {
    return this.socket;
  }

  // Room-based messaging
  joinRoom(roomId: string): void {
    this.emit('join_room', { roomId });
  }

  leaveRoom(roomId: string): void {
    this.emit('leave_room', { roomId });
  }

  sendRoomMessage(roomId: string, message: any): void {
    this.emit('room_message', { roomId, message });
  }
}

export const socketService = new SocketService();
export default socketService;
