{"name": "rjwu-admin-panel", "version": "1.0.0", "description": "RJWU EduTech Platform Admin Panel", "private": true, "dependencies": {"@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "@tanstack/react-query": "^5.0.0", "@tanstack/react-table": "^8.10.0", "axios": "^1.5.0", "chart.js": "^4.4.0", "date-fns": "^2.30.0", "framer-motion": "^10.16.0", "lucide-react": "^0.263.1", "react": "^18.2.0", "react-chartjs-2": "^5.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.45.0", "react-router-dom": "^6.15.0", "react-hot-toast": "^2.4.1", "recharts": "^2.8.0", "tailwindcss": "^3.3.0", "web-vitals": "^3.3.2", "zustand": "^4.4.0"}, "devDependencies": {"@types/react": "^18.2.21", "@types/react-dom": "^18.2.7", "@vitejs/plugin-react": "^4.0.3", "autoprefixer": "^10.4.15", "eslint": "^8.45.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "postcss": "^8.4.28", "prettier": "^3.0.0", "typescript": "^5.0.2", "vite": "^4.4.5"}, "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "test": "jest"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}