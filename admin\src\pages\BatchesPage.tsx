import React, { useState } from 'react'
import { Routes, Route } from 'react-router-dom'
import { motion } from 'framer-motion'
import {
  AcademicCapIcon,
  UserGroupIcon,
  CalendarIcon,
  CurrencyRupeeIcon,
  PlusIcon,
} from '@heroicons/react/24/outline'
import { DataTable } from '../components/DataTable'
import { BatchModal } from '../components/modals/BatchModal'

const StatCards = () => {
  const stats = [
    { name: 'Total Batches', value: '34', icon: AcademicCapIcon, color: 'bg-blue-500' },
    { name: 'Active Students', value: '1,247', icon: UserGroupIcon, color: 'bg-green-500' },
    { name: 'This Month', value: '8', icon: CalendarIcon, color: 'bg-purple-500' },
    { name: 'Revenue', value: '₹12.5L', icon: CurrencyRupeeIcon, color: 'bg-orange-500' },
  ]

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {stats.map((stat, index) => (
        <motion.div
          key={stat.name}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: index * 0.1 }}
          className="bg-white rounded-lg p-6 shadow-sm border border-gray-200"
        >
          <div className="flex items-center">
            <div className={`flex-shrink-0 p-3 rounded-md ${stat.color}`}>
              <stat.icon className="h-6 w-6 text-white" />
            </div>
            <div className="ml-5">
              <p className="text-sm font-medium text-gray-500">{stat.name}</p>
              <p className="text-2xl font-semibold text-gray-900">{stat.value}</p>
            </div>
          </div>
        </motion.div>
      ))}
    </div>
  )
}

export const BatchesPage: React.FC = () => {
  const [showBatchModal, setShowBatchModal] = useState(false)
  const [selectedBatch, setSelectedBatch] = useState<any>(null)

  const columns = [
    { key: 'name', header: 'Batch Name', sortable: true },
    { key: 'course', header: 'Course' },
    { key: 'teacher', header: 'Teacher' },
    { key: 'students', header: 'Students' },
    { key: 'startDate', header: 'Start Date', sortable: true },
    { key: 'status', header: 'Status' },
    { key: 'fee', header: 'Fee' },
    { key: 'actions', header: 'Actions' },
  ]

  const mockBatches = [
    {
      id: '1',
      name: 'JEE Mains 2024 - Batch A',
      course: 'JEE Preparation',
      teacher: 'Dr. Rajesh Kumar',
      students: '45/50',
      startDate: '2024-01-15',
      status: 'Active',
      fee: '₹25,000',
    },
    {
      id: '2',
      name: 'NEET 2024 - Biology Focus',
      course: 'NEET Preparation',
      teacher: 'Dr. Priya Sharma',
      students: '38/40',
      startDate: '2024-02-01',
      status: 'Active',
      fee: '₹22,000',
    },
    {
      id: '3',
      name: 'Class 12 Physics',
      course: 'CBSE Board',
      teacher: 'Prof. Amit Singh',
      students: '25/30',
      startDate: '2024-01-10',
      status: 'Completed',
      fee: '₹15,000',
    },
  ]

  return (
    <Routes>
      <Route path="/" element={
        <div className="space-y-6">
          {/* Header */}
          <div className="md:flex md:items-center md:justify-between">
            <div className="flex-1 min-w-0">
              <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
                Batch Management
              </h2>
              <p className="mt-1 text-sm text-gray-500">
                Manage student batches and course assignments
              </p>
            </div>
            <div className="mt-4 flex md:mt-0 md:ml-4">
              <button
                onClick={() => setShowBatchModal(true)}
                className="ml-3 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                <PlusIcon className="-ml-1 mr-2 h-5 w-5" />
                New Batch
              </button>
            </div>
          </div>

          {/* Stats */}
          <StatCards />

          {/* Filters */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <input
                type="text"
                placeholder="Search batches..."
                className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              />
              <select className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                <option value="">All Courses</option>
                <option value="jee">JEE Preparation</option>
                <option value="neet">NEET Preparation</option>
                <option value="cbse">CBSE Board</option>
              </select>
              <select className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                <option value="">All Status</option>
                <option value="active">Active</option>
                <option value="completed">Completed</option>
                <option value="upcoming">Upcoming</option>
              </select>
              <select className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                <option value="">All Teachers</option>
                <option value="dr-rajesh">Dr. Rajesh Kumar</option>
                <option value="dr-priya">Dr. Priya Sharma</option>
                <option value="prof-amit">Prof. Amit Singh</option>
              </select>
            </div>
          </div>

          {/* Batches Table */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            <DataTable
              columns={columns}
              data={mockBatches}
              onRowEdit={(batch) => {
                setSelectedBatch(batch)
                setShowBatchModal(true)
              }}
            />
          </div>

          {/* Batch Modal */}
          <BatchModal
            open={showBatchModal}
            onClose={() => {
              setShowBatchModal(false)
              setSelectedBatch(null)
            }}
            batch={selectedBatch}
          />
        </div>
      } />
    </Routes>
  )
}
