import { Request, Response, NextFunction } from 'express';
import { Batch, User } from '@/models';
import { AuthenticatedRequest } from '@/types';
import { asyncHandler, AppError, sendSuccess, sendCreated, getPaginationOptions, getPaginationData, getSkipValue } from '@/utils';

export const getAllBatches = asyncHandler(async (req: Request, res: Response) => {
  const { page, limit } = getPaginationOptions(req.query.page as string, req.query.limit as string);
  const { isActive, institutionId, search } = req.query;

  const filter: any = {};
  
  if (isActive !== undefined) filter.isActive = isActive === 'true';
  if (institutionId) filter.institutionId = institutionId;
  
  if (search) {
    filter.$or = [
      { name: { $regex: search, $options: 'i' } },
      { description: { $regex: search, $options: 'i' } }
    ];
  }

  const skip = getSkipValue(page, limit);
  
  const [batches, total] = await Promise.all([
    Batch.find(filter)
      .populate('subjects', 'name code description')
      .populate('teachers', 'firstName lastName email')
      .populate('createdBy', 'firstName lastName')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit),
    Batch.countDocuments(filter)
  ]);

  const pagination = getPaginationData(total, page, limit);

  sendSuccess(res, 'Batches retrieved successfully', batches, 200, pagination);
});

export const getBatchById = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  const { id } = req.params;

  const batch = await Batch.findById(id)
    .populate('subjects', 'name code description')
    .populate('teachers', 'firstName lastName email phone')
    .populate('students', 'firstName lastName email phone')
    .populate('createdBy', 'firstName lastName');

  if (!batch) {
    return next(new AppError('Batch not found', 404));
  }

  sendSuccess(res, 'Batch retrieved successfully', { batch });
});

export const createBatch = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const batchData = {
    ...req.body,
    createdBy: req.user?.id
  };

  const batch = await Batch.create(batchData);

  sendCreated(res, 'Batch created successfully', { batch });
});

export const updateBatch = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  const { id } = req.params;
  const updates = req.body;

  const batch = await Batch.findByIdAndUpdate(
    id,
    updates,
    { new: true, runValidators: true }
  ).populate('subjects', 'name code')
   .populate('teachers', 'firstName lastName email');

  if (!batch) {
    return next(new AppError('Batch not found', 404));
  }

  sendSuccess(res, 'Batch updated successfully', { batch });
});

export const deleteBatch = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  const { id } = req.params;

  const batch = await Batch.findByIdAndUpdate(
    id,
    { isActive: false },
    { new: true }
  );

  if (!batch) {
    return next(new AppError('Batch not found', 404));
  }

  sendSuccess(res, 'Batch deactivated successfully');
});

export const addStudentToBatch = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  const { batchId, studentId } = req.body;

  const [batch, student] = await Promise.all([
    Batch.findById(batchId),
    User.findById(studentId)
  ]);

  if (!batch) {
    return next(new AppError('Batch not found', 404));
  }

  if (!student) {
    return next(new AppError('Student not found', 404));
  }

  if (student.role !== 'student') {
    return next(new AppError('User is not a student', 400));
  }

  if (batch.currentStudents >= batch.maxStudents) {
    return next(new AppError('Batch is full', 400));
  }

  if (batch.students.includes(studentId)) {
    return next(new AppError('Student is already enrolled in this batch', 400));
  }

  batch.students.push(studentId);
  await batch.save();

  if (!student.batches.includes(batchId)) {
    student.batches.push(batchId);
    await student.save();
  }

  sendSuccess(res, 'Student added to batch successfully');
});

export const removeStudentFromBatch = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  const { batchId, studentId } = req.body;

  const [batch, student] = await Promise.all([
    Batch.findById(batchId),
    User.findById(studentId)
  ]);

  if (!batch) {
    return next(new AppError('Batch not found', 404));
  }

  batch.students = batch.students.filter(id => id.toString() !== studentId);
  await batch.save();

  if (student) {
    student.batches = student.batches.filter(id => id.toString() !== batchId);
    await student.save();
  }

  sendSuccess(res, 'Student removed from batch successfully');
});

export const addTeacherToBatch = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  const { batchId, teacherId } = req.body;

  const [batch, teacher] = await Promise.all([
    Batch.findById(batchId),
    User.findById(teacherId)
  ]);

  if (!batch) {
    return next(new AppError('Batch not found', 404));
  }

  if (!teacher) {
    return next(new AppError('Teacher not found', 404));
  }

  if (teacher.role !== 'teacher') {
    return next(new AppError('User is not a teacher', 400));
  }

  if (!batch.teachers.includes(teacherId)) {
    batch.teachers.push(teacherId);
    await batch.save();
  }

  sendSuccess(res, 'Teacher assigned to batch successfully');
});

export const removeTeacherFromBatch = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  const { batchId, teacherId } = req.body;

  const batch = await Batch.findById(batchId);
  if (!batch) {
    return next(new AppError('Batch not found', 404));
  }

  batch.teachers = batch.teachers.filter(id => id.toString() !== teacherId);
  await batch.save();

  sendSuccess(res, 'Teacher removed from batch successfully');
});

export const getBatchStats = asyncHandler(async (req: Request, res: Response) => {
  const stats = await Batch.aggregate([
    {
      $group: {
        _id: null,
        totalBatches: { $sum: 1 },
        activeBatches: { $sum: { $cond: ['$isActive', 1, 0] } },
        totalStudents: { $sum: '$currentStudents' },
        averageStudentsPerBatch: { $avg: '$currentStudents' }
      }
    }
  ]);

  const batchesByInstitution = await Batch.aggregate([
    {
      $group: {
        _id: '$institutionId',
        count: { $sum: 1 },
        totalStudents: { $sum: '$currentStudents' }
      }
    }
  ]);

  sendSuccess(res, 'Batch statistics retrieved successfully', {
    overview: stats[0] || {},
    byInstitution: batchesByInstitution
  });
});
