import { useTranslation } from 'react-i18next';
import { <PERSON> } from 'react-router-dom';
import { useQuery } from 'react-query';
import { 
  BookOpen, 
  Clock, 
  Trophy, 
  TrendingUp, 
  Play,
  Calendar,
  Bell,
  Star,
  ChevronRight,
  Target
} from 'lucide-react';
import { useAuth } from '@/hooks/useAuth';
import { courseService } from '@/services/courseService';
import { apiService } from '@/services/api';
import Loading from '@/components/common/Loading';

const StudentDashboard = () => {
  const { t } = useTranslation();
  const { user } = useAuth();

  // Fetch dashboard data
  const { data: dashboardStats, isLoading: statsLoading } = useQuery(
    'dashboard-stats',
    () => apiService.get('/dashboard/stats').then(res => res.data)
  );

  const { data: enrolledCourses, isLoading: coursesLoading } = useQuery(
    'enrolled-courses',
    () => courseService.getEnrolledCourses().then(res => res.data || [])
  );

  const { data: upcomingTests } = useQuery(
    'upcoming-tests',
    () => apiService.get('/tests/upcoming').then(res => res.data || [])
  );

  const { data: recentActivity } = useQuery(
    'recent-activity',
    () => apiService.get('/dashboard/activity').then(res => res.data || [])
  );

  if (statsLoading || coursesLoading) {
    return <Loading fullScreen text="Loading dashboard..." />;
  }

  const stats = dashboardStats || {
    totalCourses: 0,
    completedCourses: 0,
    totalHours: 0,
    certificatesEarned: 0,
    currentStreak: 0
  };

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Welcome Section */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          {t('dashboard.welcome')}, {user?.name}! 👋
        </h1>
        <p className="text-gray-600">
          Ready to continue your learning journey today?
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center">
            <div className="p-2 bg-blue-100 rounded-lg">
              <BookOpen className="w-6 h-6 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">
                {t('dashboard.stats.totalCourses')}
              </p>
              <p className="text-2xl font-bold text-gray-900">{stats.totalCourses}</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center">
            <div className="p-2 bg-green-100 rounded-lg">
              <Trophy className="w-6 h-6 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">
                {t('dashboard.stats.completedCourses')}
              </p>
              <p className="text-2xl font-bold text-gray-900">{stats.completedCourses}</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center">
            <div className="p-2 bg-purple-100 rounded-lg">
              <Clock className="w-6 h-6 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">
                {t('dashboard.stats.totalHours')}
              </p>
              <p className="text-2xl font-bold text-gray-900">{stats.totalHours}h</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center">
            <div className="p-2 bg-yellow-100 rounded-lg">
              <Star className="w-6 h-6 text-yellow-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">
                {t('dashboard.stats.certificates')}
              </p>
              <p className="text-2xl font-bold text-gray-900">{stats.certificatesEarned}</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center">
            <div className="p-2 bg-red-100 rounded-lg">
              <Target className="w-6 h-6 text-red-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">
                {t('dashboard.stats.currentStreak')}
              </p>
              <p className="text-2xl font-bold text-gray-900">{stats.currentStreak} days</p>
            </div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-8">
          {/* Continue Learning */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            <div className="p-6 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-900">
                {t('dashboard.continueLearning')}
              </h2>
            </div>
            <div className="p-6">
              {enrolledCourses && enrolledCourses.length > 0 ? (
                <div className="space-y-4">
                  {enrolledCourses.slice(0, 3).map((course: any) => (
                    <div key={course._id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                      <div className="flex items-center space-x-4">
                        <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center">
                          <BookOpen className="w-6 h-6 text-primary-600" />
                        </div>
                        <div>
                          <h3 className="font-medium text-gray-900">{course.title}</h3>
                          <p className="text-sm text-gray-500">{course.instructor?.name}</p>
                          <div className="flex items-center mt-1">
                            <div className="w-24 bg-gray-200 rounded-full h-2 mr-2">
                              <div 
                                className="bg-primary-600 h-2 rounded-full" 
                                style={{ width: `${course.progress || 0}%` }}
                              ></div>
                            </div>
                            <span className="text-xs text-gray-500">{course.progress || 0}%</span>
                          </div>
                        </div>
                      </div>
                      <Link
                        to={`/courses/${course._id}`}
                        className="text-primary-600 hover:text-primary-700"
                      >
                        <Play className="w-5 h-5" />
                      </Link>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <BookOpen className="w-12 h-12 text-gray-300 mx-auto mb-4" />
                  <p className="text-gray-500 mb-4">No courses enrolled yet</p>
                  <Link
                    to="/courses"
                    className="btn btn-primary"
                  >
                    Browse Courses
                  </Link>
                </div>
              )}
            </div>
          </div>

          {/* Upcoming Tests */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            <div className="p-6 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-900">
                {t('dashboard.upcomingTests')}
              </h2>
            </div>
            <div className="p-6">
              {upcomingTests && upcomingTests.length > 0 ? (
                <div className="space-y-4">
                  {upcomingTests.slice(0, 3).map((test: any) => (
                    <div key={test._id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                      <div>
                        <h3 className="font-medium text-gray-900">{test.title}</h3>
                        <p className="text-sm text-gray-500">{test.course?.title}</p>
                        <p className="text-xs text-gray-400">Due: {new Date(test.dueDate).toLocaleDateString()}</p>
                      </div>
                      <Link
                        to={`/test/${test._id}`}
                        className="btn btn-outline btn-sm"
                      >
                        Start Test
                      </Link>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Calendar className="w-12 h-12 text-gray-300 mx-auto mb-4" />
                  <p className="text-gray-500">No upcoming tests</p>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Quick Actions */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
            <div className="space-y-3">
              <Link
                to="/courses"
                className="flex items-center justify-between p-3 text-sm text-gray-700 hover:bg-gray-50 rounded-md transition-colors"
              >
                <span>Browse Courses</span>
                <ChevronRight className="w-4 h-4" />
              </Link>
              <Link
                to="/tests"
                className="flex items-center justify-between p-3 text-sm text-gray-700 hover:bg-gray-50 rounded-md transition-colors"
              >
                <span>View Tests</span>
                <ChevronRight className="w-4 h-4" />
              </Link>
              <Link
                to="/certificates"
                className="flex items-center justify-between p-3 text-sm text-gray-700 hover:bg-gray-50 rounded-md transition-colors"
              >
                <span>My Certificates</span>
                <ChevronRight className="w-4 h-4" />
              </Link>
              <Link
                to="/profile"
                className="flex items-center justify-between p-3 text-sm text-gray-700 hover:bg-gray-50 rounded-md transition-colors"
              >
                <span>Profile Settings</span>
                <ChevronRight className="w-4 h-4" />
              </Link>
            </div>
          </div>

          {/* Recent Activity */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Activity</h3>
            {recentActivity && recentActivity.length > 0 ? (
              <div className="space-y-3">
                {recentActivity.slice(0, 5).map((activity: any, index: number) => (
                  <div key={index} className="text-sm">
                    <p className="text-gray-900">{activity.title}</p>
                    <p className="text-gray-500 text-xs">{activity.time}</p>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-gray-500 text-sm">No recent activity</p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default StudentDashboard;
