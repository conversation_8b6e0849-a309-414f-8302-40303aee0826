import React, { useState } from 'react'
import { Routes, Route } from 'react-router-dom'
import { motion } from 'framer-motion'
import {
  BookOpenIcon,
  PlayIcon,
  DocumentTextIcon,
  ClockIcon,
  PlusIcon,
} from '@heroicons/react/24/outline'
import { DataTable } from '../components/DataTable'
import { CourseModal } from '../components/modals/CourseModal'

export const CoursesPage: React.FC = () => {
  const [showCourseModal, setShowCourseModal] = useState(false)
  const [selectedCourse, setSelectedCourse] = useState<any>(null)

  const columns = [
    { key: 'title', header: 'Course Title', sortable: true },
    { key: 'category', header: 'Category' },
    { key: 'level', header: 'Level' },
    { key: 'duration', header: 'Duration' },
    { key: 'lessons', header: 'Lessons' },
    { key: 'enrolled', header: 'Enrolled' },
    { key: 'price', header: 'Price' },
    { key: 'status', header: 'Status' },
    { key: 'actions', header: 'Actions' },
  ]

  const mockCourses = [
    {
      id: '1',
      title: 'Complete JEE Mathematics',
      category: 'JEE Preparation',
      level: 'Advanced',
      duration: '12 months',
      lessons: '240',
      enrolled: '1,247',
      price: '₹25,000',
      status: 'Active',
    },
    {
      id: '2',
      title: 'NEET Biology Masterclass',
      category: 'NEET Preparation',
      level: 'Intermediate',
      duration: '10 months',
      lessons: '180',
      enrolled: '967',
      price: '₹22,000',
      status: 'Active',
    },
    {
      id: '3',
      title: 'Physics Foundation',
      category: 'Foundation',
      level: 'Beginner',
      duration: '6 months',
      lessons: '120',
      enrolled: '543',
      price: '₹15,000',
      status: 'Draft',
    },
  ]

  const stats = [
    { name: 'Total Courses', value: '45', icon: BookOpenIcon, color: 'bg-blue-500' },
    { name: 'Active Courses', value: '38', icon: PlayIcon, color: 'bg-green-500' },
    { name: 'Total Lessons', value: '2,340', icon: DocumentTextIcon, color: 'bg-purple-500' },
    { name: 'Watch Hours', value: '12,567', icon: ClockIcon, color: 'bg-orange-500' },
  ]

  return (
    <Routes>
      <Route path="/" element={
        <div className="space-y-6">
          {/* Header */}
          <div className="md:flex md:items-center md:justify-between">
            <div className="flex-1 min-w-0">
              <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
                Course Management
              </h2>
              <p className="mt-1 text-sm text-gray-500">
                Create and manage educational courses and content
              </p>
            </div>
            <div className="mt-4 flex md:mt-0 md:ml-4">
              <button
                onClick={() => setShowCourseModal(true)}
                className="ml-3 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                <PlusIcon className="-ml-1 mr-2 h-5 w-5" />
                New Course
              </button>
            </div>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {stats.map((stat, index) => (
              <motion.div
                key={stat.name}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className="bg-white rounded-lg p-6 shadow-sm border border-gray-200"
              >
                <div className="flex items-center">
                  <div className={`flex-shrink-0 p-3 rounded-md ${stat.color}`}>
                    <stat.icon className="h-6 w-6 text-white" />
                  </div>
                  <div className="ml-5">
                    <p className="text-sm font-medium text-gray-500">{stat.name}</p>
                    <p className="text-2xl font-semibold text-gray-900">{stat.value}</p>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>

          {/* Filters */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <input
                type="text"
                placeholder="Search courses..."
                className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              />
              <select className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                <option value="">All Categories</option>
                <option value="jee">JEE Preparation</option>
                <option value="neet">NEET Preparation</option>
                <option value="foundation">Foundation</option>
                <option value="board">Board Exam</option>
              </select>
              <select className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                <option value="">All Levels</option>
                <option value="beginner">Beginner</option>
                <option value="intermediate">Intermediate</option>
                <option value="advanced">Advanced</option>
              </select>
              <select className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                <option value="">All Status</option>
                <option value="active">Active</option>
                <option value="draft">Draft</option>
                <option value="archived">Archived</option>
              </select>
            </div>
          </div>

          {/* Courses Table */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            <DataTable
              columns={columns}
              data={mockCourses}
              onRowEdit={(course) => {
                setSelectedCourse(course)
                setShowCourseModal(true)
              }}
            />
          </div>

          {/* Course Modal */}
          <CourseModal
            open={showCourseModal}
            onClose={() => {
              setShowCourseModal(false)
              setSelectedCourse(null)
            }}
            course={selectedCourse}
          />
        </div>
      } />
    </Routes>
  )
}
