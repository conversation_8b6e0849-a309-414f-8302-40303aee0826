import React, { useState } from 'react'
import { Dialog, Transition } from '@headlessui/react'
import { useForm } from 'react-hook-form'
import { XMarkIcon, BellIcon, CalendarIcon } from '@heroicons/react/24/outline'
import { LoadingSpinner } from '../LoadingSpinner'

interface NotificationModalProps {
  open: boolean
  onClose: () => void
  notification?: any
}

interface NotificationForm {
  title: string
  message: string
  type: string
  audience: string
  channel: string[]
  scheduleType: 'now' | 'later'
  scheduledDate?: string
  scheduledTime?: string
  priority: 'low' | 'medium' | 'high'
  actionUrl?: string
  actionText?: string
}

export const NotificationModal: React.FC<NotificationModalProps> = ({ 
  open, 
  onClose, 
  notification 
}) => {
  const isEditing = !!notification
  const [loading, setLoading] = useState(false)
  
  const { register, handleSubmit, formState: { errors }, reset, watch } = useForm<NotificationForm>({
    defaultValues: {
      type: 'general',
      audience: 'all-students',
      channel: ['push'],
      scheduleType: 'now',
      priority: 'medium',
    }
  })

  const watchedScheduleType = watch('scheduleType')
  const watchedChannels = watch('channel')

  React.useEffect(() => {
    if (notification) {
      reset({
        title: notification.title,
        message: notification.message,
        type: notification.type,
        audience: notification.audience,
        channel: ['push', 'email'],
        scheduleType: 'now',
        priority: 'medium',
      })
    } else {
      reset({
        type: 'general',
        audience: 'all-students',
        channel: ['push'],
        scheduleType: 'now',
        priority: 'medium',
      })
    }
  }, [notification, reset])

  const onSubmit = async (data: NotificationForm) => {
    setLoading(true)
    try {
      // API call to send/schedule notification
      console.log('Notification data:', data)
      await new Promise(resolve => setTimeout(resolve, 1000)) // Mock API call
      onClose()
    } catch (error) {
      console.error('Error sending notification:', error)
    } finally {
      setLoading(false)
    }
  }

  const channelOptions = [
    { id: 'push', label: 'Push Notification', icon: '📱' },
    { id: 'email', label: 'Email', icon: '📧' },
    { id: 'sms', label: 'SMS', icon: '💬' },
    { id: 'in-app', label: 'In-App Banner', icon: '🔔' },
  ]

  const audienceOptions = [
    { value: 'all-students', label: 'All Students (12,847)' },
    { value: 'jee-students', label: 'JEE Students (5,432)' },
    { value: 'neet-students', label: 'NEET Students (4,321)' },
    { value: 'foundation-students', label: 'Foundation Students (2,094)' },
    { value: 'teachers', label: 'All Teachers (156)' },
    { value: 'pending-payments', label: 'Pending Payments (89)' },
    { value: 'inactive-users', label: 'Inactive Users (234)' },
    { value: 'custom', label: 'Custom Audience' },
  ]

  return (
    <Transition.Root show={open} as={React.Fragment}>
      <Dialog as="div" className="relative z-50" onClose={onClose}>
        <Transition.Child
          as={React.Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
        </Transition.Child>

        <div className="fixed inset-0 z-10 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <Transition.Child
              as={React.Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
              enterTo="opacity-100 translate-y-0 sm:scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 translate-y-0 sm:scale-100"
              leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            >
              <Dialog.Panel className="relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-3xl sm:p-6">
                <div className="absolute right-0 top-0 hidden pr-4 pt-4 sm:block">
                  <button
                    type="button"
                    className="rounded-md bg-white text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
                    onClick={onClose}
                  >
                    <XMarkIcon className="h-6 w-6" />
                  </button>
                </div>

                <div className="sm:flex sm:items-start">
                  <div className="mx-auto flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-blue-100 sm:mx-0 sm:h-10 sm:w-10">
                    <BellIcon className="h-6 w-6 text-blue-600" />
                  </div>
                  <div className="mt-3 text-center sm:ml-4 sm:mt-0 sm:text-left w-full">
                    <Dialog.Title as="h3" className="text-base font-semibold leading-6 text-gray-900">
                      {isEditing ? 'Edit Notification' : 'Create New Notification'}
                    </Dialog.Title>

                    <form onSubmit={handleSubmit(onSubmit)} className="mt-6 space-y-6">
                      {/* Basic Information */}
                      <div className="space-y-4">
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                          <div className="lg:col-span-2">
                            <label htmlFor="title" className="block text-sm font-medium text-gray-700">
                              Notification Title
                            </label>
                            <input
                              {...register('title', { required: 'Title is required' })}
                              type="text"
                              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                              placeholder="e.g., New Mock Test Available"
                            />
                            {errors.title && (
                              <p className="mt-1 text-sm text-red-600">{errors.title.message}</p>
                            )}
                          </div>

                          <div>
                            <label htmlFor="type" className="block text-sm font-medium text-gray-700">
                              Notification Type
                            </label>
                            <select
                              {...register('type', { required: 'Type is required' })}
                              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                            >
                              <option value="general">General Announcement</option>
                              <option value="course">Course Update</option>
                              <option value="payment">Payment Reminder</option>
                              <option value="class">Class Reminder</option>
                              <option value="system">System Alert</option>
                              <option value="marketing">Marketing</option>
                            </select>
                          </div>

                          <div>
                            <label htmlFor="priority" className="block text-sm font-medium text-gray-700">
                              Priority
                            </label>
                            <select
                              {...register('priority', { required: 'Priority is required' })}
                              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                            >
                              <option value="low">Low</option>
                              <option value="medium">Medium</option>
                              <option value="high">High</option>
                            </select>
                          </div>

                          <div className="lg:col-span-2">
                            <label htmlFor="message" className="block text-sm font-medium text-gray-700">
                              Message Content
                            </label>
                            <textarea
                              {...register('message', { required: 'Message is required' })}
                              rows={4}
                              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                              placeholder="Write your notification message here..."
                            />
                            {errors.message && (
                              <p className="mt-1 text-sm text-red-600">{errors.message.message}</p>
                            )}
                          </div>
                        </div>
                      </div>

                      {/* Target Audience */}
                      <div className="space-y-4">
                        <h4 className="text-sm font-medium text-gray-900">Target Audience</h4>
                        <div>
                          <label htmlFor="audience" className="block text-sm font-medium text-gray-700">
                            Select Audience
                          </label>
                          <select
                            {...register('audience', { required: 'Audience is required' })}
                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                          >
                            {audienceOptions.map((option) => (
                              <option key={option.value} value={option.value}>
                                {option.label}
                              </option>
                            ))}
                          </select>
                        </div>
                      </div>

                      {/* Delivery Channels */}
                      <div className="space-y-4">
                        <h4 className="text-sm font-medium text-gray-900">Delivery Channels</h4>
                        <div className="grid grid-cols-2 lg:grid-cols-4 gap-3">
                          {channelOptions.map((channel) => (
                            <label key={channel.id} className="relative flex items-center">
                              <input
                                {...register('channel')}
                                type="checkbox"
                                value={channel.id}
                                className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                              />
                              <span className="ml-2 text-sm text-gray-700">
                                {channel.icon} {channel.label}
                              </span>
                            </label>
                          ))}
                        </div>
                      </div>

                      {/* Scheduling */}
                      <div className="space-y-4">
                        <h4 className="text-sm font-medium text-gray-900">Scheduling</h4>
                        <div className="grid grid-cols-2 gap-3">
                          <label className="relative flex cursor-pointer rounded-lg border p-4 focus:outline-none">
                            <input
                              {...register('scheduleType')}
                              type="radio"
                              value="now"
                              className="sr-only"
                            />
                            <span className="flex flex-1">
                              <span className="flex flex-col">
                                <span className="block text-sm font-medium text-gray-900">
                                  Send Now
                                </span>
                                <span className="mt-1 flex items-center text-sm text-gray-500">
                                  Deliver immediately
                                </span>
                              </span>
                            </span>
                          </label>

                          <label className="relative flex cursor-pointer rounded-lg border p-4 focus:outline-none">
                            <input
                              {...register('scheduleType')}
                              type="radio"
                              value="later"
                              className="sr-only"
                            />
                            <span className="flex flex-1">
                              <span className="flex flex-col">
                                <span className="block text-sm font-medium text-gray-900">
                                  Schedule for Later
                                </span>
                                <span className="mt-1 flex items-center text-sm text-gray-500">
                                  Choose date and time
                                </span>
                              </span>
                            </span>
                          </label>
                        </div>

                        {watchedScheduleType === 'later' && (
                          <div className="grid grid-cols-2 gap-4">
                            <div>
                              <label htmlFor="scheduledDate" className="block text-sm font-medium text-gray-700">
                                Date
                              </label>
                              <input
                                {...register('scheduledDate', {
                                  required: watchedScheduleType === 'later' ? 'Date is required' : false
                                })}
                                type="date"
                                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                              />
                            </div>
                            <div>
                              <label htmlFor="scheduledTime" className="block text-sm font-medium text-gray-700">
                                Time
                              </label>
                              <input
                                {...register('scheduledTime', {
                                  required: watchedScheduleType === 'later' ? 'Time is required' : false
                                })}
                                type="time"
                                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                              />
                            </div>
                          </div>
                        )}
                      </div>

                      {/* Call to Action */}
                      <div className="space-y-4">
                        <h4 className="text-sm font-medium text-gray-900">Call to Action (Optional)</h4>
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <label htmlFor="actionText" className="block text-sm font-medium text-gray-700">
                              Button Text
                            </label>
                            <input
                              {...register('actionText')}
                              type="text"
                              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                              placeholder="e.g., Take Test Now"
                            />
                          </div>
                          <div>
                            <label htmlFor="actionUrl" className="block text-sm font-medium text-gray-700">
                              Action URL
                            </label>
                            <input
                              {...register('actionUrl')}
                              type="url"
                              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                              placeholder="https://..."
                            />
                          </div>
                        </div>
                      </div>

                      {/* Preview */}
                      <div className="bg-gray-50 rounded-lg p-4">
                        <h4 className="text-sm font-medium text-gray-900 mb-2">Preview</h4>
                        <div className="bg-white rounded-lg p-3 border">
                          <div className="flex items-start space-x-3">
                            <BellIcon className="h-5 w-5 text-blue-600 mt-0.5" />
                            <div className="flex-1">
                              <p className="text-sm font-medium text-gray-900">
                                {watch('title') || 'Notification Title'}
                              </p>
                              <p className="text-sm text-gray-600 mt-1">
                                {watch('message') || 'Notification message will appear here...'}
                              </p>
                              {watch('actionText') && (
                                <button className="mt-2 px-3 py-1 bg-primary-600 text-white text-xs rounded">
                                  {watch('actionText')}
                                </button>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>

                      <div className="mt-5 sm:mt-4 sm:flex sm:flex-row-reverse">
                        <button
                          type="submit"
                          disabled={loading}
                          className="inline-flex w-full justify-center rounded-md bg-primary-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-primary-500 sm:ml-3 sm:w-auto disabled:opacity-50"
                        >
                          {loading ? (
                            <LoadingSpinner size="sm" />
                          ) : (
                            watchedScheduleType === 'now' ? 'Send Now' : 'Schedule Notification'
                          )}
                        </button>
                        <button
                          type="button"
                          className="mt-3 inline-flex w-full justify-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 sm:mt-0 sm:w-auto"
                          onClick={onClose}
                        >
                          Cancel
                        </button>
                      </div>
                    </form>
                  </div>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition.Root>
  )
}
