import React, { useState } from 'react'
import { Dialog, Transition } from '@headlessui/react'
import { useForm } from 'react-hook-form'
import { 
  XMarkIcon, 
  CheckCircleIcon, 
  XCircleIcon,
  EyeIcon,
  ClockIcon 
} from '@heroicons/react/24/outline'
import { LoadingSpinner } from '../LoadingSpinner'

interface ContentApprovalModalProps {
  open: boolean
  onClose: () => void
  content?: any
  selectedCount?: number
}

interface ApprovalForm {
  status: 'approved' | 'rejected' | 'pending'
  feedback: string
  category: string
  tags: string
}

export const ContentApprovalModal: React.FC<ContentApprovalModalProps> = ({ 
  open, 
  onClose, 
  content,
  selectedCount = 0 
}) => {
  const [loading, setLoading] = useState(false)
  const [activeTab, setActiveTab] = useState<'review' | 'metadata'>('review')
  
  const { register, handleSubmit, formState: { errors }, reset, watch } = useForm<ApprovalForm>({
    defaultValues: {
      status: content?.status === 'Approved' ? 'approved' : 
              content?.status === 'Rejected' ? 'rejected' : 'pending',
      feedback: '',
      category: content?.course || '',
      tags: '',
    }
  })

  const watchedStatus = watch('status')
  const isBulkAction = selectedCount > 1

  React.useEffect(() => {
    if (content) {
      reset({
        status: content.status === 'Approved' ? 'approved' : 
                content.status === 'Rejected' ? 'rejected' : 'pending',
        feedback: content.feedback || '',
        category: content.course || '',
        tags: content.tags?.join(', ') || '',
      })
    }
  }, [content, reset])

  const onSubmit = async (data: ApprovalForm) => {
    setLoading(true)
    try {
      // API call to approve/reject content
      console.log('Approval data:', data)
      await new Promise(resolve => setTimeout(resolve, 1000)) // Mock API call
      onClose()
    } catch (error) {
      console.error('Error processing approval:', error)
    } finally {
      setLoading(false)
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved':
        return <CheckCircleIcon className="h-5 w-5 text-green-600" />
      case 'rejected':
        return <XCircleIcon className="h-5 w-5 text-red-600" />
      default:
        return <ClockIcon className="h-5 w-5 text-yellow-600" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved':
        return 'bg-green-50 text-green-700 border-green-200'
      case 'rejected':
        return 'bg-red-50 text-red-700 border-red-200'
      default:
        return 'bg-yellow-50 text-yellow-700 border-yellow-200'
    }
  }

  return (
    <Transition.Root show={open} as={React.Fragment}>
      <Dialog as="div" className="relative z-50" onClose={onClose}>
        <Transition.Child
          as={React.Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
        </Transition.Child>

        <div className="fixed inset-0 z-10 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <Transition.Child
              as={React.Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
              enterTo="opacity-100 translate-y-0 sm:scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 translate-y-0 sm:scale-100"
              leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            >
              <Dialog.Panel className="relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-4xl sm:p-6">
                <div className="absolute right-0 top-0 hidden pr-4 pt-4 sm:block">
                  <button
                    type="button"
                    className="rounded-md bg-white text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
                    onClick={onClose}
                  >
                    <XMarkIcon className="h-6 w-6" />
                  </button>
                </div>

                <div className="sm:flex sm:items-start">
                  <div className="mt-3 text-center sm:ml-4 sm:mt-0 sm:text-left w-full">
                    <Dialog.Title as="h3" className="text-base font-semibold leading-6 text-gray-900">
                      {isBulkAction 
                        ? `Content Review - ${selectedCount} items` 
                        : `Content Review - ${content?.title || 'New Content'}`
                      }
                    </Dialog.Title>

                    {!isBulkAction && content && (
                      <div className="mt-4 bg-gray-50 rounded-lg p-4">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <div className="flex-shrink-0">
                              {content.type === 'Video' && <EyeIcon className="h-8 w-8 text-blue-600" />}
                              {content.type === 'Document' && <DocumentTextIcon className="h-8 w-8 text-green-600" />}
                            </div>
                            <div>
                              <h4 className="text-sm font-medium text-gray-900">{content.title}</h4>
                              <p className="text-sm text-gray-500">{content.course} • {content.teacher}</p>
                            </div>
                          </div>
                          <div className={`px-3 py-1 rounded-full text-xs font-medium border ${getStatusColor(content.status.toLowerCase())}`}>
                            <div className="flex items-center space-x-1">
                              {getStatusIcon(content.status.toLowerCase())}
                              <span>{content.status}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Tabs */}
                    <div className="mt-6">
                      <div className="border-b border-gray-200">
                        <nav className="-mb-px flex space-x-8">
                          <button
                            onClick={() => setActiveTab('review')}
                            className={`py-2 px-1 border-b-2 font-medium text-sm ${
                              activeTab === 'review'
                                ? 'border-primary-500 text-primary-600'
                                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                            }`}
                          >
                            Review & Approval
                          </button>
                          {!isBulkAction && (
                            <button
                              onClick={() => setActiveTab('metadata')}
                              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                                activeTab === 'metadata'
                                  ? 'border-primary-500 text-primary-600'
                                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                              }`}
                            >
                              Metadata
                            </button>
                          )}
                        </nav>
                      </div>
                    </div>

                    <form onSubmit={handleSubmit(onSubmit)} className="mt-6">
                      {activeTab === 'review' && (
                        <div className="space-y-4">
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                              Review Decision
                            </label>
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                              <label className="relative flex cursor-pointer rounded-lg border p-4 focus:outline-none">
                                <input
                                  {...register('status', { required: 'Status is required' })}
                                  type="radio"
                                  value="approved"
                                  className="sr-only"
                                />
                                <span className="flex flex-1">
                                  <span className="flex flex-col">
                                    <span className="flex items-center space-x-2">
                                      <CheckCircleIcon className="h-5 w-5 text-green-600" />
                                      <span className="block text-sm font-medium text-gray-900">
                                        Approve
                                      </span>
                                    </span>
                                    <span className="mt-1 flex items-center text-sm text-gray-500">
                                      Content meets quality standards
                                    </span>
                                  </span>
                                </span>
                                <CheckCircleIcon
                                  className={`h-5 w-5 text-primary-600 ${
                                    watchedStatus === 'approved' ? 'block' : 'hidden'
                                  }`}
                                />
                              </label>

                              <label className="relative flex cursor-pointer rounded-lg border p-4 focus:outline-none">
                                <input
                                  {...register('status')}
                                  type="radio"
                                  value="pending"
                                  className="sr-only"
                                />
                                <span className="flex flex-1">
                                  <span className="flex flex-col">
                                    <span className="flex items-center space-x-2">
                                      <ClockIcon className="h-5 w-5 text-yellow-600" />
                                      <span className="block text-sm font-medium text-gray-900">
                                        Pending Review
                                      </span>
                                    </span>
                                    <span className="mt-1 flex items-center text-sm text-gray-500">
                                      Requires further review
                                    </span>
                                  </span>
                                </span>
                                <CheckCircleIcon
                                  className={`h-5 w-5 text-primary-600 ${
                                    watchedStatus === 'pending' ? 'block' : 'hidden'
                                  }`}
                                />
                              </label>

                              <label className="relative flex cursor-pointer rounded-lg border p-4 focus:outline-none">
                                <input
                                  {...register('status')}
                                  type="radio"
                                  value="rejected"
                                  className="sr-only"
                                />
                                <span className="flex flex-1">
                                  <span className="flex flex-col">
                                    <span className="flex items-center space-x-2">
                                      <XCircleIcon className="h-5 w-5 text-red-600" />
                                      <span className="block text-sm font-medium text-gray-900">
                                        Reject
                                      </span>
                                    </span>
                                    <span className="mt-1 flex items-center text-sm text-gray-500">
                                      Content needs improvement
                                    </span>
                                  </span>
                                </span>
                                <CheckCircleIcon
                                  className={`h-5 w-5 text-primary-600 ${
                                    watchedStatus === 'rejected' ? 'block' : 'hidden'
                                  }`}
                                />
                              </label>
                            </div>
                          </div>

                          <div>
                            <label htmlFor="feedback" className="block text-sm font-medium text-gray-700">
                              Feedback {watchedStatus === 'rejected' && <span className="text-red-500">*</span>}
                            </label>
                            <textarea
                              {...register('feedback', {
                                required: watchedStatus === 'rejected' ? 'Feedback is required for rejection' : false
                              })}
                              rows={4}
                              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                              placeholder={
                                watchedStatus === 'approved' 
                                  ? 'Optional: Add positive feedback or suggestions...' 
                                  : watchedStatus === 'rejected'
                                  ? 'Required: Explain what needs to be improved...'
                                  : 'Add your review comments...'
                              }
                            />
                            {errors.feedback && (
                              <p className="mt-1 text-sm text-red-600">{errors.feedback.message}</p>
                            )}
                          </div>
                        </div>
                      )}

                      {activeTab === 'metadata' && !isBulkAction && (
                        <div className="space-y-4">
                          <div>
                            <label htmlFor="category" className="block text-sm font-medium text-gray-700">
                              Category
                            </label>
                            <input
                              {...register('category')}
                              type="text"
                              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                              placeholder="e.g., Mathematics, Physics, Chemistry"
                            />
                          </div>

                          <div>
                            <label htmlFor="tags" className="block text-sm font-medium text-gray-700">
                              Tags (comma separated)
                            </label>
                            <input
                              {...register('tags')}
                              type="text"
                              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                              placeholder="e.g., algebra, quadratic, equations, jee"
                            />
                          </div>

                          <div className="bg-gray-50 rounded-lg p-4">
                            <h4 className="text-sm font-medium text-gray-900 mb-2">Content Information</h4>
                            <dl className="grid grid-cols-1 sm:grid-cols-2 gap-4 text-sm">
                              <div>
                                <dt className="font-medium text-gray-500">Upload Date:</dt>
                                <dd className="text-gray-900">{content?.uploadDate}</dd>
                              </div>
                              <div>
                                <dt className="font-medium text-gray-500">File Size:</dt>
                                <dd className="text-gray-900">24.5 MB</dd>
                              </div>
                              <div>
                                <dt className="font-medium text-gray-500">Duration:</dt>
                                <dd className="text-gray-900">45 minutes</dd>
                              </div>
                              <div>
                                <dt className="font-medium text-gray-500">Format:</dt>
                                <dd className="text-gray-900">MP4 (1080p)</dd>
                              </div>
                            </dl>
                          </div>
                        </div>
                      )}

                      <div className="mt-6 sm:flex sm:flex-row-reverse">
                        <button
                          type="submit"
                          disabled={loading}
                          className="inline-flex w-full justify-center rounded-md bg-primary-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-primary-500 sm:ml-3 sm:w-auto disabled:opacity-50"
                        >
                          {loading ? (
                            <LoadingSpinner size="sm" />
                          ) : (
                            isBulkAction ? 'Apply to All' : 'Save Review'
                          )}
                        </button>
                        <button
                          type="button"
                          className="mt-3 inline-flex w-full justify-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 sm:mt-0 sm:w-auto"
                          onClick={onClose}
                        >
                          Cancel
                        </button>
                      </div>
                    </form>
                  </div>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition.Root>
  )
}
