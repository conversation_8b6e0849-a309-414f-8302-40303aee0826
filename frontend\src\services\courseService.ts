import { apiService } from './api';
import { Course, Lesson, ApiResponse, PaginatedResponse, SearchFilters } from '@/types';

class CourseService {
  // Course CRUD operations
  async getAllCourses(params?: {
    page?: number;
    limit?: number;
    category?: string;
    level?: string;
    search?: string;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  }): Promise<ApiResponse<PaginatedResponse<Course>>> {
    return apiService.get<PaginatedResponse<Course>>('/courses', params);
  }

  async getCourseById(id: string): Promise<ApiResponse<Course>> {
    return apiService.get<Course>(`/courses/${id}`);
  }

  async getEnrolledCourses(userId?: string): Promise<ApiResponse<Course[]>> {
    const endpoint = userId ? `/courses/enrolled/${userId}` : '/courses/enrolled';
    return apiService.get<Course[]>(endpoint);
  }

  async getFeaturedCourses(): Promise<ApiResponse<Course[]>> {
    return apiService.get<Course[]>('/courses/featured');
  }

  async getPopularCourses(): Promise<ApiResponse<Course[]>> {
    return apiService.get<Course[]>('/courses/popular');
  }

  async getRecommendedCourses(): Promise<ApiResponse<Course[]>> {
    return apiService.get<Course[]>('/courses/recommended');
  }

  async searchCourses(query: string, filters?: SearchFilters): Promise<ApiResponse<PaginatedResponse<Course>>> {
    return apiService.get<PaginatedResponse<Course>>('/courses/search', {
      q: query,
      ...filters,
    });
  }

  // Course enrollment
  async enrollInCourse(courseId: string): Promise<ApiResponse<void>> {
    return apiService.post<void>(`/courses/${courseId}/enroll`);
  }

  async unenrollFromCourse(courseId: string): Promise<ApiResponse<void>> {
    return apiService.delete<void>(`/courses/${courseId}/enroll`);
  }

  async checkEnrollmentStatus(courseId: string): Promise<ApiResponse<{ isEnrolled: boolean }>> {
    return apiService.get<{ isEnrolled: boolean }>(`/courses/${courseId}/enrollment-status`);
  }

  // Lesson operations
  async getCourseLessons(courseId: string): Promise<ApiResponse<Lesson[]>> {
    return apiService.get<Lesson[]>(`/courses/${courseId}/lessons`);
  }

  async getLessonById(courseId: string, lessonId: string): Promise<ApiResponse<Lesson>> {
    return apiService.get<Lesson>(`/courses/${courseId}/lessons/${lessonId}`);
  }

  async markLessonComplete(courseId: string, lessonId: string): Promise<ApiResponse<void>> {
    return apiService.post<void>(`/courses/${courseId}/lessons/${lessonId}/complete`);
  }

  async getLessonProgress(courseId: string, lessonId: string): Promise<ApiResponse<{ isCompleted: boolean; watchTime: number }>> {
    return apiService.get<{ isCompleted: boolean; watchTime: number }>(`/courses/${courseId}/lessons/${lessonId}/progress`);
  }

  async updateLessonProgress(courseId: string, lessonId: string, watchTime: number): Promise<ApiResponse<void>> {
    return apiService.put<void>(`/courses/${courseId}/lessons/${lessonId}/progress`, { watchTime });
  }

  // Course progress
  async getCourseProgress(courseId: string): Promise<ApiResponse<{
    completedLessons: number;
    totalLessons: number;
    percentage: number;
    timeSpent: number;
  }>> {
    return apiService.get(`/courses/${courseId}/progress`);
  }

  // Course reviews and ratings
  async getCourseReviews(courseId: string, page = 1, limit = 10): Promise<ApiResponse<PaginatedResponse<any>>> {
    return apiService.get(`/courses/${courseId}/reviews`, { page, limit });
  }

  async addCourseReview(courseId: string, rating: number, comment: string): Promise<ApiResponse<void>> {
    return apiService.post<void>(`/courses/${courseId}/reviews`, { rating, comment });
  }

  async updateCourseReview(courseId: string, reviewId: string, rating: number, comment: string): Promise<ApiResponse<void>> {
    return apiService.put<void>(`/courses/${courseId}/reviews/${reviewId}`, { rating, comment });
  }

  async deleteCourseReview(courseId: string, reviewId: string): Promise<ApiResponse<void>> {
    return apiService.delete<void>(`/courses/${courseId}/reviews/${reviewId}`);
  }

  // Course categories
  async getCategories(): Promise<ApiResponse<string[]>> {
    return apiService.get<string[]>('/courses/categories');
  }

  // Course wishlist
  async addToWishlist(courseId: string): Promise<ApiResponse<void>> {
    return apiService.post<void>(`/courses/${courseId}/wishlist`);
  }

  async removeFromWishlist(courseId: string): Promise<ApiResponse<void>> {
    return apiService.delete<void>(`/courses/${courseId}/wishlist`);
  }

  async getWishlist(): Promise<ApiResponse<Course[]>> {
    return apiService.get<Course[]>('/courses/wishlist');
  }

  // Course notes
  async getLessonNotes(courseId: string, lessonId: string): Promise<ApiResponse<any[]>> {
    return apiService.get(`/courses/${courseId}/lessons/${lessonId}/notes`);
  }

  async addLessonNote(courseId: string, lessonId: string, note: {
    content: string;
    timestamp: number;
  }): Promise<ApiResponse<void>> {
    return apiService.post<void>(`/courses/${courseId}/lessons/${lessonId}/notes`, note);
  }

  async updateLessonNote(courseId: string, lessonId: string, noteId: string, content: string): Promise<ApiResponse<void>> {
    return apiService.put<void>(`/courses/${courseId}/lessons/${lessonId}/notes/${noteId}`, { content });
  }

  async deleteLessonNote(courseId: string, lessonId: string, noteId: string): Promise<ApiResponse<void>> {
    return apiService.delete<void>(`/courses/${courseId}/lessons/${lessonId}/notes/${noteId}`);
  }

  // Course certificates
  async getCertificate(courseId: string): Promise<ApiResponse<{ certificateUrl: string }>> {
    return apiService.get<{ certificateUrl: string }>(`/courses/${courseId}/certificate`);
  }

  async downloadCertificate(courseId: string): Promise<void> {
    return apiService.downloadFile(`/courses/${courseId}/certificate/download`, `certificate-${courseId}.pdf`);
  }

  // Course statistics (for instructors/admins)
  async getCourseStats(courseId: string): Promise<ApiResponse<{
    enrollments: number;
    completions: number;
    averageRating: number;
    totalRevenue: number;
    watchTime: number;
  }>> {
    return apiService.get(`/courses/${courseId}/stats`);
  }
}

export const courseService = new CourseService();
export default courseService;
