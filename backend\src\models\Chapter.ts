import mongoose, { Schema } from 'mongoose';
import { IChapter } from '@/types';

const chapterSchema = new Schema<IChapter>({
  title: {
    type: String,
    required: [true, 'Chapter title is required'],
    trim: true,
    maxlength: [200, 'Chapter title cannot exceed 200 characters']
  },
  description: {
    type: String,
    required: [true, 'Chapter description is required'],
    maxlength: [1000, 'Description cannot exceed 1000 characters']
  },
  subjectId: {
    type: Schema.Types.ObjectId,
    ref: 'Subject',
    required: [true, 'Subject ID is required']
  },
  order: {
    type: Number,
    required: [true, 'Chapter order is required'],
    min: [1, 'Order must be at least 1']
  },
  videos: [{
    type: Schema.Types.ObjectId,
    ref: 'Video'
  }],
  notes: [{
    type: Schema.Types.ObjectId,
    ref: 'Note'
  }],
  tests: [{
    type: Schema.Types.ObjectId,
    ref: 'Test'
  }],
  isActive: {
    type: Boolean,
    default: true
  },
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  }
}, {
  timestamps: true
});

chapterSchema.index({ subjectId: 1, order: 1 }, { unique: true });
chapterSchema.index({ isActive: 1 });

export default mongoose.model<IChapter>('Chapter', chapterSchema);
