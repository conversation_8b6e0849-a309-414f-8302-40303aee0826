import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { User, BiometricConfig } from '@/types';
import { AuthService } from '@/services/auth';
import { SecurityService } from '@/services/security';

interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  biometric: BiometricConfig;
  loginAttempts: number;
  isLocked: boolean;
  lockUntil: number | null;
}

const initialState: AuthState = {
  user: null,
  token: null,
  isAuthenticated: false,
  isLoading: false,
  error: null,
  biometric: {
    isAvailable: false,
    biometryType: '',
    isEnabled: false,
  },
  loginAttempts: 0,
  isLocked: false,
  lockUntil: null,
};

export const login = createAsyncThunk(
  'auth/login',
  async (credentials: { email: string; password: string }) => {
    const response = await AuthService.login(credentials);
    return response;
  }
);

export const loginWithBiometric = createAsyncThunk(
  'auth/loginWithBiometric',
  async () => {
    const response = await AuthService.loginWithBiometric();
    return response;
  }
);

export const register = createAsyncThunk(
  'auth/register',
  async (userData: {
    email: string;
    password: string;
    name: string;
    role: string;
  }) => {
    const response = await AuthService.register(userData);
    return response;
  }
);

export const logout = createAsyncThunk('auth/logout', async () => {
  await AuthService.logout();
  await SecurityService.clearSecureData();
});

export const refreshToken = createAsyncThunk('auth/refreshToken', async () => {
  const response = await AuthService.refreshToken();
  return response;
});

export const verifyBiometric = createAsyncThunk(
  'auth/verifyBiometric',
  async () => {
    const isAvailable = await SecurityService.isBiometricAvailable();
    const biometryType = await SecurityService.getBiometryType();
    return { isAvailable, biometryType };
  }
);

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    resetLoginAttempts: (state) => {
      state.loginAttempts = 0;
      state.isLocked = false;
      state.lockUntil = null;
    },
    incrementLoginAttempts: (state) => {
      state.loginAttempts += 1;
      if (state.loginAttempts >= 5) {
        state.isLocked = true;
        state.lockUntil = Date.now() + 15 * 60 * 1000; // 15 minutes
      }
    },
    enableBiometric: (state) => {
      state.biometric.isEnabled = true;
    },
    disableBiometric: (state) => {
      state.biometric.isEnabled = false;
    },
    updateUser: (state, action: PayloadAction<Partial<User>>) => {
      if (state.user) {
        state.user = { ...state.user, ...action.payload };
      }
    },
  },
  extraReducers: (builder) => {
    builder
      // Login
      .addCase(login.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(login.fulfilled, (state, action) => {
        state.isLoading = false;
        state.user = action.payload.user;
        state.token = action.payload.token;
        state.isAuthenticated = true;
        state.error = null;
        state.loginAttempts = 0;
        state.isLocked = false;
        state.lockUntil = null;
      })
      .addCase(login.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Login failed';
        state.loginAttempts += 1;
        if (state.loginAttempts >= 5) {
          state.isLocked = true;
          state.lockUntil = Date.now() + 15 * 60 * 1000;
        }
      })
      // Biometric Login
      .addCase(loginWithBiometric.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(loginWithBiometric.fulfilled, (state, action) => {
        state.isLoading = false;
        state.user = action.payload.user;
        state.token = action.payload.token;
        state.isAuthenticated = true;
        state.error = null;
      })
      .addCase(loginWithBiometric.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Biometric authentication failed';
      })
      // Register
      .addCase(register.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(register.fulfilled, (state, action) => {
        state.isLoading = false;
        state.user = action.payload.user;
        state.token = action.payload.token;
        state.isAuthenticated = true;
        state.error = null;
      })
      .addCase(register.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Registration failed';
      })
      // Logout
      .addCase(logout.fulfilled, (state) => {
        state.user = null;
        state.token = null;
        state.isAuthenticated = false;
        state.error = null;
        state.loginAttempts = 0;
        state.isLocked = false;
        state.lockUntil = null;
      })
      // Refresh Token
      .addCase(refreshToken.fulfilled, (state, action) => {
        state.token = action.payload.token;
        state.user = action.payload.user;
      })
      // Verify Biometric
      .addCase(verifyBiometric.fulfilled, (state, action) => {
        state.biometric.isAvailable = action.payload.isAvailable;
        state.biometric.biometryType = action.payload.biometryType;
      });
  },
});

export const {
  clearError,
  resetLoginAttempts,
  incrementLoginAttempts,
  enableBiometric,
  disableBiometric,
  updateUser,
} = authSlice.actions;

export default authSlice.reducer;
