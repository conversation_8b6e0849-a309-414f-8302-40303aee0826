import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { DownloadItem } from '@/types';
import { DownloadService } from '@/services/download';

interface DownloadsState {
  downloads: DownloadItem[];
  isDownloading: boolean;
  error: string | null;
  totalProgress: number;
  activeDownloads: number;
  downloadedSize: number;
  totalSize: number;
}

const initialState: DownloadsState = {
  downloads: [],
  isDownloading: false,
  error: null,
  totalProgress: 0,
  activeDownloads: 0,
  downloadedSize: 0,
  totalSize: 0,
};

export const startDownload = createAsyncThunk(
  'downloads/startDownload',
  async (item: Omit<DownloadItem, 'id' | 'progress' | 'status' | 'createdAt'>) => {
    const downloadId = await DownloadService.startDownload(item);
    return { ...item, id: downloadId };
  }
);

export const pauseDownload = createAsyncThunk(
  'downloads/pauseDownload',
  async (downloadId: string) => {
    await DownloadService.pauseDownload(downloadId);
    return downloadId;
  }
);

export const resumeDownload = createAsyncThunk(
  'downloads/resumeDownload',
  async (downloadId: string) => {
    await DownloadService.resumeDownload(downloadId);
    return downloadId;
  }
);

export const cancelDownload = createAsyncThunk(
  'downloads/cancelDownload',
  async (downloadId: string) => {
    await DownloadService.cancelDownload(downloadId);
    return downloadId;
  }
);

export const deleteDownload = createAsyncThunk(
  'downloads/deleteDownload',
  async (downloadId: string) => {
    await DownloadService.deleteDownload(downloadId);
    return downloadId;
  }
);

export const retryDownload = createAsyncThunk(
  'downloads/retryDownload',
  async (downloadId: string) => {
    await DownloadService.retryDownload(downloadId);
    return downloadId;
  }
);

export const fetchDownloads = createAsyncThunk(
  'downloads/fetchDownloads',
  async () => {
    const downloads = await DownloadService.getDownloads();
    return downloads;
  }
);

export const clearCompletedDownloads = createAsyncThunk(
  'downloads/clearCompleted',
  async () => {
    await DownloadService.clearCompletedDownloads();
  }
);

const downloadsSlice = createSlice({
  name: 'downloads',
  initialState,
  reducers: {
    updateDownloadProgress: (
      state,
      action: PayloadAction<{
        id: string;
        progress: number;
        downloadedSize: number;
      }>
    ) => {
      const { id, progress, downloadedSize } = action.payload;
      const download = state.downloads.find((d) => d.id === id);
      if (download) {
        download.progress = progress;
        download.downloadedSize = downloadedSize;
        download.status = progress >= 100 ? 'completed' : 'downloading';
      }
      
      // Update total progress
      const totalDownloaded = state.downloads.reduce(
        (sum, d) => sum + d.downloadedSize,
        0
      );
      const totalSize = state.downloads.reduce((sum, d) => sum + d.size, 0);
      
      state.downloadedSize = totalDownloaded;
      state.totalSize = totalSize;
      state.totalProgress = totalSize > 0 ? (totalDownloaded / totalSize) * 100 : 0;
      
      // Update active downloads count
      state.activeDownloads = state.downloads.filter(
        (d) => d.status === 'downloading'
      ).length;
      
      state.isDownloading = state.activeDownloads > 0;
    },
    updateDownloadStatus: (
      state,
      action: PayloadAction<{
        id: string;
        status: DownloadItem['status'];
        error?: string;
      }>
    ) => {
      const { id, status, error } = action.payload;
      const download = state.downloads.find((d) => d.id === id);
      if (download) {
        download.status = status;
        if (error) {
          state.error = error;
        }
      }
      
      // Update active downloads count
      state.activeDownloads = state.downloads.filter(
        (d) => d.status === 'downloading'
      ).length;
      
      state.isDownloading = state.activeDownloads > 0;
    },
    clearError: (state) => {
      state.error = null;
    },
    setDownloadError: (state, action: PayloadAction<string>) => {
      state.error = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      // Start Download
      .addCase(startDownload.pending, (state) => {
        state.error = null;
      })
      .addCase(startDownload.fulfilled, (state, action) => {
        const newDownload: DownloadItem = {
          ...action.payload,
          progress: 0,
          status: 'pending',
          downloadedSize: 0,
          createdAt: new Date(),
        };
        state.downloads.push(newDownload);
        state.activeDownloads += 1;
        state.isDownloading = true;
      })
      .addCase(startDownload.rejected, (state, action) => {
        state.error = action.error.message || 'Failed to start download';
      })
      // Pause Download
      .addCase(pauseDownload.fulfilled, (state, action) => {
        const download = state.downloads.find((d) => d.id === action.payload);
        if (download) {
          download.status = 'paused';
        }
        state.activeDownloads = state.downloads.filter(
          (d) => d.status === 'downloading'
        ).length;
        state.isDownloading = state.activeDownloads > 0;
      })
      // Resume Download
      .addCase(resumeDownload.fulfilled, (state, action) => {
        const download = state.downloads.find((d) => d.id === action.payload);
        if (download) {
          download.status = 'downloading';
        }
        state.activeDownloads = state.downloads.filter(
          (d) => d.status === 'downloading'
        ).length;
        state.isDownloading = state.activeDownloads > 0;
      })
      // Cancel Download
      .addCase(cancelDownload.fulfilled, (state, action) => {
        state.downloads = state.downloads.filter((d) => d.id !== action.payload);
        state.activeDownloads = state.downloads.filter(
          (d) => d.status === 'downloading'
        ).length;
        state.isDownloading = state.activeDownloads > 0;
      })
      // Delete Download
      .addCase(deleteDownload.fulfilled, (state, action) => {
        state.downloads = state.downloads.filter((d) => d.id !== action.payload);
        state.activeDownloads = state.downloads.filter(
          (d) => d.status === 'downloading'
        ).length;
        state.isDownloading = state.activeDownloads > 0;
      })
      // Retry Download
      .addCase(retryDownload.fulfilled, (state, action) => {
        const download = state.downloads.find((d) => d.id === action.payload);
        if (download) {
          download.status = 'pending';
          download.progress = 0;
          download.downloadedSize = 0;
        }
      })
      // Fetch Downloads
      .addCase(fetchDownloads.fulfilled, (state, action) => {
        state.downloads = action.payload;
        state.activeDownloads = action.payload.filter(
          (d) => d.status === 'downloading'
        ).length;
        state.isDownloading = state.activeDownloads > 0;
        
        // Calculate totals
        const totalDownloaded = action.payload.reduce(
          (sum, d) => sum + d.downloadedSize,
          0
        );
        const totalSize = action.payload.reduce((sum, d) => sum + d.size, 0);
        
        state.downloadedSize = totalDownloaded;
        state.totalSize = totalSize;
        state.totalProgress = totalSize > 0 ? (totalDownloaded / totalSize) * 100 : 0;
      })
      // Clear Completed Downloads
      .addCase(clearCompletedDownloads.fulfilled, (state) => {
        state.downloads = state.downloads.filter((d) => d.status !== 'completed');
      });
  },
});

export const {
  updateDownloadProgress,
  updateDownloadStatus,
  clearError,
  setDownloadError,
} = downloadsSlice.actions;

export default downloadsSlice.reducer;
