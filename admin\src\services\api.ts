import axios from 'axios'
import toast from 'react-hot-toast'

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api'

export const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Request interceptor
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('admin-auth-storage')
    if (token) {
      const { token: authToken } = JSON.parse(token)
      if (authToken) {
        config.headers.Authorization = `Bearer ${authToken}`
      }
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor
api.interceptors.response.use(
  (response) => {
    return response
  },
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('admin-auth-storage')
      window.location.href = '/login'
      toast.error('Session expired. Please login again.')
    } else if (error.response?.status >= 500) {
      toast.error('Server error. Please try again later.')
    } else if (error.response?.data?.message) {
      toast.error(error.response.data.message)
    } else {
      toast.error('An error occurred. Please try again.')
    }
    return Promise.reject(error)
  }
)

// API endpoints
export const adminAPI = {
  // Dashboard
  getDashboardStats: () => api.get('/admin/dashboard/stats'),
  getRevenueData: (period: string) => api.get(`/admin/dashboard/revenue?period=${period}`),
  getUserGrowthData: (period: string) => api.get(`/admin/dashboard/user-growth?period=${period}`),
  
  // Users
  getUsers: (params: any) => api.get('/admin/users', { params }),
  getUser: (id: string) => api.get(`/admin/users/${id}`),
  createUser: (data: any) => api.post('/admin/users', data),
  updateUser: (id: string, data: any) => api.put(`/admin/users/${id}`, data),
  deleteUser: (id: string) => api.delete(`/admin/users/${id}`),
  bulkUpdateUsers: (data: any) => api.post('/admin/users/bulk-update', data),
  exportUsers: (params: any) => api.get('/admin/users/export', { params }),
  
  // Batches
  getBatches: (params: any) => api.get('/admin/batches', { params }),
  getBatch: (id: string) => api.get(`/admin/batches/${id}`),
  createBatch: (data: any) => api.post('/admin/batches', data),
  updateBatch: (id: string, data: any) => api.put(`/admin/batches/${id}`, data),
  deleteBatch: (id: string) => api.delete(`/admin/batches/${id}`),
  getBatchStudents: (id: string) => api.get(`/admin/batches/${id}/students`),
  addStudentsToBatch: (id: string, studentIds: string[]) => 
    api.post(`/admin/batches/${id}/students`, { studentIds }),
  
  // Courses
  getCourses: (params: any) => api.get('/admin/courses', { params }),
  getCourse: (id: string) => api.get(`/admin/courses/${id}`),
  createCourse: (data: any) => api.post('/admin/courses', data),
  updateCourse: (id: string, data: any) => api.put(`/admin/courses/${id}`, data),
  deleteCourse: (id: string) => api.delete(`/admin/courses/${id}`),
  
  // Content
  getContent: (params: any) => api.get('/admin/content', { params }),
  approveContent: (id: string) => api.post(`/admin/content/${id}/approve`),
  rejectContent: (id: string, reason: string) => 
    api.post(`/admin/content/${id}/reject`, { reason }),
  bulkApproveContent: (ids: string[]) => api.post('/admin/content/bulk-approve', { ids }),
  
  // Payments
  getPayments: (params: any) => api.get('/admin/payments', { params }),
  getPayment: (id: string) => api.get(`/admin/payments/${id}`),
  processRefund: (id: string, amount: number, reason: string) => 
    api.post(`/admin/payments/${id}/refund`, { amount, reason }),
  getPaymentGateways: () => api.get('/admin/payments/gateways'),
  updatePaymentGateway: (gateway: string, config: any) => 
    api.put(`/admin/payments/gateways/${gateway}`, config),
  
  // Analytics
  getAnalytics: (type: string, params: any) => api.get(`/admin/analytics/${type}`, { params }),
  
  // Notifications
  getNotifications: (params: any) => api.get('/admin/notifications', { params }),
  sendNotification: (data: any) => api.post('/admin/notifications', data),
  scheduleNotification: (data: any) => api.post('/admin/notifications/schedule', data),
  
  // Audit Logs
  getAuditLogs: (params: any) => api.get('/admin/audit-logs', { params }),
  
  // Support
  getSupportTickets: (params: any) => api.get('/admin/support/tickets', { params }),
  updateTicketStatus: (id: string, status: string) => 
    api.put(`/admin/support/tickets/${id}`, { status }),
  
  // Settings
  getSettings: () => api.get('/admin/settings'),
  updateSettings: (data: any) => api.put('/admin/settings', data),
  getSystemHealth: () => api.get('/admin/system/health'),
  createBackup: () => api.post('/admin/system/backup'),
  getBackups: () => api.get('/admin/system/backups'),
}
