import React, { useState } from 'react'
import { useForm } from 'react-hook-form'
import { LoadingSpinner } from '../LoadingSpinner'
import { BellIcon } from '@heroicons/react/24/outline'

interface NotificationSettingsForm {
  emailNotifications: boolean
  smsNotifications: boolean
  pushNotifications: boolean
  inAppNotifications: boolean
  emailProvider: string
  smtpHost: string
  smtpPort: number
  smtpUsername: string
  smtpPassword: string
  smtpEncryption: string
  smsProvider: string
  smsApiKey: string
  smsApiSecret: string
  pushProvider: string
  fcmServerKey: string
  fcmSenderId: string
  notificationFrequency: string
  digestEnabled: boolean
  digestTime: string
  unsubscribeEnabled: boolean
}

export const NotificationSettings: React.FC = () => {
  const [loading, setLoading] = useState(false)

  const { register, handleSubmit, formState: { errors }, watch } = useForm<NotificationSettingsForm>({
    defaultValues: {
      emailNotifications: true,
      smsNotifications: true,
      pushNotifications: true,
      inAppNotifications: true,
      emailProvider: 'smtp',
      smtpHost: 'smtp.gmail.com',
      smtpPort: 587,
      smtpUsername: '',
      smtpPassword: '',
      smtpEncryption: 'tls',
      smsProvider: 'twilio',
      smsApiKey: '',
      smsApiSecret: '',
      pushProvider: 'fcm',
      fcmServerKey: '',
      fcmSenderId: '',
      notificationFrequency: 'immediate',
      digestEnabled: true,
      digestTime: '09:00',
      unsubscribeEnabled: true,
    }
  })

  const watchedValues = {
    emailNotifications: watch('emailNotifications'),
    smsNotifications: watch('smsNotifications'),
    pushNotifications: watch('pushNotifications'),
    emailProvider: watch('emailProvider'),
    smsProvider: watch('smsProvider'),
    digestEnabled: watch('digestEnabled'),
  }

  const onSubmit = async (data: NotificationSettingsForm) => {
    setLoading(true)
    try {
      // API call to save notification settings
      console.log('Notification settings:', data)
      await new Promise(resolve => setTimeout(resolve, 1000)) // Mock API call
    } catch (error) {
      console.error('Error saving notification settings:', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      {/* Notification Channels */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center space-x-2 mb-4">
          <BellIcon className="h-5 w-5 text-primary-600" />
          <h3 className="text-lg font-medium text-gray-900">Notification Channels</h3>
        </div>
        
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <label htmlFor="emailNotifications" className="text-sm font-medium text-gray-900">
                Email Notifications
              </label>
              <p className="text-sm text-gray-500">
                Send notifications via email
              </p>
            </div>
            <input
              {...register('emailNotifications')}
              type="checkbox"
              className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
            />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <label htmlFor="smsNotifications" className="text-sm font-medium text-gray-900">
                SMS Notifications
              </label>
              <p className="text-sm text-gray-500">
                Send notifications via SMS
              </p>
            </div>
            <input
              {...register('smsNotifications')}
              type="checkbox"
              className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
            />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <label htmlFor="pushNotifications" className="text-sm font-medium text-gray-900">
                Push Notifications
              </label>
              <p className="text-sm text-gray-500">
                Send push notifications to mobile apps
              </p>
            </div>
            <input
              {...register('pushNotifications')}
              type="checkbox"
              className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
            />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <label htmlFor="inAppNotifications" className="text-sm font-medium text-gray-900">
                In-App Notifications
              </label>
              <p className="text-sm text-gray-500">
                Show notifications within the application
              </p>
            </div>
            <input
              {...register('inAppNotifications')}
              type="checkbox"
              className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
            />
          </div>
        </div>
      </div>

      {/* Email Configuration */}
      {watchedValues.emailNotifications && (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Email Configuration</h3>
          
          <div className="space-y-4">
            <div>
              <label htmlFor="emailProvider" className="block text-sm font-medium text-gray-700">
                Email Provider
              </label>
              <select
                {...register('emailProvider')}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
              >
                <option value="smtp">Custom SMTP</option>
                <option value="sendgrid">SendGrid</option>
                <option value="mailgun">Mailgun</option>
                <option value="ses">Amazon SES</option>
              </select>
            </div>

            {watchedValues.emailProvider === 'smtp' && (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                <div>
                  <label htmlFor="smtpHost" className="block text-sm font-medium text-gray-700">
                    SMTP Host
                  </label>
                  <input
                    {...register('smtpHost', { required: 'SMTP host is required' })}
                    type="text"
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                  />
                  {errors.smtpHost && (
                    <p className="mt-1 text-sm text-red-600">{errors.smtpHost.message}</p>
                  )}
                </div>

                <div>
                  <label htmlFor="smtpPort" className="block text-sm font-medium text-gray-700">
                    SMTP Port
                  </label>
                  <input
                    {...register('smtpPort', { required: 'SMTP port is required' })}
                    type="number"
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                  />
                  {errors.smtpPort && (
                    <p className="mt-1 text-sm text-red-600">{errors.smtpPort.message}</p>
                  )}
                </div>

                <div>
                  <label htmlFor="smtpUsername" className="block text-sm font-medium text-gray-700">
                    SMTP Username
                  </label>
                  <input
                    {...register('smtpUsername', { required: 'SMTP username is required' })}
                    type="text"
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                  />
                  {errors.smtpUsername && (
                    <p className="mt-1 text-sm text-red-600">{errors.smtpUsername.message}</p>
                  )}
                </div>

                <div>
                  <label htmlFor="smtpPassword" className="block text-sm font-medium text-gray-700">
                    SMTP Password
                  </label>
                  <input
                    {...register('smtpPassword', { required: 'SMTP password is required' })}
                    type="password"
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                  />
                  {errors.smtpPassword && (
                    <p className="mt-1 text-sm text-red-600">{errors.smtpPassword.message}</p>
                  )}
                </div>

                <div>
                  <label htmlFor="smtpEncryption" className="block text-sm font-medium text-gray-700">
                    Encryption
                  </label>
                  <select
                    {...register('smtpEncryption')}
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                  >
                    <option value="none">None</option>
                    <option value="tls">TLS</option>
                    <option value="ssl">SSL</option>
                  </select>
                </div>
              </div>
            )}

            <div className="flex items-center space-x-4">
              <button
                type="button"
                className="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700"
              >
                Test Email Configuration
              </button>
              <button
                type="button"
                className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
              >
                Send Test Email
              </button>
            </div>
          </div>
        </div>
      )}

      {/* SMS Configuration */}
      {watchedValues.smsNotifications && (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">SMS Configuration</h3>
          
          <div className="space-y-4">
            <div>
              <label htmlFor="smsProvider" className="block text-sm font-medium text-gray-700">
                SMS Provider
              </label>
              <select
                {...register('smsProvider')}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
              >
                <option value="twilio">Twilio</option>
                <option value="textlocal">TextLocal</option>
                <option value="msg91">MSG91</option>
                <option value="aws-sns">AWS SNS</option>
              </select>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
              <div>
                <label htmlFor="smsApiKey" className="block text-sm font-medium text-gray-700">
                  API Key
                </label>
                <input
                  {...register('smsApiKey', { required: 'SMS API key is required' })}
                  type="password"
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                />
                {errors.smsApiKey && (
                  <p className="mt-1 text-sm text-red-600">{errors.smsApiKey.message}</p>
                )}
              </div>

              <div>
                <label htmlFor="smsApiSecret" className="block text-sm font-medium text-gray-700">
                  API Secret
                </label>
                <input
                  {...register('smsApiSecret', { required: 'SMS API secret is required' })}
                  type="password"
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                />
                {errors.smsApiSecret && (
                  <p className="mt-1 text-sm text-red-600">{errors.smsApiSecret.message}</p>
                )}
              </div>
            </div>

            <div className="flex items-center space-x-4">
              <button
                type="button"
                className="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700"
              >
                Test SMS Configuration
              </button>
              <button
                type="button"
                className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
              >
                Send Test SMS
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Push Notification Configuration */}
      {watchedValues.pushNotifications && (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Push Notification Configuration</h3>
          
          <div className="space-y-4">
            <div>
              <label htmlFor="pushProvider" className="block text-sm font-medium text-gray-700">
                Push Provider
              </label>
              <select
                {...register('pushProvider')}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
              >
                <option value="fcm">Firebase Cloud Messaging</option>
                <option value="apns">Apple Push Notification</option>
                <option value="onesignal">OneSignal</option>
              </select>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
              <div>
                <label htmlFor="fcmServerKey" className="block text-sm font-medium text-gray-700">
                  FCM Server Key
                </label>
                <input
                  {...register('fcmServerKey', { required: 'FCM server key is required' })}
                  type="password"
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                />
                {errors.fcmServerKey && (
                  <p className="mt-1 text-sm text-red-600">{errors.fcmServerKey.message}</p>
                )}
              </div>

              <div>
                <label htmlFor="fcmSenderId" className="block text-sm font-medium text-gray-700">
                  FCM Sender ID
                </label>
                <input
                  {...register('fcmSenderId', { required: 'FCM sender ID is required' })}
                  type="text"
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                />
                {errors.fcmSenderId && (
                  <p className="mt-1 text-sm text-red-600">{errors.fcmSenderId.message}</p>
                )}
              </div>
            </div>

            <button
              type="button"
              className="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700"
            >
              Test Push Configuration
            </button>
          </div>
        </div>
      )}

      {/* Notification Preferences */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Notification Preferences</h3>
        
        <div className="space-y-4">
          <div>
            <label htmlFor="notificationFrequency" className="block text-sm font-medium text-gray-700">
              Notification Frequency
            </label>
            <select
              {...register('notificationFrequency')}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
            >
              <option value="immediate">Immediate</option>
              <option value="hourly">Hourly Digest</option>
              <option value="daily">Daily Digest</option>
              <option value="weekly">Weekly Digest</option>
            </select>
          </div>

          <div className="flex items-center justify-between">
            <div>
              <label htmlFor="digestEnabled" className="text-sm font-medium text-gray-900">
                Enable Digest Notifications
              </label>
              <p className="text-sm text-gray-500">
                Send summary notifications instead of individual ones
              </p>
            </div>
            <input
              {...register('digestEnabled')}
              type="checkbox"
              className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
            />
          </div>

          {watchedValues.digestEnabled && (
            <div>
              <label htmlFor="digestTime" className="block text-sm font-medium text-gray-700">
                Digest Time
              </label>
              <input
                {...register('digestTime')}
                type="time"
                className="mt-1 block w-32 rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
              />
            </div>
          )}

          <div className="flex items-center justify-between">
            <div>
              <label htmlFor="unsubscribeEnabled" className="text-sm font-medium text-gray-900">
                Allow Unsubscribe
              </label>
              <p className="text-sm text-gray-500">
                Include unsubscribe links in notification emails
              </p>
            </div>
            <input
              {...register('unsubscribeEnabled')}
              type="checkbox"
              className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
            />
          </div>
        </div>
      </div>

      {/* Save Button */}
      <div className="flex justify-end">
        <button
          type="submit"
          disabled={loading}
          className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"
        >
          {loading ? (
            <LoadingSpinner size="sm" />
          ) : (
            'Save Notification Settings'
          )}
        </button>
      </div>
    </form>
  )
}
