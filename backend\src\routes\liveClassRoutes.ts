import { Router } from 'express';
import { body, param, query } from 'express-validator';
import LiveClassController from '../controllers/LiveClassController';
import LiveClassPollController from '../controllers/LiveClassPollController';
import { authMiddleware, roleMiddleware } from '../middleware/auth';

const router = Router();

// Live Class Management Routes
router.post(
  '/',
  authMiddleware,
  roleMiddleware(['teacher', 'admin']),
  [
    body('title').notEmpty().withMessage('Title is required'),
    body('description').notEmpty().withMessage('Description is required'),
    body('course').isMongoId().withMessage('Valid course ID is required'),
    body('scheduledStart').isISO8601().withMessage('Valid start time is required'),
    body('scheduledEnd').isISO8601().withMessage('Valid end time is required'),
    body('maxParticipants').optional().isInt({ min: 1 }).withMessage('Max participants must be a positive integer')
  ],
  LiveClassController.createLiveClass
);

router.get(
  '/',
  authMiddleware,
  [
    query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
    query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
    query('status').optional().isIn(['scheduled', 'live', 'ended', 'cancelled']).withMessage('Invalid status')
  ],
  LiveClassController.getLiveClasses
);

router.get(
  '/:id',
  authMiddleware,
  [
    param('id').isMongoId().withMessage('Valid live class ID is required')
  ],
  LiveClassController.getLiveClassById
);

router.put(
  '/:id',
  authMiddleware,
  roleMiddleware(['teacher', 'admin']),
  [
    param('id').isMongoId().withMessage('Valid live class ID is required')
  ],
  LiveClassController.updateLiveClass
);

router.delete(
  '/:id',
  authMiddleware,
  roleMiddleware(['teacher', 'admin']),
  [
    param('id').isMongoId().withMessage('Valid live class ID is required')
  ],
  LiveClassController.deleteLiveClass
);

// Live Class Control Routes
router.post(
  '/:id/start',
  authMiddleware,
  roleMiddleware(['teacher', 'admin']),
  [
    param('id').isMongoId().withMessage('Valid live class ID is required')
  ],
  LiveClassController.startLiveClass
);

router.post(
  '/:id/end',
  authMiddleware,
  roleMiddleware(['teacher', 'admin']),
  [
    param('id').isMongoId().withMessage('Valid live class ID is required')
  ],
  LiveClassController.endLiveClass
);

// Participant Management Routes
router.post(
  '/:id/join',
  authMiddleware,
  [
    param('id').isMongoId().withMessage('Valid live class ID is required')
  ],
  LiveClassController.joinLiveClass
);

router.post(
  '/:id/leave',
  authMiddleware,
  [
    param('id').isMongoId().withMessage('Valid live class ID is required')
  ],
  LiveClassController.leaveLiveClass
);

router.get(
  '/:id/participants',
  authMiddleware,
  [
    param('id').isMongoId().withMessage('Valid live class ID is required')
  ],
  LiveClassController.getParticipants
);

router.put(
  '/:id/participants/:participantId/permissions',
  authMiddleware,
  roleMiddleware(['teacher', 'admin']),
  [
    param('id').isMongoId().withMessage('Valid live class ID is required'),
    param('participantId').isMongoId().withMessage('Valid participant ID is required')
  ],
  LiveClassController.updateParticipantPermissions
);

router.delete(
  '/:id/participants/:participantId',
  authMiddleware,
  roleMiddleware(['teacher', 'admin']),
  [
    param('id').isMongoId().withMessage('Valid live class ID is required'),
    param('participantId').isMongoId().withMessage('Valid participant ID is required')
  ],
  LiveClassController.removeParticipant
);

// Chat Routes
router.post(
  '/:id/chat',
  authMiddleware,
  [
    param('id').isMongoId().withMessage('Valid live class ID is required'),
    body('message').notEmpty().withMessage('Message is required'),
    body('type').optional().isIn(['text', 'emoji', 'file']).withMessage('Invalid message type')
  ],
  LiveClassController.sendChatMessage
);

router.get(
  '/:id/chat',
  authMiddleware,
  [
    param('id').isMongoId().withMessage('Valid live class ID is required'),
    query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
    query('skip').optional().isInt({ min: 0 }).withMessage('Skip must be non-negative')
  ],
  LiveClassController.getChatMessages
);

router.post(
  '/:id/chat/:messageId/pin',
  authMiddleware,
  roleMiddleware(['teacher', 'admin']),
  [
    param('id').isMongoId().withMessage('Valid live class ID is required'),
    param('messageId').isMongoId().withMessage('Valid message ID is required')
  ],
  LiveClassController.pinChatMessage
);

router.post(
  '/:id/chat/:messageId/hide',
  authMiddleware,
  roleMiddleware(['teacher', 'admin']),
  [
    param('id').isMongoId().withMessage('Valid live class ID is required'),
    param('messageId').isMongoId().withMessage('Valid message ID is required')
  ],
  LiveClassController.hideChatMessage
);

router.post(
  '/:id/announcement',
  authMiddleware,
  roleMiddleware(['teacher', 'admin']),
  [
    param('id').isMongoId().withMessage('Valid live class ID is required'),
    body('message').notEmpty().withMessage('Announcement message is required')
  ],
  LiveClassController.sendAnnouncement
);

// Poll Routes
router.post(
  '/:id/polls',
  authMiddleware,
  roleMiddleware(['teacher', 'admin']),
  [
    param('id').isMongoId().withMessage('Valid live class ID is required'),
    body('question').notEmpty().withMessage('Poll question is required'),
    body('type').isIn(['multiple-choice', 'single-choice', 'text', 'rating', 'yes-no']).withMessage('Invalid poll type'),
    body('options').optional().isArray().withMessage('Options must be an array')
  ],
  LiveClassPollController.createPoll
);

router.get(
  '/:id/polls',
  authMiddleware,
  [
    param('id').isMongoId().withMessage('Valid live class ID is required')
  ],
  LiveClassPollController.getLiveClassPolls
);

router.post(
  '/:id/polls/:pollId/start',
  authMiddleware,
  roleMiddleware(['teacher', 'admin']),
  [
    param('id').isMongoId().withMessage('Valid live class ID is required'),
    param('pollId').isMongoId().withMessage('Valid poll ID is required')
  ],
  LiveClassPollController.startPoll
);

router.post(
  '/:id/polls/:pollId/close',
  authMiddleware,
  roleMiddleware(['teacher', 'admin']),
  [
    param('id').isMongoId().withMessage('Valid live class ID is required'),
    param('pollId').isMongoId().withMessage('Valid poll ID is required')
  ],
  LiveClassPollController.closePoll
);

router.post(
  '/:id/polls/:pollId/respond',
  authMiddleware,
  [
    param('id').isMongoId().withMessage('Valid live class ID is required'),
    param('pollId').isMongoId().withMessage('Valid poll ID is required'),
    body('answer').exists().withMessage('Answer is required')
  ],
  LiveClassPollController.submitResponse
);

router.get(
  '/:id/polls/:pollId/results',
  authMiddleware,
  [
    param('id').isMongoId().withMessage('Valid live class ID is required'),
    param('pollId').isMongoId().withMessage('Valid poll ID is required')
  ],
  LiveClassPollController.getPollResults
);

router.delete(
  '/:id/polls/:pollId',
  authMiddleware,
  [
    param('id').isMongoId().withMessage('Valid live class ID is required'),
    param('pollId').isMongoId().withMessage('Valid poll ID is required')
  ],
  LiveClassPollController.deletePoll
);

export default router;
