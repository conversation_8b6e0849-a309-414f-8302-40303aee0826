import { Request, Response, NextFunction } from 'express';
import { User } from '@/models';
import { AuthenticatedRequest } from '@/types';
import { asyncHandler, AppError, sendSuccess, sendCreated } from '@/utils';
import crypto from 'crypto';

export const register = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  const { email, password, firstName, lastName, role = 'student' } = req.body;

  const existingUser = await User.findOne({ email });
  if (existingUser) {
    return next(new AppError('User already exists with this email', 400));
  }

  const verifyToken = crypto.randomBytes(32).toString('hex');

  const user = await User.create({
    email,
    password,
    firstName,
    lastName,
    role,
    verifyToken
  });

  const token = user.generateAuthToken();

  res.cookie('token', token, {
    expires: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'strict'
  });

  sendCreated(res, 'User registered successfully. Please verify your email.', {
    user: {
      id: user._id,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      role: user.role,
      isVerified: user.isVerified
    },
    token
  });
});

export const login = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  const { email, password } = req.body;

  if (!email || !password) {
    return next(new AppError('Please provide email and password', 400));
  }

  const user = await User.findOne({ email }).select('+password');
  if (!user || !(await user.comparePassword(password))) {
    return next(new AppError('Invalid email or password', 401));
  }

  if (!user.isActive) {
    return next(new AppError('Your account has been deactivated', 401));
  }

  user.lastLogin = new Date();
  await user.save({ validateBeforeSave: false });

  const token = user.generateAuthToken();

  res.cookie('token', token, {
    expires: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'strict'
  });

  sendSuccess(res, 'Login successful', {
    user: {
      id: user._id,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      role: user.role,
      isVerified: user.isVerified,
      lastLogin: user.lastLogin
    },
    token
  });
});

export const logout = asyncHandler(async (req: Request, res: Response) => {
  res.cookie('token', '', {
    expires: new Date(Date.now()),
    httpOnly: true
  });

  sendSuccess(res, 'Logout successful');
});

export const getProfile = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const user = await User.findById(req.user?.id)
    .populate('batches', 'name description')
    .populate('subjects', 'name code');

  sendSuccess(res, 'Profile retrieved successfully', { user });
});

export const updateProfile = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const allowedFields = ['firstName', 'lastName', 'phone', 'dateOfBirth', 'address'];
  const updates: any = {};

  allowedFields.forEach(field => {
    if (req.body[field] !== undefined) {
      updates[field] = req.body[field];
    }
  });

  const user = await User.findByIdAndUpdate(
    req.user?.id,
    updates,
    { new: true, runValidators: true }
  );

  sendSuccess(res, 'Profile updated successfully', { user });
});

export const changePassword = asyncHandler(async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  const { currentPassword, newPassword } = req.body;

  const user = await User.findById(req.user?.id).select('+password');
  if (!user) {
    return next(new AppError('User not found', 404));
  }

  if (!(await user.comparePassword(currentPassword))) {
    return next(new AppError('Current password is incorrect', 400));
  }

  user.password = newPassword;
  await user.save();

  sendSuccess(res, 'Password changed successfully');
});

export const forgotPassword = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  const { email } = req.body;

  const user = await User.findOne({ email });
  if (!user) {
    return next(new AppError('No user found with this email', 404));
  }

  const resetToken = crypto.randomBytes(32).toString('hex');
  user.resetPasswordToken = crypto.createHash('sha256').update(resetToken).digest('hex');
  user.resetPasswordExpire = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes

  await user.save({ validateBeforeSave: false });

  sendSuccess(res, 'Password reset token sent to email', { resetToken });
});

export const resetPassword = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  const { token } = req.params;
  const { password } = req.body;

  const hashedToken = crypto.createHash('sha256').update(token).digest('hex');

  const user = await User.findOne({
    resetPasswordToken: hashedToken,
    resetPasswordExpire: { $gt: Date.now() }
  });

  if (!user) {
    return next(new AppError('Token is invalid or has expired', 400));
  }

  user.password = password;
  user.resetPasswordToken = undefined;
  user.resetPasswordExpire = undefined;
  await user.save();

  const authToken = user.generateAuthToken();

  sendSuccess(res, 'Password reset successful', { token: authToken });
});

export const verifyEmail = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  const { token } = req.params;

  const user = await User.findOne({ verifyToken: token });
  if (!user) {
    return next(new AppError('Invalid verification token', 400));
  }

  user.isVerified = true;
  user.verifyToken = undefined;
  await user.save({ validateBeforeSave: false });

  sendSuccess(res, 'Email verified successfully');
});
