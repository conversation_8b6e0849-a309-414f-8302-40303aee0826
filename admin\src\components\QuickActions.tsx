import React from 'react'
import { Link } from 'react-router-dom'
import {
  UserPlusIcon,
  AcademicCapIcon,
  BellIcon,
  DocumentPlusIcon,
  CogIcon,
  ChartBarIcon,
} from '@heroicons/react/24/outline'

const actions = [
  {
    name: 'Add New User',
    href: '/users/new',
    icon: UserPlusIcon,
    color: 'bg-blue-500 hover:bg-blue-600',
  },
  {
    name: 'Create Batch',
    href: '/batches/new',
    icon: AcademicCapIcon,
    color: 'bg-green-500 hover:bg-green-600',
  },
  {
    name: 'Send Notification',
    href: '/notifications/new',
    icon: BellIcon,
    color: 'bg-purple-500 hover:bg-purple-600',
  },
  {
    name: 'Upload Content',
    href: '/content/upload',
    icon: DocumentPlusIcon,
    color: 'bg-orange-500 hover:bg-orange-600',
  },
  {
    name: 'View Reports',
    href: '/analytics',
    icon: ChartBarIcon,
    color: 'bg-indigo-500 hover:bg-indigo-600',
  },
  {
    name: 'System Settings',
    href: '/settings',
    icon: CogIcon,
    color: 'bg-gray-500 hover:bg-gray-600',
  },
]

export const QuickActions: React.FC = () => {
  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
      <div className="px-6 py-4 border-b border-gray-200">
        <h3 className="text-lg font-medium text-gray-900">Quick Actions</h3>
      </div>
      <div className="p-6">
        <div className="grid grid-cols-2 gap-4">
          {actions.map((action) => (
            <Link
              key={action.name}
              to={action.href}
              className={`${action.color} text-white rounded-lg p-4 flex flex-col items-center justify-center space-y-2 transition-colors duration-200 hover:scale-105 transform`}
            >
              <action.icon className="h-6 w-6" />
              <span className="text-sm font-medium text-center">{action.name}</span>
            </Link>
          ))}
        </div>
      </div>
    </div>
  )
}
