const {getDefaultConfig, mergeConfig} = require('@react-native/metro-config');

const config = {
  transformer: {
    getTransformOptions: async () => ({
      transform: {
        experimentalImportSupport: false,
        inlineRequires: true,
      },
    }),
  },
  resolver: {
    alias: {
      '@': './src',
      '@/components': './src/components',
      '@/screens': './src/screens',
      '@/services': './src/services',
      '@/utils': './src/utils',
      '@/types': './src/types',
      '@/store': './src/store',
      '@/hooks': './src/hooks',
      '@/constants': './src/constants',
      '@/assets': './assets',
    },
  },
};

module.exports = mergeConfig(getDefaultConfig(__dirname), config);
