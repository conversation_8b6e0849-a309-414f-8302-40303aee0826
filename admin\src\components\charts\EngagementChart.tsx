import React from 'react'
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  <PERSON><PERSON><PERSON>,
  Responsive<PERSON><PERSON><PERSON>,
  <PERSON>,
} from 'recharts'

const data = [
  { day: 'Mon', loginRate: 85, videoCompletion: 72, quizAttempts: 68 },
  { day: 'Tue', loginRate: 88, videoCompletion: 75, quizAttempts: 71 },
  { day: 'Wed', loginRate: 92, videoCompletion: 78, quizAttempts: 74 },
  { day: 'Thu', loginRate: 89, videoCompletion: 81, quizAttempts: 77 },
  { day: 'Fri', loginRate: 94, videoCompletion: 83, quizAttempts: 79 },
  { day: 'Sat', loginRate: 87, videoCompletion: 76, quizAttempts: 73 },
  { day: 'Sun', loginRate: 82, videoCompletion: 69, quizAttempts: 65 },
]

export const EngagementChart: React.FC = () => {
  return (
    <div className="h-80">
      <ResponsiveContainer width="100%" height="100%">
        <LineChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
          <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
          <XAxis 
            dataKey="day" 
            axisLine={false}
            tickLine={false}
            tick={{ fontSize: 12, fill: '#6b7280' }}
          />
          <YAxis 
            axisLine={false}
            tickLine={false}
            tick={{ fontSize: 12, fill: '#6b7280' }}
            domain={[0, 100]}
            tickFormatter={(value) => `${value}%`}
          />
          <Tooltip
            contentStyle={{
              backgroundColor: '#374151',
              border: 'none',
              borderRadius: '8px',
              color: 'white',
            }}
            formatter={(value: number) => [`${value}%`, '']}
            labelStyle={{ color: '#d1d5db' }}
          />
          <Legend />
          <Line
            type="monotone"
            dataKey="loginRate"
            stroke="#3b82f6"
            strokeWidth={2}
            dot={{ fill: '#3b82f6', strokeWidth: 2, r: 4 }}
            name="Login Rate"
          />
          <Line
            type="monotone"
            dataKey="videoCompletion"
            stroke="#10b981"
            strokeWidth={2}
            dot={{ fill: '#10b981', strokeWidth: 2, r: 4 }}
            name="Video Completion"
          />
          <Line
            type="monotone"
            dataKey="quizAttempts"
            stroke="#f59e0b"
            strokeWidth={2}
            dot={{ fill: '#f59e0b', strokeWidth: 2, r: 4 }}
            name="Quiz Attempts"
          />
        </LineChart>
      </ResponsiveContainer>
    </div>
  )
}
