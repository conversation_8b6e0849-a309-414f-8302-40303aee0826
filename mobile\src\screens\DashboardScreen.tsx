import React, { useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  RefreshControl,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useTheme } from '@/context/ThemeContext';
import { useI18n } from '@/context/I18nContext';
import { useAppDispatch, useAppSelector } from '@/hooks/redux';
import { fetchFeaturedCourses, fetchMyCourses } from '@/store/slices/coursesSlice';

export const DashboardScreen: React.FC = () => {
  const { colors } = useTheme();
  const { t } = useI18n();
  const dispatch = useAppDispatch();
  const { user } = useAppSelector(state => state.auth);
  const { featuredCourses, myCourses, isLoading } = useAppSelector(state => state.courses);
  const [refreshing, setRefreshing] = React.useState(false);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      await Promise.all([
        dispatch(fetchFeaturedCourses()),
        dispatch(fetchMyCourses()),
      ]);
    } catch (error) {
      console.error('Failed to load dashboard data:', error);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadDashboardData();
    setRefreshing(false);
  };

  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Good Morning';
    if (hour < 17) return 'Good Afternoon';
    return 'Good Evening';
  };

  return (
    <ScrollView
      style={[styles.container, { backgroundColor: colors.background }]}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
      }
    >
      {/* Header */}
      <View style={styles.header}>
        <View>
          <Text style={[styles.greeting, { color: colors.textSecondary }]}>
            {getGreeting()}
          </Text>
          <Text style={[styles.userName, { color: colors.text }]}>
            {user?.name || 'Student'}
          </Text>
        </View>
        <TouchableOpacity style={[styles.notificationButton, { backgroundColor: colors.surface }]}>
          <Icon name="notifications" size={24} color={colors.text} />
          <View style={[styles.notificationBadge, { backgroundColor: colors.error }]} />
        </TouchableOpacity>
      </View>

      {/* Stats Cards */}
      <View style={styles.statsContainer}>
        <View style={[styles.statCard, { backgroundColor: colors.primary }]}>
          <Icon name="school" size={32} color={colors.background} />
          <Text style={[styles.statNumber, { color: colors.background }]}>
            {myCourses.length}
          </Text>
          <Text style={[styles.statLabel, { color: colors.background }]}>
            Enrolled Courses
          </Text>
        </View>
        
        <View style={[styles.statCard, { backgroundColor: colors.secondary }]}>
          <Icon name="trending-up" size={32} color={colors.background} />
          <Text style={[styles.statNumber, { color: colors.background }]}>
            {Math.round(myCourses.reduce((acc, course) => acc + course.progress, 0) / myCourses.length) || 0}%
          </Text>
          <Text style={[styles.statLabel, { color: colors.background }]}>
            Avg Progress
          </Text>
        </View>
      </View>

      {/* Continue Learning */}
      {myCourses.length > 0 && (
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>
              {t('dashboard.continueLearning')}
            </Text>
            <TouchableOpacity>
              <Text style={[styles.seeAll, { color: colors.primary }]}>
                See All
              </Text>
            </TouchableOpacity>
          </View>
          
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            {myCourses.slice(0, 5).map((course) => (
              <TouchableOpacity
                key={course.id}
                style={[styles.courseCard, { backgroundColor: colors.surface }]}
              >
                <View style={[styles.courseThumbnail, { backgroundColor: colors.primary }]}>
                  <Icon name="play-circle-outline" size={40} color={colors.background} />
                </View>
                <Text style={[styles.courseTitle, { color: colors.text }]} numberOfLines={2}>
                  {course.title}
                </Text>
                <View style={styles.progressContainer}>
                  <View style={[styles.progressBar, { backgroundColor: colors.border }]}>
                    <View
                      style={[
                        styles.progressFill,
                        { backgroundColor: colors.primary, width: `${course.progress}%` }
                      ]}
                    />
                  </View>
                  <Text style={[styles.progressText, { color: colors.textSecondary }]}>
                    {Math.round(course.progress)}%
                  </Text>
                </View>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>
      )}

      {/* Featured Courses */}
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            {t('dashboard.featuredCourses')}
          </Text>
          <TouchableOpacity>
            <Text style={[styles.seeAll, { color: colors.primary }]}>
              See All
            </Text>
          </TouchableOpacity>
        </View>
        
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          {featuredCourses.slice(0, 5).map((course) => (
            <TouchableOpacity
              key={course.id}
              style={[styles.courseCard, { backgroundColor: colors.surface }]}
            >
              <View style={[styles.courseThumbnail, { backgroundColor: colors.secondary }]}>
                <Icon name="star" size={40} color={colors.background} />
              </View>
              <Text style={[styles.courseTitle, { color: colors.text }]} numberOfLines={2}>
                {course.title}
              </Text>
              <View style={styles.courseInfo}>
                <View style={styles.ratingContainer}>
                  <Icon name="star" size={16} color={colors.warning} />
                  <Text style={[styles.rating, { color: colors.textSecondary }]}>
                    {course.rating.toFixed(1)}
                  </Text>
                </View>
                <Text style={[styles.price, { color: colors.primary }]}>
                  ${course.price}
                </Text>
              </View>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      {/* Quick Actions */}
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>
          Quick Actions
        </Text>
        
        <View style={styles.actionsGrid}>
          <TouchableOpacity style={[styles.actionButton, { backgroundColor: colors.surface }]}>
            <Icon name="download" size={24} color={colors.primary} />
            <Text style={[styles.actionText, { color: colors.text }]}>
              Downloads
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={[styles.actionButton, { backgroundColor: colors.surface }]}>
            <Icon name="bookmark" size={24} color={colors.primary} />
            <Text style={[styles.actionText, { color: colors.text }]}>
              Bookmarks
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={[styles.actionButton, { backgroundColor: colors.surface }]}>
            <Icon name="history" size={24} color={colors.primary} />
            <Text style={[styles.actionText, { color: colors.text }]}>
              History
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={[styles.actionButton, { backgroundColor: colors.surface }]}>
            <Icon name="settings" size={24} color={colors.primary} />
            <Text style={[styles.actionText, { color: colors.text }]}>
              Settings
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 20,
  },
  greeting: {
    fontSize: 16,
    marginBottom: 4,
  },
  userName: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  notificationButton: {
    padding: 12,
    borderRadius: 12,
    position: 'relative',
  },
  notificationBadge: {
    position: 'absolute',
    top: 8,
    right: 8,
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  statsContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    marginBottom: 20,
    gap: 12,
  },
  statCard: {
    flex: 1,
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    marginTop: 8,
  },
  statLabel: {
    fontSize: 12,
    marginTop: 4,
    opacity: 0.9,
  },
  section: {
    marginBottom: 24,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  seeAll: {
    fontSize: 14,
    fontWeight: '500',
  },
  courseCard: {
    width: 160,
    padding: 12,
    borderRadius: 12,
    marginLeft: 20,
    marginRight: 4,
  },
  courseThumbnail: {
    height: 100,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  courseTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
    minHeight: 32,
  },
  progressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  progressBar: {
    flex: 1,
    height: 4,
    borderRadius: 2,
  },
  progressFill: {
    height: '100%',
    borderRadius: 2,
  },
  progressText: {
    fontSize: 12,
  },
  courseInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  rating: {
    fontSize: 12,
  },
  price: {
    fontSize: 14,
    fontWeight: '600',
  },
  actionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: 20,
    gap: 12,
  },
  actionButton: {
    flex: 1,
    minWidth: '45%',
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    gap: 8,
  },
  actionText: {
    fontSize: 14,
    fontWeight: '500',
  },
});
