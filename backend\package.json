{"name": "rjwu-backend", "version": "1.0.0", "description": "Backend for RJWU EduTech Platform", "main": "dist/server.js", "scripts": {"dev": "nodemon --exec ts-node src/server.ts", "build": "tsc", "start": "node dist/server.js", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix"}, "keywords": ["edutech", "nodejs", "express", "mongodb"], "author": "RJWU", "license": "MIT", "dependencies": {"express": "^4.18.2", "mongoose": "^7.5.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "cors": "^2.8.5", "helmet": "^7.0.0", "express-rate-limit": "^6.8.1", "joi": "^17.9.2", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.4", "socket.io": "^4.7.2", "redis": "^4.6.7", "compression": "^1.7.4", "winston": "^3.10.0", "express-validator": "^7.0.1", "cookie-parser": "^1.4.6", "express-session": "^1.17.3", "connect-mongo": "^5.0.0", "dotenv": "^16.3.1", "node-cron": "^3.0.2", "googleapis": "^126.0.1", "simple-peer": "^9.11.1", "uuid": "^9.0.0", "jimp": "^0.22.8", "ffmpeg": "^0.0.4"}, "devDependencies": {"@types/express": "^4.17.17", "@types/node": "^20.4.8", "@types/bcryptjs": "^2.4.2", "@types/jsonwebtoken": "^9.0.2", "@types/cors": "^2.8.13", "@types/multer": "^1.4.7", "@types/nodemailer": "^6.4.9", "@types/compression": "^1.7.2", "@types/cookie-parser": "^1.4.3", "@types/express-session": "^1.17.7", "@types/jest": "^29.5.3", "@types/uuid": "^9.0.2", "@typescript-eslint/eslint-plugin": "^6.2.1", "@typescript-eslint/parser": "^6.2.1", "eslint": "^8.46.0", "jest": "^29.6.2", "nodemon": "^3.0.1", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "typescript": "^5.1.6"}}