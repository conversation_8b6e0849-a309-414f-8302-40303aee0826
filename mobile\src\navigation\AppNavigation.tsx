import React from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import Icon from 'react-native-vector-icons/MaterialIcons';

import { useAppSelector } from '@/hooks/redux';
import { RootStackParamList, MainTabParamList } from '@/types';
import { THEME_COLORS } from '@/constants';

// Auth Screens
import { SplashScreen } from '@/screens/SplashScreen';
import { OnboardingScreen } from '@/screens/OnboardingScreen';
import { AuthScreen } from '@/screens/AuthScreen';

// Main Screens
import { DashboardScreen } from '@/screens/DashboardScreen';
import { CoursesScreen } from '@/screens/CoursesScreen';
import { CourseDetailScreen } from '@/screens/CourseDetailScreen';
import { VideoPlayerScreen } from '@/screens/VideoPlayerScreen';
import { DownloadsScreen } from '@/screens/DownloadsScreen';
import { ProfileScreen } from '@/screens/ProfileScreen';
import { SettingsScreen } from '@/screens/SettingsScreen';
import { NotificationsScreen } from '@/screens/NotificationsScreen';

const RootStack = createNativeStackNavigator<RootStackParamList>();
const MainTab = createBottomTabNavigator<MainTabParamList>();

const MainTabNavigator: React.FC = () => {
  const { theme } = useAppSelector(state => state.settings);
  const colors = THEME_COLORS[theme];

  return (
    <MainTab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName: string;

          switch (route.name) {
            case 'Dashboard':
              iconName = 'dashboard';
              break;
            case 'Courses':
              iconName = 'school';
              break;
            case 'Downloads':
              iconName = 'download';
              break;
            case 'Profile':
              iconName = 'person';
              break;
            default:
              iconName = 'help';
              break;
          }

          return <Icon name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: colors.primary,
        tabBarInactiveTintColor: colors.textSecondary,
        tabBarStyle: {
          backgroundColor: colors.background,
          borderTopColor: colors.border,
          paddingBottom: 5,
          paddingTop: 5,
          height: 60,
        },
        tabBarLabelStyle: {
          fontSize: 12,
          fontWeight: '600',
        },
        headerStyle: {
          backgroundColor: colors.background,
        },
        headerTintColor: colors.text,
        headerTitleStyle: {
          fontWeight: 'bold',
        },
      })}
    >
      <MainTab.Screen 
        name="Dashboard" 
        component={DashboardScreen}
        options={{ title: 'Dashboard' }}
      />
      <MainTab.Screen 
        name="Courses" 
        component={CoursesScreen}
        options={{ title: 'Courses' }}
      />
      <MainTab.Screen 
        name="Downloads" 
        component={DownloadsScreen}
        options={{ title: 'Downloads' }}
      />
      <MainTab.Screen 
        name="Profile" 
        component={ProfileScreen}
        options={{ title: 'Profile' }}
      />
    </MainTab.Navigator>
  );
};

export const AppNavigation: React.FC = () => {
  const { isAuthenticated } = useAppSelector(state => state.auth);
  const { theme } = useAppSelector(state => state.settings);
  const colors = THEME_COLORS[theme];

  return (
    <RootStack.Navigator
      screenOptions={{
        headerStyle: {
          backgroundColor: colors.background,
        },
        headerTintColor: colors.text,
        headerTitleStyle: {
          fontWeight: 'bold',
        },
        contentStyle: {
          backgroundColor: colors.background,
        },
      }}
    >
      {!isAuthenticated ? (
        // Auth Stack
        <>
          <RootStack.Screen 
            name="Splash" 
            component={SplashScreen}
            options={{ headerShown: false }}
          />
          <RootStack.Screen 
            name="Onboarding" 
            component={OnboardingScreen}
            options={{ headerShown: false }}
          />
          <RootStack.Screen 
            name="Auth" 
            component={AuthScreen}
            options={{ headerShown: false }}
          />
        </>
      ) : (
        // Main App Stack
        <>
          <RootStack.Screen 
            name="Main" 
            component={MainTabNavigator}
            options={{ headerShown: false }}
          />
          <RootStack.Screen 
            name="CourseDetail" 
            component={CourseDetailScreen}
            options={{ 
              title: 'Course Details',
              presentation: 'modal'
            }}
          />
          <RootStack.Screen 
            name="VideoPlayer" 
            component={VideoPlayerScreen}
            options={{ 
              headerShown: false,
              orientation: 'landscape',
              gestureEnabled: false,
            }}
          />
          <RootStack.Screen 
            name="Settings" 
            component={SettingsScreen}
            options={{ title: 'Settings' }}
          />
          <RootStack.Screen 
            name="Notifications" 
            component={NotificationsScreen}
            options={{ title: 'Notifications' }}
          />
        </>
      )}
    </RootStack.Navigator>
  );
};
