export interface User {
  id: string;
  email: string;
  name: string;
  avatar?: string;
  role: 'student' | 'teacher' | 'admin';
  institution?: string;
  preferences: UserPreferences;
}

export interface UserPreferences {
  language: 'en' | 'hi';
  theme: 'light' | 'dark';
  notifications: boolean;
  autoDownload: boolean;
  videoQuality: 'low' | 'medium' | 'high';
}

export interface Course {
  id: string;
  title: string;
  description: string;
  thumbnail: string;
  instructor: string;
  duration: number;
  totalLessons: number;
  completedLessons: number;
  progress: number;
  rating: number;
  price: number;
  isPurchased: boolean;
  isDownloaded: boolean;
  category: string;
  level: 'beginner' | 'intermediate' | 'advanced';
  tags: string[];
  lessons: Lesson[];
}

export interface Lesson {
  id: string;
  title: string;
  description: string;
  videoUrl: string;
  duration: number;
  isCompleted: boolean;
  isDownloaded: boolean;
  downloadProgress?: number;
  thumbnailUrl: string;
  resources: Resource[];
  quiz?: Quiz;
}

export interface Resource {
  id: string;
  title: string;
  type: 'pdf' | 'image' | 'document';
  url: string;
  isDownloaded: boolean;
}

export interface Quiz {
  id: string;
  title: string;
  questions: Question[];
  timeLimit: number;
  passingScore: number;
}

export interface Question {
  id: string;
  text: string;
  type: 'multiple-choice' | 'true-false' | 'text';
  options?: string[];
  correctAnswer: string | string[];
  explanation?: string;
}

export interface Notification {
  id: string;
  title: string;
  message: string;
  type: 'info' | 'success' | 'warning' | 'error';
  timestamp: Date;
  isRead: boolean;
  actionUrl?: string;
}

export interface DownloadItem {
  id: string;
  type: 'video' | 'resource';
  title: string;
  url: string;
  filePath: string;
  progress: number;
  status: 'pending' | 'downloading' | 'completed' | 'error' | 'paused';
  size: number;
  downloadedSize: number;
  createdAt: Date;
}

export interface SecurityConfig {
  isRootDetectionEnabled: boolean;
  isScreenshotBlocked: boolean;
  isScreenRecordingBlocked: boolean;
  watermarkText: string;
  sessionTimeout: number;
  maxDevices: number;
}

export interface AppState {
  isLoading: boolean;
  isAuthenticated: boolean;
  user: User | null;
  courses: Course[];
  downloads: DownloadItem[];
  notifications: Notification[];
  networkStatus: {
    isConnected: boolean;
    type: string;
  };
  security: SecurityConfig;
  theme: 'light' | 'dark';
  language: 'en' | 'hi';
}

export type RootStackParamList = {
  Splash: undefined;
  Onboarding: undefined;
  Auth: undefined;
  Main: undefined;
  CourseDetail: { courseId: string };
  VideoPlayer: { lessonId: string; courseId: string };
  Profile: undefined;
  Settings: undefined;
  Downloads: undefined;
  Notifications: undefined;
};

export type MainTabParamList = {
  Dashboard: undefined;
  Courses: undefined;
  Downloads: undefined;
  Profile: undefined;
};

export interface BiometricConfig {
  isAvailable: boolean;
  biometryType: string;
  isEnabled: boolean;
}
