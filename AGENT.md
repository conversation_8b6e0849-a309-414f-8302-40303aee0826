# RJWU EduTech Platform - Agent Instructions

## Development Commands

### Full Platform
- `npm run install:all` - Install dependencies for all services
- `npm run dev` - Start all development servers concurrently
- `npm run build` - Build all services for production
- `npm test` - Run all test suites

### Backend (Node.js/Express)
- `cd backend && npm run dev` - Start backend development server
- `cd backend && npm run build` - Build backend for production
- `cd backend && npm test` - Run backend tests
- `cd backend && npm run lint` - Lint backend code

### Frontend (React.js)
- `cd frontend && npm run dev` - Start frontend development server
- `cd frontend && npm run build` - Build frontend for production
- `cd frontend && npm test` - Run frontend tests

### Admin Panel
- `cd admin && npm run dev` - Start admin panel development
- `cd admin && npm run build` - Build admin panel

### Mobile (React Native)
- `cd mobile && npm run android` - Run Android app
- `cd mobile && npm run ios` - Run iOS app
- `cd mobile && npm run build:android` - Build Android APK
- `cd mobile && npm run build:ios` - Build iOS app

## Code Style Preferences

### General
- Use TypeScript for all new code
- Follow ESLint and Prettier configurations
- Use functional components with hooks in React
- Prefer async/await over Promise chains
- Use descriptive variable and function names

### File Organization
- Group related files in feature-based folders
- Keep components small and focused
- Separate business logic from UI components
- Use barrel exports (index.ts) for clean imports

### Database
- Use Mongoose for MongoDB operations
- Define clear schema interfaces
- Implement proper validation
- Use transactions for complex operations

### Security
- Never commit secrets or API keys
- Use environment variables for configuration
- Implement proper input validation
- Follow OWASP security guidelines

### API Design
- Use RESTful conventions
- Implement proper error handling
- Use consistent response formats
- Document endpoints with OpenAPI/Swagger

## Project Structure

```
RJWU/
├── backend/
│   ├── src/
│   │   ├── controllers/
│   │   ├── models/
│   │   ├── routes/
│   │   ├── middleware/
│   │   ├── services/
│   │   └── utils/
│   ├── tests/
│   └── package.json
├── frontend/
│   ├── src/
│   │   ├── components/
│   │   ├── pages/
│   │   ├── hooks/
│   │   ├── services/
│   │   ├── utils/
│   │   └── types/
│   └── package.json
├── admin/
├── mobile/
├── desktop/
└── shared/
```

## Testing Strategy

- Unit tests for business logic
- Integration tests for API endpoints
- Component tests for React components
- E2E tests for critical user flows
- Use Jest for JavaScript/TypeScript testing
- Use React Testing Library for component tests

## Deployment

- Use Docker for containerization
- Environment-specific configuration files
- CI/CD pipeline with GitHub Actions
- Staging and production environments
- Database migrations and seeding

## Key Features to Remember

1. **Multi-tenant Architecture**: Support for multiple educational institutions
2. **Multilingual Support**: Hindi and English with RTL support
3. **Role-based Access Control**: Students, Teachers, Admins with granular permissions
4. **Content Security**: Watermarking, DRM, session management
5. **Real-time Features**: Live classes, chat, notifications
6. **Mobile-first Design**: Responsive and PWA-ready
7. **Offline Capability**: Downloaded content for mobile apps
8. **Payment Integration**: Multiple gateways, EMI support
9. **Analytics**: Comprehensive tracking and reporting
10. **Scalability**: Microservices-ready architecture
