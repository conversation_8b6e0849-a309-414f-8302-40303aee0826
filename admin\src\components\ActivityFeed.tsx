import React from 'react'
import { format } from 'date-fns'
import {
  UserPlusIcon,
  CurrencyRupeeIcon,
  DocumentCheckIcon,
  ExclamationTriangleIcon,
} from '@heroicons/react/24/outline'

const activities = [
  {
    id: 1,
    type: 'user_registration',
    title: 'New student registered',
    description: '<PERSON><PERSON> joined Batch-2024-CS',
    timestamp: new Date(Date.now() - 5 * 60 * 1000),
    icon: UserPlusIcon,
    iconColor: 'text-green-600',
    iconBg: 'bg-green-100',
  },
  {
    id: 2,
    type: 'payment',
    title: 'Payment received',
    description: '₹15,000 payment for Premium Course',
    timestamp: new Date(Date.now() - 15 * 60 * 1000),
    icon: CurrencyRupeeIcon,
    iconColor: 'text-blue-600',
    iconBg: 'bg-blue-100',
  },
  {
    id: 3,
    type: 'content_approval',
    title: 'Content approved',
    description: 'Mathematics Chapter 5 video approved',
    timestamp: new Date(Date.now() - 30 * 60 * 1000),
    icon: DocumentCheckIcon,
    iconColor: 'text-purple-600',
    iconBg: 'bg-purple-100',
  },
  {
    id: 4,
    type: 'system_alert',
    title: 'System alert',
    description: 'Server CPU usage above 85%',
    timestamp: new Date(Date.now() - 45 * 60 * 1000),
    icon: ExclamationTriangleIcon,
    iconColor: 'text-red-600',
    iconBg: 'bg-red-100',
  },
  {
    id: 5,
    type: 'user_registration',
    title: 'Bulk registration',
    description: '15 students added to Batch-2024-PCM',
    timestamp: new Date(Date.now() - 60 * 60 * 1000),
    icon: UserPlusIcon,
    iconColor: 'text-green-600',
    iconBg: 'bg-green-100',
  },
]

export const ActivityFeed: React.FC = () => {
  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
      <div className="px-6 py-4 border-b border-gray-200">
        <h3 className="text-lg font-medium text-gray-900">Recent Activity</h3>
      </div>
      <div className="flow-root">
        <ul className="divide-y divide-gray-200">
          {activities.map((activity) => (
            <li key={activity.id} className="px-6 py-4">
              <div className="relative flex space-x-3">
                <div>
                  <span className={`h-8 w-8 rounded-full flex items-center justify-center ring-8 ring-white ${activity.iconBg}`}>
                    <activity.icon className={`h-5 w-5 ${activity.iconColor}`} />
                  </span>
                </div>
                <div className="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                  <div>
                    <p className="text-sm font-medium text-gray-900">{activity.title}</p>
                    <p className="text-sm text-gray-500">{activity.description}</p>
                  </div>
                  <div className="text-right text-sm whitespace-nowrap text-gray-500">
                    <time dateTime={activity.timestamp.toISOString()}>
                      {format(activity.timestamp, 'MMM d, HH:mm')}
                    </time>
                  </div>
                </div>
              </div>
            </li>
          ))}
        </ul>
      </div>
      <div className="px-6 py-3 border-t border-gray-200">
        <a href="#" className="text-sm font-medium text-primary-600 hover:text-primary-500">
          View all activity →
        </a>
      </div>
    </div>
  )
}
