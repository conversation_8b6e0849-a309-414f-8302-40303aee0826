import React from 'react'
import { Dialog, Transition } from '@headlessui/react'
import { useForm } from 'react-hook-form'
import { XMarkIcon } from '@heroicons/react/24/outline'
import { LoadingSpinner } from '../LoadingSpinner'

interface CourseModalProps {
  open: boolean
  onClose: () => void
  course?: any
}

interface CourseForm {
  title: string
  description: string
  category: string
  level: string
  duration: string
  price: number
  thumbnail: string
  objectives: string
  prerequisites: string
  status: string
  language: string
  tags: string
}

export const CourseModal: React.FC<CourseModalProps> = ({ open, onClose, course }) => {
  const isEditing = !!course
  const [loading, setLoading] = React.useState(false)

  const { register, handleSubmit, formState: { errors }, reset } = useForm<CourseForm>({
    defaultValues: course ? {
      title: course.title,
      description: course.description,
      category: course.category,
      level: course.level,
      duration: course.duration,
      price: parseInt(course.price?.replace(/[₹,]/g, '') || '0'),
      thumbnail: course.thumbnail,
      objectives: course.objectives,
      prerequisites: course.prerequisites,
      status: course.status,
      language: course.language || 'english',
      tags: course.tags?.join(', ') || '',
    } : {
      level: 'beginner',
      status: 'draft',
      language: 'english',
    }
  })

  React.useEffect(() => {
    if (course) {
      reset({
        title: course.title,
        description: course.description,
        category: course.category,
        level: course.level,
        duration: course.duration,
        price: parseInt(course.price?.replace(/[₹,]/g, '') || '0'),
        thumbnail: course.thumbnail,
        objectives: course.objectives,
        prerequisites: course.prerequisites,
        status: course.status,
        language: course.language || 'english',
        tags: course.tags?.join(', ') || '',
      })
    } else {
      reset({
        level: 'beginner',
        status: 'draft',
        language: 'english',
      })
    }
  }, [course, reset])

  const onSubmit = async (data: CourseForm) => {
    setLoading(true)
    try {
      // API call to create/update course
      console.log('Course data:', data)
      await new Promise(resolve => setTimeout(resolve, 1000)) // Mock API call
      onClose()
    } catch (error) {
      console.error('Error saving course:', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <Transition.Root show={open} as={React.Fragment}>
      <Dialog as="div" className="relative z-50" onClose={onClose}>
        <Transition.Child
          as={React.Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
        </Transition.Child>

        <div className="fixed inset-0 z-10 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <Transition.Child
              as={React.Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
              enterTo="opacity-100 translate-y-0 sm:scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 translate-y-0 sm:scale-100"
              leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            >
              <Dialog.Panel className="relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-3xl sm:p-6">
                <div className="absolute right-0 top-0 hidden pr-4 pt-4 sm:block">
                  <button
                    type="button"
                    className="rounded-md bg-white text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
                    onClick={onClose}
                  >
                    <XMarkIcon className="h-6 w-6" />
                  </button>
                </div>

                <div className="sm:flex sm:items-start">
                  <div className="mt-3 text-center sm:ml-4 sm:mt-0 sm:text-left w-full">
                    <Dialog.Title as="h3" className="text-base font-semibold leading-6 text-gray-900">
                      {isEditing ? 'Edit Course' : 'Create New Course'}
                    </Dialog.Title>

                    <form onSubmit={handleSubmit(onSubmit)} className="mt-6 space-y-6">
                      {/* Basic Information */}
                      <div className="space-y-4">
                        <h4 className="text-sm font-medium text-gray-900">Basic Information</h4>
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                          <div className="lg:col-span-2">
                            <label htmlFor="title" className="block text-sm font-medium text-gray-700">
                              Course Title
                            </label>
                            <input
                              {...register('title', { required: 'Course title is required' })}
                              type="text"
                              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                              placeholder="e.g., Complete JEE Mathematics"
                            />
                            {errors.title && (
                              <p className="mt-1 text-sm text-red-600">{errors.title.message}</p>
                            )}
                          </div>

                          <div>
                            <label htmlFor="category" className="block text-sm font-medium text-gray-700">
                              Category
                            </label>
                            <select
                              {...register('category', { required: 'Category is required' })}
                              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                            >
                              <option value="">Select Category</option>
                              <option value="JEE Preparation">JEE Preparation</option>
                              <option value="NEET Preparation">NEET Preparation</option>
                              <option value="Foundation">Foundation</option>
                              <option value="Board Exam">Board Exam</option>
                              <option value="Competitive Exam">Competitive Exam</option>
                            </select>
                            {errors.category && (
                              <p className="mt-1 text-sm text-red-600">{errors.category.message}</p>
                            )}
                          </div>

                          <div>
                            <label htmlFor="level" className="block text-sm font-medium text-gray-700">
                              Difficulty Level
                            </label>
                            <select
                              {...register('level', { required: 'Level is required' })}
                              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                            >
                              <option value="beginner">Beginner</option>
                              <option value="intermediate">Intermediate</option>
                              <option value="advanced">Advanced</option>
                            </select>
                          </div>

                          <div>
                            <label htmlFor="duration" className="block text-sm font-medium text-gray-700">
                              Duration
                            </label>
                            <input
                              {...register('duration', { required: 'Duration is required' })}
                              type="text"
                              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                              placeholder="e.g., 6 months"
                            />
                            {errors.duration && (
                              <p className="mt-1 text-sm text-red-600">{errors.duration.message}</p>
                            )}
                          </div>

                          <div>
                            <label htmlFor="price" className="block text-sm font-medium text-gray-700">
                              Price (₹)
                            </label>
                            <input
                              {...register('price', { 
                                required: 'Price is required',
                                min: { value: 0, message: 'Price cannot be negative' }
                              })}
                              type="number"
                              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                            />
                            {errors.price && (
                              <p className="mt-1 text-sm text-red-600">{errors.price.message}</p>
                            )}
                          </div>

                          <div>
                            <label htmlFor="language" className="block text-sm font-medium text-gray-700">
                              Language
                            </label>
                            <select
                              {...register('language', { required: 'Language is required' })}
                              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                            >
                              <option value="english">English</option>
                              <option value="hindi">Hindi</option>
                              <option value="both">Both (English & Hindi)</option>
                            </select>
                          </div>

                          <div>
                            <label htmlFor="status" className="block text-sm font-medium text-gray-700">
                              Status
                            </label>
                            <select
                              {...register('status', { required: 'Status is required' })}
                              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                            >
                              <option value="draft">Draft</option>
                              <option value="active">Active</option>
                              <option value="archived">Archived</option>
                            </select>
                          </div>

                          <div className="lg:col-span-2">
                            <label htmlFor="description" className="block text-sm font-medium text-gray-700">
                              Course Description
                            </label>
                            <textarea
                              {...register('description', { required: 'Description is required' })}
                              rows={4}
                              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                              placeholder="Describe what students will learn in this course..."
                            />
                            {errors.description && (
                              <p className="mt-1 text-sm text-red-600">{errors.description.message}</p>
                            )}
                          </div>
                        </div>
                      </div>

                      {/* Additional Details */}
                      <div className="space-y-4">
                        <h4 className="text-sm font-medium text-gray-900">Additional Details</h4>
                        <div className="grid grid-cols-1 gap-4">
                          <div>
                            <label htmlFor="objectives" className="block text-sm font-medium text-gray-700">
                              Learning Objectives
                            </label>
                            <textarea
                              {...register('objectives')}
                              rows={3}
                              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                              placeholder="What will students achieve after completing this course?"
                            />
                          </div>

                          <div>
                            <label htmlFor="prerequisites" className="block text-sm font-medium text-gray-700">
                              Prerequisites
                            </label>
                            <textarea
                              {...register('prerequisites')}
                              rows={2}
                              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                              placeholder="What should students know before taking this course?"
                            />
                          </div>

                          <div>
                            <label htmlFor="tags" className="block text-sm font-medium text-gray-700">
                              Tags (comma separated)
                            </label>
                            <input
                              {...register('tags')}
                              type="text"
                              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                              placeholder="mathematics, algebra, calculus, jee"
                            />
                          </div>

                          <div>
                            <label htmlFor="thumbnail" className="block text-sm font-medium text-gray-700">
                              Thumbnail URL
                            </label>
                            <input
                              {...register('thumbnail')}
                              type="text"
                              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                              placeholder="https://example.com/thumbnail.jpg"
                            />
                          </div>
                        </div>
                      </div>

                      <div className="mt-5 sm:mt-4 sm:flex sm:flex-row-reverse">
                        <button
                          type="submit"
                          disabled={loading}
                          className="inline-flex w-full justify-center rounded-md bg-primary-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-primary-500 sm:ml-3 sm:w-auto disabled:opacity-50"
                        >
                          {loading ? (
                            <LoadingSpinner size="sm" />
                          ) : (
                            isEditing ? 'Update Course' : 'Create Course'
                          )}
                        </button>
                        <button
                          type="button"
                          className="mt-3 inline-flex w-full justify-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 sm:mt-0 sm:w-auto"
                          onClick={onClose}
                        >
                          Cancel
                        </button>
                      </div>
                    </form>
                  </div>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition.Root>
  )
}
