import { Schema, model, Document, Types } from 'mongoose';

export interface ILiveClassRecording extends Document {
  _id: Types.ObjectId;
  liveClass: Types.ObjectId;
  title: string;
  description?: string;
  recordingUrl: string;
  thumbnailUrl?: string;
  duration: number; // in seconds
  fileSize: number; // in bytes
  format: string;
  quality: 'low' | 'medium' | 'high' | 'hd';
  status: 'processing' | 'ready' | 'failed' | 'archived';
  segments: [{
    startTime: number; // seconds from start
    endTime: number;
    title: string;
    type: 'content' | 'poll' | 'break' | 'q&a';
  }];
  watermark: {
    enabled: boolean;
    text?: string;
    position: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right' | 'center';
    opacity: number;
  };
  security: {
    encrypted: boolean;
    drm: boolean;
    accessControl: 'public' | 'enrolled' | 'restricted';
    allowedUsers: Types.ObjectId[];
    expiresAt?: Date;
  };
  analytics: {
    totalViews: number;
    uniqueViewers: number;
    averageViewDuration: number;
    completionRate: number;
    engagementScore: number;
  };
  processing: {
    startedAt?: Date;
    completedAt?: Date;
    failedAt?: Date;
    error?: string;
    progress: number;
  };
  metadata: {
    chatIncluded: boolean;
    pollsIncluded: boolean;
    screenshareIncluded: boolean;
    originalStreamKey?: string;
  };
  createdAt: Date;
  updatedAt: Date;
}

const LiveClassRecordingSchema = new Schema<ILiveClassRecording>({
  liveClass: {
    type: Schema.Types.ObjectId,
    ref: 'LiveClass',
    required: true
  },
  title: {
    type: String,
    required: true,
    maxlength: 200
  },
  description: {
    type: String,
    maxlength: 1000
  },
  recordingUrl: {
    type: String,
    required: true
  },
  thumbnailUrl: String,
  duration: {
    type: Number,
    required: true,
    min: 0
  },
  fileSize: {
    type: Number,
    required: true,
    min: 0
  },
  format: {
    type: String,
    required: true,
    enum: ['mp4', 'webm', 'mov', 'avi']
  },
  quality: {
    type: String,
    enum: ['low', 'medium', 'high', 'hd'],
    default: 'medium'
  },
  status: {
    type: String,
    enum: ['processing', 'ready', 'failed', 'archived'],
    default: 'processing'
  },
  segments: [{
    startTime: {
      type: Number,
      required: true
    },
    endTime: {
      type: Number,
      required: true
    },
    title: {
      type: String,
      required: true
    },
    type: {
      type: String,
      enum: ['content', 'poll', 'break', 'q&a'],
      default: 'content'
    }
  }],
  watermark: {
    enabled: {
      type: Boolean,
      default: true
    },
    text: String,
    position: {
      type: String,
      enum: ['top-left', 'top-right', 'bottom-left', 'bottom-right', 'center'],
      default: 'bottom-right'
    },
    opacity: {
      type: Number,
      default: 0.7,
      min: 0,
      max: 1
    }
  },
  security: {
    encrypted: {
      type: Boolean,
      default: true
    },
    drm: {
      type: Boolean,
      default: true
    },
    accessControl: {
      type: String,
      enum: ['public', 'enrolled', 'restricted'],
      default: 'enrolled'
    },
    allowedUsers: [{
      type: Schema.Types.ObjectId,
      ref: 'User'
    }],
    expiresAt: Date
  },
  analytics: {
    totalViews: {
      type: Number,
      default: 0
    },
    uniqueViewers: {
      type: Number,
      default: 0
    },
    averageViewDuration: {
      type: Number,
      default: 0
    },
    completionRate: {
      type: Number,
      default: 0
    },
    engagementScore: {
      type: Number,
      default: 0
    }
  },
  processing: {
    startedAt: Date,
    completedAt: Date,
    failedAt: Date,
    error: String,
    progress: {
      type: Number,
      default: 0,
      min: 0,
      max: 100
    }
  },
  metadata: {
    chatIncluded: {
      type: Boolean,
      default: true
    },
    pollsIncluded: {
      type: Boolean,
      default: true
    },
    screenshareIncluded: {
      type: Boolean,
      default: true
    },
    originalStreamKey: String
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for performance
LiveClassRecordingSchema.index({ liveClass: 1 });
LiveClassRecordingSchema.index({ status: 1, createdAt: -1 });
LiveClassRecordingSchema.index({ 'security.accessControl': 1, 'security.allowedUsers': 1 });

// Virtual for human-readable duration
LiveClassRecordingSchema.virtual('formattedDuration').get(function() {
  const hours = Math.floor(this.duration / 3600);
  const minutes = Math.floor((this.duration % 3600) / 60);
  const seconds = Math.floor(this.duration % 60);
  
  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  }
  return `${minutes}:${seconds.toString().padStart(2, '0')}`;
});

export default model<ILiveClassRecording>('LiveClassRecording', LiveClassRecordingSchema);
