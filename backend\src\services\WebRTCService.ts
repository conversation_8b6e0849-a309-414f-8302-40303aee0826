import { Server as SocketIOServer } from 'socket.io';
import { v4 as uuidv4 } from 'uuid';

interface Room {
  id: string;
  participants: Map<string, Participant>;
  moderators: Set<string>;
  settings: RoomSettings;
  createdAt: Date;
  lastActivity: Date;
}

interface Participant {
  id: string;
  userId: string;
  socketId: string;
  name: string;
  role: 'instructor' | 'student' | 'moderator';
  permissions: ParticipantPermissions;
  joinedAt: Date;
  isPresenting: boolean;
  connectionQuality: 'poor' | 'fair' | 'good' | 'excellent';
}

interface ParticipantPermissions {
  canSpeak: boolean;
  canVideo: boolean;
  canScreenShare: boolean;
  canChat: boolean;
  canModerate: boolean;
}

interface RoomSettings {
  maxParticipants: number;
  allowScreenShare: boolean;
  moderatorApproval: boolean;
  recordingEnabled: boolean;
  chatEnabled: boolean;
}

export class WebRTCService {
  private rooms: Map<string, Room> = new Map();
  private io: SocketIOServer | null = null;

  initialize(io: SocketIOServer): void {
    this.io = io;
    this.setupSocketHandlers();
    this.startCleanupTask();
  }

  createRoom(roomId: string, settings: Partial<RoomSettings> = {}): Room {
    const room: Room = {
      id: roomId,
      participants: new Map(),
      moderators: new Set(),
      settings: {
        maxParticipants: 500,
        allowScreenShare: true,
        moderatorApproval: false,
        recordingEnabled: true,
        chatEnabled: true,
        ...settings
      },
      createdAt: new Date(),
      lastActivity: new Date()
    };

    this.rooms.set(roomId, room);
    return room;
  }

  joinRoom(roomId: string, participant: Omit<Participant, 'id' | 'joinedAt' | 'isPresenting' | 'connectionQuality'>): Participant | null {
    const room = this.rooms.get(roomId);
    if (!room) return null;

    if (room.participants.size >= room.settings.maxParticipants) {
      throw new Error('Room is full');
    }

    const participantData: Participant = {
      id: uuidv4(),
      ...participant,
      joinedAt: new Date(),
      isPresenting: false,
      connectionQuality: 'good'
    };

    room.participants.set(participantData.id, participantData);
    room.lastActivity = new Date();

    // If first participant or instructor, make them a moderator
    if (room.participants.size === 1 || participant.role === 'instructor') {
      room.moderators.add(participantData.id);
      participantData.permissions.canModerate = true;
    }

    this.broadcastToRoom(roomId, 'participant-joined', {
      participant: this.sanitizeParticipant(participantData),
      participantCount: room.participants.size
    });

    return participantData;
  }

  leaveRoom(roomId: string, participantId: string): void {
    const room = this.rooms.get(roomId);
    if (!room) return;

    const participant = room.participants.get(participantId);
    if (!participant) return;

    // Stop presenting if they were presenting
    if (participant.isPresenting) {
      this.stopPresenting(roomId, participantId);
    }

    room.participants.delete(participantId);
    room.moderators.delete(participantId);
    room.lastActivity = new Date();

    this.broadcastToRoom(roomId, 'participant-left', {
      participantId,
      participantCount: room.participants.size
    });

    // Remove room if empty
    if (room.participants.size === 0) {
      this.rooms.delete(roomId);
    }
  }

  updateParticipantPermissions(roomId: string, participantId: string, permissions: Partial<ParticipantPermissions>, moderatorId: string): boolean {
    const room = this.rooms.get(roomId);
    if (!room) return false;

    const moderator = room.participants.get(moderatorId);
    if (!moderator || !moderator.permissions.canModerate) return false;

    const participant = room.participants.get(participantId);
    if (!participant) return false;

    participant.permissions = { ...participant.permissions, ...permissions };
    room.lastActivity = new Date();

    this.broadcastToRoom(roomId, 'participant-permissions-updated', {
      participantId,
      permissions: participant.permissions
    });

    return true;
  }

  startPresenting(roomId: string, participantId: string): boolean {
    const room = this.rooms.get(roomId);
    if (!room) return false;

    const participant = room.participants.get(participantId);
    if (!participant || !participant.permissions.canScreenShare) return false;

    // Stop any other presenter
    for (const [id, p] of room.participants) {
      if (p.isPresenting) {
        p.isPresenting = false;
        this.broadcastToRoom(roomId, 'presentation-stopped', { participantId: id });
      }
    }

    participant.isPresenting = true;
    room.lastActivity = new Date();

    this.broadcastToRoom(roomId, 'presentation-started', { participantId });

    return true;
  }

  stopPresenting(roomId: string, participantId: string): boolean {
    const room = this.rooms.get(roomId);
    if (!room) return false;

    const participant = room.participants.get(participantId);
    if (!participant || !participant.isPresenting) return false;

    participant.isPresenting = false;
    room.lastActivity = new Date();

    this.broadcastToRoom(roomId, 'presentation-stopped', { participantId });

    return true;
  }

  muteParticipant(roomId: string, participantId: string, moderatorId: string): boolean {
    return this.updateParticipantPermissions(roomId, participantId, { canSpeak: false }, moderatorId);
  }

  unmuteParticipant(roomId: string, participantId: string, moderatorId: string): boolean {
    return this.updateParticipantPermissions(roomId, participantId, { canSpeak: true }, moderatorId);
  }

  removeParticipant(roomId: string, participantId: string, moderatorId: string): boolean {
    const room = this.rooms.get(roomId);
    if (!room) return false;

    const moderator = room.participants.get(moderatorId);
    if (!moderator || !moderator.permissions.canModerate) return false;

    const participant = room.participants.get(participantId);
    if (!participant) return false;

    // Notify the participant they're being removed
    const socket = this.findSocketByParticipantId(participantId);
    if (socket) {
      socket.emit('removed-from-room', { roomId, reason: 'Removed by moderator' });
      socket.leave(roomId);
    }

    this.leaveRoom(roomId, participantId);
    return true;
  }

  getRoomInfo(roomId: string): Room | null {
    const room = this.rooms.get(roomId);
    if (!room) return null;

    return {
      ...room,
      participants: new Map(
        Array.from(room.participants.entries()).map(([id, p]) => [id, this.sanitizeParticipant(p)])
      )
    };
  }

  getParticipants(roomId: string): Participant[] {
    const room = this.rooms.get(roomId);
    if (!room) return [];

    return Array.from(room.participants.values()).map(p => this.sanitizeParticipant(p));
  }

  private setupSocketHandlers(): void {
    if (!this.io) return;

    this.io.on('connection', (socket) => {
      socket.on('join-room', (data) => {
        const { roomId, participant } = data;
        
        try {
          const participantData = this.joinRoom(roomId, {
            ...participant,
            socketId: socket.id
          });

          if (participantData) {
            socket.join(roomId);
            socket.emit('joined-room', {
              roomId,
              participant: this.sanitizeParticipant(participantData),
              participants: this.getParticipants(roomId)
            });
          }
        } catch (error) {
          socket.emit('join-error', { message: error.message });
        }
      });

      socket.on('leave-room', (data) => {
        const { roomId, participantId } = data;
        this.leaveRoom(roomId, participantId);
        socket.leave(roomId);
      });

      socket.on('webrtc-signal', (data) => {
        const { roomId, to, signal } = data;
        socket.to(to).emit('webrtc-signal', {
          from: socket.id,
          signal
        });
      });

      socket.on('start-presenting', (data) => {
        const { roomId, participantId } = data;
        this.startPresenting(roomId, participantId);
      });

      socket.on('stop-presenting', (data) => {
        const { roomId, participantId } = data;
        this.stopPresenting(roomId, participantId);
      });

      socket.on('mute-participant', (data) => {
        const { roomId, participantId, moderatorId } = data;
        this.muteParticipant(roomId, participantId, moderatorId);
      });

      socket.on('unmute-participant', (data) => {
        const { roomId, participantId, moderatorId } = data;
        this.unmuteParticipant(roomId, participantId, moderatorId);
      });

      socket.on('remove-participant', (data) => {
        const { roomId, participantId, moderatorId } = data;
        this.removeParticipant(roomId, participantId, moderatorId);
      });

      socket.on('update-connection-quality', (data) => {
        const { roomId, participantId, quality } = data;
        this.updateConnectionQuality(roomId, participantId, quality);
      });

      socket.on('disconnect', () => {
        this.handleDisconnect(socket.id);
      });
    });
  }

  private broadcastToRoom(roomId: string, event: string, data: any): void {
    if (!this.io) return;
    this.io.to(roomId).emit(event, data);
  }

  private findSocketByParticipantId(participantId: string): any {
    if (!this.io) return null;

    for (const room of this.rooms.values()) {
      const participant = room.participants.get(participantId);
      if (participant) {
        return this.io.sockets.sockets.get(participant.socketId);
      }
    }
    return null;
  }

  private sanitizeParticipant(participant: Participant): any {
    return {
      id: participant.id,
      userId: participant.userId,
      name: participant.name,
      role: participant.role,
      permissions: participant.permissions,
      joinedAt: participant.joinedAt,
      isPresenting: participant.isPresenting,
      connectionQuality: participant.connectionQuality
    };
  }

  private updateConnectionQuality(roomId: string, participantId: string, quality: Participant['connectionQuality']): void {
    const room = this.rooms.get(roomId);
    if (!room) return;

    const participant = room.participants.get(participantId);
    if (!participant) return;

    participant.connectionQuality = quality;
    room.lastActivity = new Date();

    this.broadcastToRoom(roomId, 'connection-quality-updated', {
      participantId,
      quality
    });
  }

  private handleDisconnect(socketId: string): void {
    for (const [roomId, room] of this.rooms) {
      for (const [participantId, participant] of room.participants) {
        if (participant.socketId === socketId) {
          this.leaveRoom(roomId, participantId);
          break;
        }
      }
    }
  }

  private startCleanupTask(): void {
    // Clean up empty rooms every 5 minutes
    setInterval(() => {
      const now = new Date();
      const threshold = 30 * 60 * 1000; // 30 minutes

      for (const [roomId, room] of this.rooms) {
        if (room.participants.size === 0 && 
            (now.getTime() - room.lastActivity.getTime()) > threshold) {
          this.rooms.delete(roomId);
        }
      }
    }, 5 * 60 * 1000);
  }
}

export default new WebRTCService();
