import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  Alert,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useTheme } from '@/context/ThemeContext';
import { useI18n } from '@/context/I18nContext';
import { useAppDispatch, useAppSelector } from '@/hooks/redux';
import { login, register, loginWithBiometric, verifyBiometric } from '@/store/slices/authSlice';

export const AuthScreen: React.FC = () => {
  const { colors } = useTheme();
  const { t } = useI18n();
  const dispatch = useAppDispatch();
  const { isLoading, error, biometric } = useAppSelector(state => state.auth);

  const [isLoginMode, setIsLoginMode] = useState(true);
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [name, setName] = useState('');
  const [showPassword, setShowPassword] = useState(false);

  useEffect(() => {
    dispatch(verifyBiometric());
  }, [dispatch]);

  const handleAuth = async () => {
    if (!email || !password) {
      Alert.alert(t('common.error'), 'Please fill all required fields');
      return;
    }

    if (!isLoginMode && password !== confirmPassword) {
      Alert.alert(t('common.error'), 'Passwords do not match');
      return;
    }

    try {
      if (isLoginMode) {
        await dispatch(login({ email, password })).unwrap();
      } else {
        await dispatch(register({ email, password, name, role: 'student' })).unwrap();
      }
    } catch (err) {
      Alert.alert(t('common.error'), err as string);
    }
  };

  const handleBiometricAuth = async () => {
    try {
      await dispatch(loginWithBiometric()).unwrap();
    } catch (err) {
      Alert.alert(t('common.error'), err as string);
    }
  };

  return (
    <KeyboardAvoidingView
      style={[styles.container, { backgroundColor: colors.background }]}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView contentContainerStyle={styles.scrollContent} showsVerticalScrollIndicator={false}>
        <View style={styles.header}>
          <Text style={[styles.title, { color: colors.text }]}>
            {isLoginMode ? t('auth.login') : t('auth.register')}
          </Text>
          <Text style={[styles.subtitle, { color: colors.textSecondary }]}>
            {isLoginMode 
              ? 'Welcome back to RJWU EduTech'
              : 'Create your RJWU EduTech account'
            }
          </Text>
        </View>

        <View style={styles.form}>
          {!isLoginMode && (
            <View style={[styles.inputContainer, { borderColor: colors.border }]}>
              <Icon name="person" size={20} color={colors.textSecondary} />
              <TextInput
                style={[styles.input, { color: colors.text }]}
                placeholder={t('auth.name')}
                placeholderTextColor={colors.textSecondary}
                value={name}
                onChangeText={setName}
                autoCapitalize="words"
              />
            </View>
          )}

          <View style={[styles.inputContainer, { borderColor: colors.border }]}>
            <Icon name="email" size={20} color={colors.textSecondary} />
            <TextInput
              style={[styles.input, { color: colors.text }]}
              placeholder={t('auth.email')}
              placeholderTextColor={colors.textSecondary}
              value={email}
              onChangeText={setEmail}
              keyboardType="email-address"
              autoCapitalize="none"
            />
          </View>

          <View style={[styles.inputContainer, { borderColor: colors.border }]}>
            <Icon name="lock" size={20} color={colors.textSecondary} />
            <TextInput
              style={[styles.input, { color: colors.text }]}
              placeholder={t('auth.password')}
              placeholderTextColor={colors.textSecondary}
              value={password}
              onChangeText={setPassword}
              secureTextEntry={!showPassword}
            />
            <TouchableOpacity onPress={() => setShowPassword(!showPassword)}>
              <Icon
                name={showPassword ? 'visibility' : 'visibility-off'}
                size={20}
                color={colors.textSecondary}
              />
            </TouchableOpacity>
          </View>

          {!isLoginMode && (
            <View style={[styles.inputContainer, { borderColor: colors.border }]}>
              <Icon name="lock" size={20} color={colors.textSecondary} />
              <TextInput
                style={[styles.input, { color: colors.text }]}
                placeholder={t('auth.confirmPassword')}
                placeholderTextColor={colors.textSecondary}
                value={confirmPassword}
                onChangeText={setConfirmPassword}
                secureTextEntry={!showPassword}
              />
            </View>
          )}

          {isLoginMode && (
            <TouchableOpacity style={styles.forgotPassword}>
              <Text style={[styles.forgotPasswordText, { color: colors.primary }]}>
                {t('auth.forgotPassword')}
              </Text>
            </TouchableOpacity>
          )}

          <TouchableOpacity
            style={[styles.authButton, { backgroundColor: colors.primary }]}
            onPress={handleAuth}
            disabled={isLoading}
          >
            <Text style={[styles.authButtonText, { color: colors.background }]}>
              {isLoading 
                ? t('common.loading')
                : isLoginMode 
                  ? t('auth.login') 
                  : t('auth.register')
              }
            </Text>
          </TouchableOpacity>

          {isLoginMode && biometric.isAvailable && (
            <TouchableOpacity
              style={[styles.biometricButton, { borderColor: colors.primary }]}
              onPress={handleBiometricAuth}
              disabled={isLoading}
            >
              <Icon name="fingerprint" size={24} color={colors.primary} />
              <Text style={[styles.biometricButtonText, { color: colors.primary }]}>
                {t('auth.loginWithBiometric')}
              </Text>
            </TouchableOpacity>
          )}

          <TouchableOpacity
            style={styles.switchMode}
            onPress={() => setIsLoginMode(!isLoginMode)}
          >
            <Text style={[styles.switchModeText, { color: colors.textSecondary }]}>
              {isLoginMode 
                ? "Don't have an account? "
                : "Already have an account? "
              }
              <Text style={{ color: colors.primary }}>
                {isLoginMode ? t('auth.register') : t('auth.login')}
              </Text>
            </Text>
          </TouchableOpacity>
        </View>

        {error && (
          <View style={[styles.errorContainer, { backgroundColor: colors.error + '20' }]}>
            <Text style={[styles.errorText, { color: colors.error }]}>
              {error}
            </Text>
          </View>
        )}
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    justifyContent: 'center',
    paddingHorizontal: 24,
    paddingVertical: 40,
  },
  header: {
    alignItems: 'center',
    marginBottom: 40,
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
  },
  form: {
    marginBottom: 20,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginBottom: 16,
  },
  input: {
    flex: 1,
    marginLeft: 12,
    fontSize: 16,
  },
  forgotPassword: {
    alignItems: 'flex-end',
    marginBottom: 24,
  },
  forgotPasswordText: {
    fontSize: 14,
  },
  authButton: {
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
    marginBottom: 16,
  },
  authButtonText: {
    fontSize: 18,
    fontWeight: '600',
  },
  biometricButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    borderRadius: 12,
    borderWidth: 1,
    marginBottom: 24,
  },
  biometricButtonText: {
    fontSize: 16,
    fontWeight: '500',
    marginLeft: 8,
  },
  switchMode: {
    alignItems: 'center',
  },
  switchModeText: {
    fontSize: 14,
  },
  errorContainer: {
    padding: 12,
    borderRadius: 8,
    marginTop: 16,
  },
  errorText: {
    fontSize: 14,
    textAlign: 'center',
  },
});
