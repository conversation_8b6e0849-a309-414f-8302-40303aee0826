import React, { useState } from 'react'
import { Routes, Route } from 'react-router-dom'
import { motion } from 'framer-motion'
import {
  DocumentTextIcon,
  PlayIcon,
  PhotoIcon,
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon,
  PlusIcon,
} from '@heroicons/react/24/outline'
import { DataTable } from '../components/DataTable'
import { ContentApprovalModal } from '../components/modals/ContentApprovalModal'

export const ContentPage: React.FC = () => {
  const [showApprovalModal, setShowApprovalModal] = useState(false)
  const [selectedContent, setSelectedContent] = useState<any>(null)
  const [selectedItems, setSelectedItems] = useState<string[]>([])

  const columns = [
    { key: 'title', header: 'Content Title', sortable: true },
    { key: 'type', header: 'Type' },
    { key: 'course', header: 'Course' },
    { key: 'teacher', header: 'Teacher' },
    { key: 'uploadDate', header: 'Upload Date', sortable: true },
    { key: 'status', header: 'Status' },
    { key: 'views', header: 'Views' },
    { key: 'actions', header: 'Actions' },
  ]

  const mockContent = [
    {
      id: '1',
      title: 'Quadratic Equations - Chapter 1',
      type: 'Video',
      course: 'JEE Mathematics',
      teacher: 'Dr. Rajesh Kumar',
      uploadDate: '2024-01-15',
      status: 'Pending Review',
      views: '0',
    },
    {
      id: '2',
      title: 'Organic Chemistry Notes',
      type: 'Document',
      course: 'NEET Chemistry',
      teacher: 'Dr. Priya Sharma',
      uploadDate: '2024-01-14',
      status: 'Approved',
      views: '1,234',
    },
    {
      id: '3',
      title: 'Physics Problem Set - Mechanics',
      type: 'Assignment',
      course: 'JEE Physics',
      teacher: 'Prof. Amit Singh',
      uploadDate: '2024-01-13',
      status: 'Rejected',
      views: '0',
    },
  ]

  const stats = [
    { name: 'Total Content', value: '2,847', icon: DocumentTextIcon, color: 'bg-blue-500' },
    { name: 'Pending Review', value: '127', icon: ClockIcon, color: 'bg-yellow-500' },
    { name: 'Approved', value: '2,456', icon: CheckCircleIcon, color: 'bg-green-500' },
    { name: 'Rejected', value: '264', icon: XCircleIcon, color: 'bg-red-500' },
  ]

  const handleBulkApproval = () => {
    if (selectedItems.length > 0) {
      setShowApprovalModal(true)
    }
  }

  return (
    <Routes>
      <Route path="/" element={
        <div className="space-y-6">
          {/* Header */}
          <div className="md:flex md:items-center md:justify-between">
            <div className="flex-1 min-w-0">
              <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
                Content Management
              </h2>
              <p className="mt-1 text-sm text-gray-500">
                Review, approve and manage educational content
              </p>
            </div>
            <div className="mt-4 flex md:mt-0 md:ml-4 space-x-2">
              <button className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                <DocumentTextIcon className="-ml-1 mr-2 h-5 w-5" />
                Upload Content
              </button>
              <button className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700">
                <PlusIcon className="-ml-1 mr-2 h-5 w-5" />
                Bulk Upload
              </button>
            </div>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {stats.map((stat, index) => (
              <motion.div
                key={stat.name}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className="bg-white rounded-lg p-6 shadow-sm border border-gray-200"
              >
                <div className="flex items-center">
                  <div className={`flex-shrink-0 p-3 rounded-md ${stat.color}`}>
                    <stat.icon className="h-6 w-6 text-white" />
                  </div>
                  <div className="ml-5">
                    <p className="text-sm font-medium text-gray-500">{stat.name}</p>
                    <p className="text-2xl font-semibold text-gray-900">{stat.value}</p>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>

          {/* Filters */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
              <input
                type="text"
                placeholder="Search content..."
                className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              />
              <select className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                <option value="">All Types</option>
                <option value="video">Videos</option>
                <option value="document">Documents</option>
                <option value="assignment">Assignments</option>
                <option value="quiz">Quizzes</option>
              </select>
              <select className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                <option value="">All Courses</option>
                <option value="jee-math">JEE Mathematics</option>
                <option value="neet-bio">NEET Biology</option>
                <option value="jee-physics">JEE Physics</option>
              </select>
              <select className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                <option value="">All Status</option>
                <option value="pending">Pending Review</option>
                <option value="approved">Approved</option>
                <option value="rejected">Rejected</option>
              </select>
              <select className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                <option value="">All Teachers</option>
                <option value="dr-rajesh">Dr. Rajesh Kumar</option>
                <option value="dr-priya">Dr. Priya Sharma</option>
                <option value="prof-amit">Prof. Amit Singh</option>
              </select>
            </div>
          </div>

          {/* Bulk Actions */}
          {selectedItems.length > 0 && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-primary-50 border border-primary-200 rounded-lg p-4"
            >
              <div className="flex items-center justify-between">
                <span className="text-sm text-primary-700">
                  {selectedItems.length} item{selectedItems.length > 1 ? 's' : ''} selected
                </span>
                <div className="flex space-x-2">
                  <button
                    onClick={handleBulkApproval}
                    className="px-3 py-1 text-sm bg-green-600 text-white rounded hover:bg-green-700"
                  >
                    Bulk Approve
                  </button>
                  <button className="px-3 py-1 text-sm bg-red-600 text-white rounded hover:bg-red-700">
                    Bulk Reject
                  </button>
                  <button className="px-3 py-1 text-sm bg-gray-600 text-white rounded hover:bg-gray-700">
                    Move to Archive
                  </button>
                </div>
              </div>
            </motion.div>
          )}

          {/* Content Table */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            <DataTable
              columns={columns}
              data={mockContent}
              onRowSelect={setSelectedItems}
              selectedRows={selectedItems}
              onRowEdit={(content) => {
                setSelectedContent(content)
                setShowApprovalModal(true)
              }}
            />
          </div>

          {/* Content Approval Modal */}
          <ContentApprovalModal
            open={showApprovalModal}
            onClose={() => {
              setShowApprovalModal(false)
              setSelectedContent(null)
            }}
            content={selectedContent}
            selectedCount={selectedItems.length}
          />
        </div>
      } />
    </Routes>
  )
}
