import React, { useState } from 'react'
import { Routes, Route } from 'react-router-dom'
import { motion } from 'framer-motion'
import {
  CogIcon,
  ShieldCheckIcon,
  CreditCardIcon,
  BellIcon,
  GlobeAltIcon,
  ServerIcon,
  DatabaseIcon,
  UserGroupIcon,
} from '@heroicons/react/24/outline'
import { GeneralSettings } from '../components/settings/GeneralSettings'
import { SecuritySettings } from '../components/settings/SecuritySettings'
import { PaymentSettings } from '../components/settings/PaymentSettings'
import { NotificationSettings } from '../components/settings/NotificationSettings'
import { SystemSettings } from '../components/settings/SystemSettings'

export const SettingsPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState('general')

  const tabs = [
    { id: 'general', name: 'General', icon: CogIcon },
    { id: 'security', name: 'Security', icon: ShieldCheckIcon },
    { id: 'payments', name: 'Payments', icon: CreditCardIcon },
    { id: 'notifications', name: 'Notifications', icon: BellIcon },
    { id: 'localization', name: 'Localization', icon: GlobeAltIcon },
    { id: 'integrations', name: 'Integrations', icon: ServerIcon },
    { id: 'backup', name: 'Backup & Recovery', icon: DatabaseIcon },
    { id: 'users', name: 'User Management', icon: UserGroupIcon },
  ]

  const renderTabContent = () => {
    switch (activeTab) {
      case 'general':
        return <GeneralSettings />
      case 'security':
        return <SecuritySettings />
      case 'payments':
        return <PaymentSettings />
      case 'notifications':
        return <NotificationSettings />
      case 'system':
        return <SystemSettings />
      default:
        return (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-12 text-center">
            <CogIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">
              {tabs.find(tab => tab.id === activeTab)?.name} Settings
            </h3>
            <p className="mt-1 text-sm text-gray-500">
              Configuration options for this section are coming soon.
            </p>
          </div>
        )
    }
  }

  return (
    <Routes>
      <Route path="/" element={
        <div className="space-y-6">
          {/* Header */}
          <div className="md:flex md:items-center md:justify-between">
            <div className="flex-1 min-w-0">
              <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
                System Settings
              </h2>
              <p className="mt-1 text-sm text-gray-500">
                Configure platform settings, security, and integrations
              </p>
            </div>
            <div className="mt-4 flex md:mt-0 md:ml-4">
              <button className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700">
                <DatabaseIcon className="-ml-1 mr-2 h-5 w-5" />
                Create Backup
              </button>
            </div>
          </div>

          {/* Settings Layout */}
          <div className="flex flex-col lg:flex-row gap-6">
            {/* Sidebar Navigation */}
            <div className="lg:w-64 flex-shrink-0">
              <nav className="space-y-1 bg-white rounded-lg shadow-sm border border-gray-200 p-2">
                {tabs.map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                      activeTab === tab.id
                        ? 'bg-primary-50 text-primary-700 border-r-4 border-primary-600'
                        : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                    }`}
                  >
                    <tab.icon className={`mr-3 h-5 w-5 ${
                      activeTab === tab.id ? 'text-primary-600' : 'text-gray-400'
                    }`} />
                    {tab.name}
                  </button>
                ))}
              </nav>

              {/* System Status */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
                className="mt-6 bg-white rounded-lg shadow-sm border border-gray-200 p-4"
              >
                <h3 className="text-sm font-medium text-gray-900 mb-3">System Status</h3>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-xs text-gray-500">Database</span>
                    <div className="flex items-center">
                      <div className="w-2 h-2 bg-green-400 rounded-full mr-2"></div>
                      <span className="text-xs text-green-600">Healthy</span>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-xs text-gray-500">API Service</span>
                    <div className="flex items-center">
                      <div className="w-2 h-2 bg-green-400 rounded-full mr-2"></div>
                      <span className="text-xs text-green-600">Online</span>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-xs text-gray-500">Storage</span>
                    <div className="flex items-center">
                      <div className="w-2 h-2 bg-yellow-400 rounded-full mr-2"></div>
                      <span className="text-xs text-yellow-600">78% Used</span>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-xs text-gray-500">Cache</span>
                    <div className="flex items-center">
                      <div className="w-2 h-2 bg-green-400 rounded-full mr-2"></div>
                      <span className="text-xs text-green-600">Optimized</span>
                    </div>
                  </div>
                </div>
              </motion.div>
            </div>

            {/* Main Content */}
            <div className="flex-1">
              <motion.div
                key={activeTab}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3 }}
              >
                {renderTabContent()}
              </motion.div>
            </div>
          </div>
        </div>
      } />
    </Routes>
  )
}
