# API Configuration
VITE_API_URL=http://localhost:5000/api
VITE_SOCKET_URL=http://localhost:5000

# Environment
VITE_NODE_ENV=development

# App Configuration
VITE_APP_NAME=RJWU EduTech Platform
VITE_APP_VERSION=1.0.0

# Firebase (Optional - for push notifications)
VITE_FIREBASE_API_KEY=
VITE_FIREBASE_AUTH_DOMAIN=
VITE_FIREBASE_PROJECT_ID=
VITE_FIREBASE_STORAGE_BUCKET=
VITE_FIREBASE_MESSAGING_SENDER_ID=
VITE_FIREBASE_APP_ID=

# Video Streaming (Optional)
VITE_VIDEO_STREAMING_URL=

# Payment Gateway (Optional)
VITE_RAZORPAY_KEY_ID=
VITE_STRIPE_PUBLISHABLE_KEY=

# Analytics (Optional)
VITE_GOOGLE_ANALYTICS_ID=

# Feature Flags
VITE_ENABLE_SOCIAL_LOGIN=true
VITE_ENABLE_OFFLINE_MODE=true
VITE_ENABLE_PUSH_NOTIFICATIONS=true
