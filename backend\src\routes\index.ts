import express from 'express';
import authRoutes from './authRoutes';
import userRoutes from './userRoutes';
import batchRoutes from './batchRoutes';
import liveClassRoutes from './liveClassRoutes';

const router = express.Router();

router.use('/auth', authRoutes);
router.use('/users', userRoutes);
router.use('/batches', batchRoutes);
router.use('/live-classes', liveClassRoutes);

export default router;
