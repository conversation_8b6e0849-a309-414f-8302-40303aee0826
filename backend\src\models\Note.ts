import mongoose, { Schema } from 'mongoose';
import { INote } from '@/types';

const noteSchema = new Schema<INote>({
  title: {
    type: String,
    required: [true, 'Note title is required'],
    trim: true,
    maxlength: [200, 'Note title cannot exceed 200 characters']
  },
  content: {
    type: String,
    required: [true, 'Note content is required']
  },
  type: {
    type: String,
    enum: ['pdf', 'text', 'image'],
    required: [true, 'Note type is required']
  },
  url: {
    type: String,
    match: [/^https?:\/\/.+/, 'Please enter a valid URL']
  },
  chapterId: {
    type: Schema.Types.ObjectId,
    ref: 'Chapter',
    required: [true, 'Chapter ID is required']
  },
  subjectId: {
    type: Schema.Types.ObjectId,
    ref: 'Subject',
    required: [true, 'Subject ID is required']
  },
  order: {
    type: Number,
    required: [true, 'Note order is required'],
    min: [1, 'Order must be at least 1']
  },
  isActive: {
    type: Boolean,
    default: true
  },
  accessLevel: {
    type: String,
    enum: ['free', 'premium', 'batch_only'],
    default: 'batch_only'
  },
  downloads: {
    type: Number,
    default: 0,
    min: [0, 'Downloads cannot be negative']
  },
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  }
}, {
  timestamps: true
});

noteSchema.index({ chapterId: 1, order: 1 }, { unique: true });
noteSchema.index({ subjectId: 1 });
noteSchema.index({ type: 1 });
noteSchema.index({ accessLevel: 1 });
noteSchema.index({ isActive: 1 });

export default mongoose.model<INote>('Note', noteSchema);
