import { api } from './api';
import { Course, Lesson } from '@/types';

export class CourseService {
  static async getCourses(params?: {
    page?: number;
    limit?: number;
    category?: string;
  }): Promise<Course[]> {
    try {
      const queryParams = new URLSearchParams();
      if (params?.page) queryParams.append('page', params.page.toString());
      if (params?.limit) queryParams.append('limit', params.limit.toString());
      if (params?.category) queryParams.append('category', params.category);

      const response = await api.get<Course[]>(`/courses?${queryParams.toString()}`);
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  static async getFeaturedCourses(): Promise<Course[]> {
    try {
      const response = await api.get<Course[]>('/courses/featured');
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  static async getMyCourses(): Promise<Course[]> {
    try {
      const response = await api.get<Course[]>('/courses/my-courses');
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  static async getCourseDetails(courseId: string): Promise<Course> {
    try {
      const response = await api.get<Course>(`/courses/${courseId}`);
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  static async searchCourses(query: string): Promise<Course[]> {
    try {
      const response = await api.get<Course[]>(`/courses/search?q=${encodeURIComponent(query)}`);
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  static async purchaseCourse(courseId: string): Promise<any> {
    try {
      const response = await api.post(`/courses/${courseId}/purchase`);
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  static async markLessonComplete(courseId: string, lessonId: string): Promise<any> {
    try {
      const response = await api.post(`/courses/${courseId}/lessons/${lessonId}/complete`);
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  static async updateProgress(
    courseId: string,
    lessonId: string,
    progress: number
  ): Promise<any> {
    try {
      const response = await api.patch(`/courses/${courseId}/lessons/${lessonId}/progress`, {
        progress,
      });
      return response.data;
    } catch (error) {
      throw error;
    }
  }
}
