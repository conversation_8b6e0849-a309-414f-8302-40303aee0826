import { Schema, model, Document, Types } from 'mongoose';

export interface ILiveClassParticipant extends Document {
  _id: Types.ObjectId;
  liveClass: Types.ObjectId;
  user: Types.ObjectId;
  joinedAt: Date;
  leftAt?: Date;
  status: 'joined' | 'left' | 'removed' | 'banned';
  role: 'student' | 'teacher' | 'moderator' | 'observer';
  permissions: {
    canSpeak: boolean;
    canVideo: boolean;
    canScreenShare: boolean;
    canChat: boolean;
    canViewPolls: boolean;
    canCreatePolls: boolean;
  };
  attendance: {
    marked: boolean;
    markedAt?: Date;
    markedBy?: Types.ObjectId;
    duration: number; // in minutes
    engagementScore: number;
  };
  breakoutRoom?: {
    roomId: string;
    joinedAt: Date;
    leftAt?: Date;
  };
  connectionInfo: {
    ip: string;
    userAgent: string;
    platform: string;
    quality: 'low' | 'medium' | 'high' | 'auto';
  };
  warnings: [{
    type: string;
    reason: string;
    issuedBy: Types.ObjectId;
    issuedAt: Date;
  }];
  createdAt: Date;
  updatedAt: Date;
}

const LiveClassParticipantSchema = new Schema<ILiveClassParticipant>({
  liveClass: {
    type: Schema.Types.ObjectId,
    ref: 'LiveClass',
    required: true
  },
  user: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  joinedAt: {
    type: Date,
    default: Date.now
  },
  leftAt: Date,
  status: {
    type: String,
    enum: ['joined', 'left', 'removed', 'banned'],
    default: 'joined'
  },
  role: {
    type: String,
    enum: ['student', 'teacher', 'moderator', 'observer'],
    default: 'student'
  },
  permissions: {
    canSpeak: {
      type: Boolean,
      default: true
    },
    canVideo: {
      type: Boolean,
      default: true
    },
    canScreenShare: {
      type: Boolean,
      default: false
    },
    canChat: {
      type: Boolean,
      default: true
    },
    canViewPolls: {
      type: Boolean,
      default: true
    },
    canCreatePolls: {
      type: Boolean,
      default: false
    }
  },
  attendance: {
    marked: {
      type: Boolean,
      default: false
    },
    markedAt: Date,
    markedBy: {
      type: Schema.Types.ObjectId,
      ref: 'User'
    },
    duration: {
      type: Number,
      default: 0
    },
    engagementScore: {
      type: Number,
      default: 0,
      min: 0,
      max: 100
    }
  },
  breakoutRoom: {
    roomId: String,
    joinedAt: Date,
    leftAt: Date
  },
  connectionInfo: {
    ip: String,
    userAgent: String,
    platform: String,
    quality: {
      type: String,
      enum: ['low', 'medium', 'high', 'auto'],
      default: 'auto'
    }
  },
  warnings: [{
    type: String,
    reason: String,
    issuedBy: {
      type: Schema.Types.ObjectId,
      ref: 'User'
    },
    issuedAt: {
      type: Date,
      default: Date.now
    }
  }]
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Compound indexes for performance
LiveClassParticipantSchema.index({ liveClass: 1, user: 1 }, { unique: true });
LiveClassParticipantSchema.index({ liveClass: 1, status: 1 });
LiveClassParticipantSchema.index({ user: 1, joinedAt: -1 });

// Virtual for session duration
LiveClassParticipantSchema.virtual('sessionDuration').get(function() {
  if (this.leftAt) {
    return Math.floor((this.leftAt.getTime() - this.joinedAt.getTime()) / (1000 * 60));
  }
  return Math.floor((Date.now() - this.joinedAt.getTime()) / (1000 * 60));
});

export default model<ILiveClassParticipant>('LiveClassParticipant', LiveClassParticipantSchema);
