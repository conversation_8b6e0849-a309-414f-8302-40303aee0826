import React from 'react'
import { useQuery } from '@tanstack/react-query'
import { motion } from 'framer-motion'
import {
  UsersIcon,
  AcademicCapIcon,
  CurrencyRupeeIcon,
  ChartBarIcon,
  ArrowUpIcon,
  ArrowDownIcon,
} from '@heroicons/react/24/outline'
import { adminAPI } from '../services/api'
import { LoadingSpinner } from '../components/LoadingSpinner'
import { RevenueChart } from '../components/charts/RevenueChart'
import { UserGrowthChart } from '../components/charts/UserGrowthChart'
import { ActivityFeed } from '../components/ActivityFeed'
import { QuickActions } from '../components/QuickActions'

const StatCard: React.FC<{
  title: string
  value: string
  change: number
  icon: React.ComponentType<{ className?: string }>
  color: string
}> = ({ title, value, change, icon: Icon, color }) => (
  <motion.div
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    className="bg-white rounded-lg p-6 shadow-sm border border-gray-200"
  >
    <div className="flex items-center">
      <div className={`flex-shrink-0 p-3 rounded-md ${color}`}>
        <Icon className="h-6 w-6 text-white" />
      </div>
      <div className="ml-5 w-0 flex-1">
        <dl>
          <dt className="text-sm font-medium text-gray-500 truncate">{title}</dt>
          <dd className="flex items-baseline">
            <div className="text-2xl font-semibold text-gray-900">{value}</div>
            <div className={`ml-2 flex items-baseline text-sm font-semibold ${
              change >= 0 ? 'text-green-600' : 'text-red-600'
            }`}>
              {change >= 0 ? (
                <ArrowUpIcon className="self-center flex-shrink-0 h-4 w-4" />
              ) : (
                <ArrowDownIcon className="self-center flex-shrink-0 h-4 w-4" />
              )}
              <span className="sr-only">{change >= 0 ? 'Increased' : 'Decreased'} by</span>
              {Math.abs(change)}%
            </div>
          </dd>
        </dl>
      </div>
    </div>
  </motion.div>
)

export const DashboardPage: React.FC = () => {
  const { data: stats, isLoading } = useQuery({
    queryKey: ['dashboard-stats'],
    queryFn: () => adminAPI.getDashboardStats(),
    select: (response) => response.data,
  })

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  const mockStats = {
    totalUsers: { value: '12,847', change: 8.2 },
    totalRevenue: { value: '₹2,45,678', change: 12.5 },
    activeBatches: { value: '34', change: -2.1 },
    completionRate: { value: '87.5%', change: 5.3 },
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="md:flex md:items-center md:justify-between">
        <div className="flex-1 min-w-0">
          <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
            Dashboard
          </h2>
          <p className="mt-1 text-sm text-gray-500">
            Welcome back! Here's what's happening with your platform.
          </p>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="Total Users"
          value={mockStats.totalUsers.value}
          change={mockStats.totalUsers.change}
          icon={UsersIcon}
          color="bg-blue-500"
        />
        <StatCard
          title="Total Revenue"
          value={mockStats.totalRevenue.value}
          change={mockStats.totalRevenue.change}
          icon={CurrencyRupeeIcon}
          color="bg-green-500"
        />
        <StatCard
          title="Active Batches"
          value={mockStats.activeBatches.value}
          change={mockStats.activeBatches.change}
          icon={AcademicCapIcon}
          color="bg-purple-500"
        />
        <StatCard
          title="Completion Rate"
          value={mockStats.completionRate.value}
          change={mockStats.completionRate.change}
          icon={ChartBarIcon}
          color="bg-orange-500"
        />
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
        >
          <h3 className="text-lg font-medium text-gray-900 mb-4">Revenue Overview</h3>
          <RevenueChart />
        </motion.div>

        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
        >
          <h3 className="text-lg font-medium text-gray-900 mb-4">User Growth</h3>
          <UserGrowthChart />
        </motion.div>
      </div>

      {/* Bottom Section */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="lg:col-span-2"
        >
          <ActivityFeed />
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
        >
          <QuickActions />
        </motion.div>
      </div>
    </div>
  )
}
