@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply border-border;
  }
  
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
  
  html {
    scroll-behavior: smooth;
  }
}

@layer components {
  .btn {
    @apply inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50;
  }
  
  .btn-primary {
    @apply btn bg-primary-600 text-white hover:bg-primary-700 active:bg-primary-800;
  }
  
  .btn-secondary {
    @apply btn bg-secondary-100 text-secondary-900 hover:bg-secondary-200 active:bg-secondary-300;
  }
  
  .btn-danger {
    @apply btn bg-danger-600 text-white hover:bg-danger-700 active:bg-danger-800;
  }
  
  .btn-outline {
    @apply btn border border-input bg-background hover:bg-accent hover:text-accent-foreground;
  }
  
  .btn-ghost {
    @apply btn hover:bg-accent hover:text-accent-foreground;
  }
  
  .input {
    @apply flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
  }
  
  .card {
    @apply rounded-lg border bg-card text-card-foreground shadow-sm;
  }
  
  .watermark {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) rotate(-45deg);
    color: rgba(255, 255, 255, 0.1);
    font-size: 2rem;
    font-weight: bold;
    pointer-events: none;
    z-index: 10;
    user-select: none;
  }
  
  .video-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    z-index: 5;
  }
}

/* Dark mode styles */
@media (prefers-color-scheme: dark) {
  :root {
    --background: 222.2% 84% 4.9%;
    --foreground: 210% 40% 98%;
    --card: 222.2% 84% 4.9%;
    --card-foreground: 210% 40% 98%;
    --popover: 222.2% 84% 4.9%;
    --popover-foreground: 210% 40% 98%;
    --primary: 210% 40% 98%;
    --primary-foreground: 222.2% 47.4% 11.2%;
    --secondary: 217.2% 32.6% 17.5%;
    --secondary-foreground: 210% 40% 98%;
    --muted: 217.2% 32.6% 17.5%;
    --muted-foreground: 215% 20.2% 65.1%;
    --accent: 217.2% 32.6% 17.5%;
    --accent-foreground: 210% 40% 98%;
    --destructive: 0% 62.8% 30.6%;
    --destructive-foreground: 210% 40% 98%;
    --border: 217.2% 32.6% 17.5%;
    --input: 217.2% 32.6% 17.5%;
    --ring: 212.7% 26.8% 83.9%;
  }
}

/* Hindi/RTL Support */
.rtl {
  direction: rtl;
}

.rtl .sidebar {
  left: auto;
  right: 0;
}

.rtl .dropdown-menu {
  left: auto;
  right: 0;
}

/* Loading animations */
.spinner {
  @apply inline-block w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin;
}

.skeleton {
  @apply animate-pulse bg-gray-200 rounded;
}

/* Video player customizations */
.video-js {
  @apply w-full h-full;
}

.video-js .vjs-big-play-button {
  @apply border-none bg-black bg-opacity-50 rounded-full;
}

.video-js .vjs-control-bar {
  @apply bg-black bg-opacity-75;
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
}
