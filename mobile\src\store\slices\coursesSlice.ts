import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { Course, Lesson } from '@/types';
import { CourseService } from '@/services/course';

interface CoursesState {
  courses: Course[];
  featuredCourses: Course[];
  myCourses: Course[];
  currentCourse: Course | null;
  currentLesson: Lesson | null;
  isLoading: boolean;
  error: string | null;
  searchQuery: string;
  selectedCategory: string;
  filters: {
    level: string[];
    price: string;
    rating: number;
  };
}

const initialState: CoursesState = {
  courses: [],
  featuredCourses: [],
  myCourses: [],
  currentCourse: null,
  currentLesson: null,
  isLoading: false,
  error: null,
  searchQuery: '',
  selectedCategory: '',
  filters: {
    level: [],
    price: 'all',
    rating: 0,
  },
};

export const fetchCourses = createAsyncThunk(
  'courses/fetchCourses',
  async (params?: { page?: number; limit?: number; category?: string }) => {
    const response = await CourseService.getCourses(params);
    return response;
  }
);

export const fetchFeaturedCourses = createAsyncThunk(
  'courses/fetchFeaturedCourses',
  async () => {
    const response = await CourseService.getFeaturedCourses();
    return response;
  }
);

export const fetchMyCourses = createAsyncThunk(
  'courses/fetchMyCourses',
  async () => {
    const response = await CourseService.getMyCourses();
    return response;
  }
);

export const fetchCourseDetails = createAsyncThunk(
  'courses/fetchCourseDetails',
  async (courseId: string) => {
    const response = await CourseService.getCourseDetails(courseId);
    return response;
  }
);

export const searchCourses = createAsyncThunk(
  'courses/searchCourses',
  async (query: string) => {
    const response = await CourseService.searchCourses(query);
    return response;
  }
);

export const purchaseCourse = createAsyncThunk(
  'courses/purchaseCourse',
  async (courseId: string) => {
    const response = await CourseService.purchaseCourse(courseId);
    return response;
  }
);

export const markLessonComplete = createAsyncThunk(
  'courses/markLessonComplete',
  async ({ courseId, lessonId }: { courseId: string; lessonId: string }) => {
    const response = await CourseService.markLessonComplete(courseId, lessonId);
    return { courseId, lessonId, ...response };
  }
);

export const updateProgress = createAsyncThunk(
  'courses/updateProgress',
  async ({
    courseId,
    lessonId,
    progress,
  }: {
    courseId: string;
    lessonId: string;
    progress: number;
  }) => {
    const response = await CourseService.updateProgress(courseId, lessonId, progress);
    return { courseId, lessonId, progress, ...response };
  }
);

const coursesSlice = createSlice({
  name: 'courses',
  initialState,
  reducers: {
    setSearchQuery: (state, action: PayloadAction<string>) => {
      state.searchQuery = action.payload;
    },
    setSelectedCategory: (state, action: PayloadAction<string>) => {
      state.selectedCategory = action.payload;
    },
    setFilters: (state, action: PayloadAction<Partial<CoursesState['filters']>>) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    clearFilters: (state) => {
      state.filters = {
        level: [],
        price: 'all',
        rating: 0,
      };
      state.searchQuery = '';
      state.selectedCategory = '';
    },
    setCurrentCourse: (state, action: PayloadAction<Course | null>) => {
      state.currentCourse = action.payload;
    },
    setCurrentLesson: (state, action: PayloadAction<Lesson | null>) => {
      state.currentLesson = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
    updateCourseProgress: (
      state,
      action: PayloadAction<{ courseId: string; progress: number; completedLessons: number }>
    ) => {
      const { courseId, progress, completedLessons } = action.payload;
      const course = state.courses.find((c) => c.id === courseId);
      if (course) {
        course.progress = progress;
        course.completedLessons = completedLessons;
      }
      const myCourse = state.myCourses.find((c) => c.id === courseId);
      if (myCourse) {
        myCourse.progress = progress;
        myCourse.completedLessons = completedLessons;
      }
      if (state.currentCourse?.id === courseId) {
        state.currentCourse.progress = progress;
        state.currentCourse.completedLessons = completedLessons;
      }
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch Courses
      .addCase(fetchCourses.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchCourses.fulfilled, (state, action) => {
        state.isLoading = false;
        state.courses = action.payload;
      })
      .addCase(fetchCourses.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to fetch courses';
      })
      // Fetch Featured Courses
      .addCase(fetchFeaturedCourses.fulfilled, (state, action) => {
        state.featuredCourses = action.payload;
      })
      // Fetch My Courses
      .addCase(fetchMyCourses.fulfilled, (state, action) => {
        state.myCourses = action.payload;
      })
      // Fetch Course Details
      .addCase(fetchCourseDetails.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(fetchCourseDetails.fulfilled, (state, action) => {
        state.isLoading = false;
        state.currentCourse = action.payload;
      })
      .addCase(fetchCourseDetails.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to fetch course details';
      })
      // Search Courses
      .addCase(searchCourses.fulfilled, (state, action) => {
        state.courses = action.payload;
      })
      // Purchase Course
      .addCase(purchaseCourse.fulfilled, (state, action) => {
        const courseId = action.meta.arg;
        const course = state.courses.find((c) => c.id === courseId);
        if (course) {
          course.isPurchased = true;
          state.myCourses.push(course);
        }
      })
      // Mark Lesson Complete
      .addCase(markLessonComplete.fulfilled, (state, action) => {
        const { courseId, lessonId } = action.payload;
        const updateLesson = (course: Course) => {
          const lesson = course.lessons.find((l) => l.id === lessonId);
          if (lesson) {
            lesson.isCompleted = true;
            course.completedLessons += 1;
            course.progress = (course.completedLessons / course.totalLessons) * 100;
          }
        };

        const course = state.courses.find((c) => c.id === courseId);
        if (course) updateLesson(course);

        const myCourse = state.myCourses.find((c) => c.id === courseId);
        if (myCourse) updateLesson(myCourse);

        if (state.currentCourse?.id === courseId) {
          updateLesson(state.currentCourse);
        }
      })
      // Update Progress
      .addCase(updateProgress.fulfilled, (state, action) => {
        const { courseId, lessonId, progress } = action.payload;
        if (state.currentLesson?.id === lessonId) {
          // Update lesson progress logic here
        }
      });
  },
});

export const {
  setSearchQuery,
  setSelectedCategory,
  setFilters,
  clearFilters,
  setCurrentCourse,
  setCurrentLesson,
  clearError,
  updateCourseProgress,
} = coursesSlice.actions;

export default coursesSlice.reducer;
