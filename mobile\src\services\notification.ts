import messaging from '@react-native-firebase/messaging';
import { Platform, PermissionsAndroid } from 'react-native';
import { api } from './api';
import { StorageService } from './storage';
import { Notification } from '@/types';

export class NotificationService {
  private static isInitialized = false;

  static async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      // Request permission
      await this.requestPermission();
      
      // Get FCM token
      const token = await messaging().getToken();
      StorageService.setItem('fcm_token', token);
      
      // Set up message handlers
      this.setupMessageHandlers();
      
      this.isInitialized = true;
      console.log('Notification service initialized');
    } catch (error) {
      console.error('Notification service initialization failed:', error);
    }
  }

  static async requestPermission(): Promise<boolean> {
    try {
      if (Platform.OS === 'android') {
        if (Platform.Version >= 33) {
          const granted = await PermissionsAndroid.request(
            PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS
          );
          return granted === PermissionsAndroid.RESULTS.GRANTED;
        }
        return true;
      } else {
        const authStatus = await messaging().requestPermission();
        const enabled =
          authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
          authStatus === messaging.AuthorizationStatus.PROVISIONAL;
        return enabled;
      }
    } catch (error) {
      console.error('Permission request failed:', error);
      return false;
    }
  }

  static async registerForPushNotifications(): Promise<string> {
    try {
      const token = await messaging().getToken();
      
      // Send token to server
      await api.post('/notifications/register', { token });
      
      return token;
    } catch (error) {
      console.error('Push notification registration failed:', error);
      throw error;
    }
  }

  private static setupMessageHandlers(): void {
    // Handle background messages
    messaging().setBackgroundMessageHandler(async remoteMessage => {
      console.log('Message handled in the background!', remoteMessage);
      await this.handleBackgroundMessage(remoteMessage);
    });

    // Handle foreground messages
    messaging().onMessage(async remoteMessage => {
      console.log('A new FCM message arrived!', remoteMessage);
      await this.handleForegroundMessage(remoteMessage);
    });

    // Handle notification opened app
    messaging().onNotificationOpenedApp(remoteMessage => {
      console.log('Notification caused app to open from background state:', remoteMessage);
      this.handleNotificationOpened(remoteMessage);
    });

    // Check whether an initial notification is available
    messaging()
      .getInitialNotification()
      .then(remoteMessage => {
        if (remoteMessage) {
          console.log('Notification caused app to open from quit state:', remoteMessage);
          this.handleNotificationOpened(remoteMessage);
        }
      });
  }

  private static async handleBackgroundMessage(remoteMessage: any): Promise<void> {
    try {
      // Store notification locally for when app opens
      const notification: Notification = {
        id: remoteMessage.messageId || Date.now().toString(),
        title: remoteMessage.notification?.title || '',
        message: remoteMessage.notification?.body || '',
        type: remoteMessage.data?.type || 'info',
        timestamp: new Date(),
        isRead: false,
        actionUrl: remoteMessage.data?.actionUrl,
      };

      const notifications = StorageService.getObject<Notification[]>('local_notifications') || [];
      notifications.unshift(notification);
      StorageService.setObject('local_notifications', notifications);
    } catch (error) {
      console.error('Background message handling failed:', error);
    }
  }

  private static async handleForegroundMessage(remoteMessage: any): Promise<void> {
    try {
      // Show in-app notification or update UI
      const notification: Notification = {
        id: remoteMessage.messageId || Date.now().toString(),
        title: remoteMessage.notification?.title || '',
        message: remoteMessage.notification?.body || '',
        type: remoteMessage.data?.type || 'info',
        timestamp: new Date(),
        isRead: false,
        actionUrl: remoteMessage.data?.actionUrl,
      };

      // Add to store/state if app is active
      // This would typically dispatch to Redux store
      console.log('Foreground notification:', notification);
    } catch (error) {
      console.error('Foreground message handling failed:', error);
    }
  }

  private static handleNotificationOpened(remoteMessage: any): void {
    try {
      // Navigate to appropriate screen based on notification data
      const actionUrl = remoteMessage.data?.actionUrl;
      if (actionUrl) {
        // Handle navigation
        console.log('Navigate to:', actionUrl);
      }
    } catch (error) {
      console.error('Notification opened handling failed:', error);
    }
  }

  static async getNotifications(): Promise<Notification[]> {
    try {
      const response = await api.get<Notification[]>('/notifications');
      return response.data;
    } catch (error) {
      console.error('Failed to fetch notifications:', error);
      // Return local notifications as fallback
      return StorageService.getObject<Notification[]>('local_notifications') || [];
    }
  }

  static async markAsRead(notificationId: string): Promise<void> {
    try {
      await api.patch(`/notifications/${notificationId}/read`);
    } catch (error) {
      console.error('Failed to mark notification as read:', error);
      throw error;
    }
  }

  static async markAllAsRead(): Promise<void> {
    try {
      await api.patch('/notifications/read-all');
    } catch (error) {
      console.error('Failed to mark all notifications as read:', error);
      throw error;
    }
  }

  static async deleteNotification(notificationId: string): Promise<void> {
    try {
      await api.delete(`/notifications/${notificationId}`);
    } catch (error) {
      console.error('Failed to delete notification:', error);
      throw error;
    }
  }

  static async clearAllNotifications(): Promise<void> {
    try {
      await api.delete('/notifications');
    } catch (error) {
      console.error('Failed to clear all notifications:', error);
      throw error;
    }
  }

  static async refreshNotifications(): Promise<void> {
    try {
      // Fetch latest notifications from server
      await this.getNotifications();
    } catch (error) {
      console.error('Failed to refresh notifications:', error);
    }
  }

  static async scheduleLocalNotification(
    title: string,
    message: string,
    triggerDate: Date
  ): Promise<void> {
    try {
      // This would use a local notification library
      // For now, just log the intent
      console.log('Schedule local notification:', { title, message, triggerDate });
    } catch (error) {
      console.error('Failed to schedule local notification:', error);
    }
  }
}
