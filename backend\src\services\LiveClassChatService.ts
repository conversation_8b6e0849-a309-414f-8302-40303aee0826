import { Types } from 'mongoose';
import LiveClassChat, { ILiveClassChat } from '../models/LiveClassChat';
import LiveClassParticipant from '../models/LiveClassParticipant';
import LiveClass from '../models/LiveClass';

export class LiveClassChatService {
  async sendMessage(data: {
    liveClassId: string;
    userId: string;
    message: string;
    type?: 'text' | 'emoji' | 'file';
    metadata?: any;
  }): Promise<ILiveClassChat | null> {
    const { liveClassId, userId, message, type = 'text', metadata } = data;

    // Check if user is participant
    const participant = await LiveClassParticipant.findOne({
      liveClass: liveClassId,
      user: userId,
      status: 'joined'
    });

    if (!participant || !participant.permissions.canChat) {
      throw new Error('User not authorized to send messages');
    }

    // Check live class chat settings
    const liveClass = await LiveClass.findById(liveClassId);
    if (!liveClass || !liveClass.chatSettings.enabled) {
      throw new Error('Chat is disabled for this live class');
    }

    // Check slow mode
    if (liveClass.chatSettings.slowMode > 0) {
      const lastMessage = await LiveClassChat.findOne({
        liveClass: liveClassId,
        user: userId
      }).sort({ createdAt: -1 });

      if (lastMessage) {
        const timeSinceLastMessage = (Date.now() - lastMessage.createdAt.getTime()) / 1000;
        if (timeSinceLastMessage < liveClass.chatSettings.slowMode) {
          throw new Error(`Slow mode active. Please wait ${liveClass.chatSettings.slowMode - timeSinceLastMessage} seconds`);
        }
      }
    }

    // Check message content for moderation
    const moderatedMessage = await this.moderateMessage(message, liveClass.chatSettings.moderationEnabled);

    const chatMessage = new LiveClassChat({
      liveClass: liveClassId,
      user: userId,
      message: moderatedMessage,
      type,
      metadata: metadata || {}
    });

    await chatMessage.save();
    
    // Populate user info
    await chatMessage.populate('user', 'name avatar role');

    return chatMessage;
  }

  async getMessages(liveClassId: string, options: {
    limit?: number;
    skip?: number;
    since?: Date;
    includeHidden?: boolean;
  } = {}): Promise<ILiveClassChat[]> {
    const { limit = 50, skip = 0, since, includeHidden = false } = options;

    const query: any = { liveClass: liveClassId };
    
    if (since) {
      query.createdAt = { $gte: since };
    }

    if (!includeHidden) {
      query['moderation.isHidden'] = { $ne: true };
    }

    const messages = await LiveClassChat.find(query)
      .populate('user', 'name avatar role')
      .populate('metadata.replyTo')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    return messages.reverse(); // Return in chronological order
  }

  async getPinnedMessages(liveClassId: string): Promise<ILiveClassChat[]> {
    return LiveClassChat.find({
      liveClass: liveClassId,
      'moderation.isPinned': true,
      'moderation.isHidden': { $ne: true }
    })
      .populate('user', 'name avatar role')
      .sort({ 'moderation.pinnedAt': -1 });
  }

  async pinMessage(messageId: string, moderatorId: string): Promise<boolean> {
    const message = await LiveClassChat.findById(messageId);
    if (!message) return false;

    // Check if moderator has permission
    const moderator = await LiveClassParticipant.findOne({
      liveClass: message.liveClass,
      user: moderatorId,
      status: 'joined'
    });

    if (!moderator || !moderator.permissions.canModerate) {
      throw new Error('User not authorized to pin messages');
    }

    message.moderation.isPinned = true;
    message.moderation.pinnedBy = new Types.ObjectId(moderatorId);
    message.moderation.pinnedAt = new Date();
    await message.save();

    return true;
  }

  async unpinMessage(messageId: string, moderatorId: string): Promise<boolean> {
    const message = await LiveClassChat.findById(messageId);
    if (!message) return false;

    // Check if moderator has permission
    const moderator = await LiveClassParticipant.findOne({
      liveClass: message.liveClass,
      user: moderatorId,
      status: 'joined'
    });

    if (!moderator || !moderator.permissions.canModerate) {
      throw new Error('User not authorized to unpin messages');
    }

    message.moderation.isPinned = false;
    message.moderation.pinnedBy = undefined;
    message.moderation.pinnedAt = undefined;
    await message.save();

    return true;
  }

  async hideMessage(messageId: string, moderatorId: string, reason?: string): Promise<boolean> {
    const message = await LiveClassChat.findById(messageId);
    if (!message) return false;

    // Check if moderator has permission
    const moderator = await LiveClassParticipant.findOne({
      liveClass: message.liveClass,
      user: moderatorId,
      status: 'joined'
    });

    if (!moderator || !moderator.permissions.canModerate) {
      throw new Error('User not authorized to hide messages');
    }

    message.moderation.isHidden = true;
    message.moderation.hiddenBy = new Types.ObjectId(moderatorId);
    message.moderation.hiddenAt = new Date();
    message.moderation.hiddenReason = reason;
    await message.save();

    return true;
  }

  async unhideMessage(messageId: string, moderatorId: string): Promise<boolean> {
    const message = await LiveClassChat.findById(messageId);
    if (!message) return false;

    // Check if moderator has permission
    const moderator = await LiveClassParticipant.findOne({
      liveClass: message.liveClass,
      user: moderatorId,
      status: 'joined'
    });

    if (!moderator || !moderator.permissions.canModerate) {
      throw new Error('User not authorized to unhide messages');
    }

    message.moderation.isHidden = false;
    message.moderation.hiddenBy = undefined;
    message.moderation.hiddenAt = undefined;
    message.moderation.hiddenReason = undefined;
    await message.save();

    return true;
  }

  async addReaction(messageId: string, userId: string, emoji: string): Promise<boolean> {
    const message = await LiveClassChat.findById(messageId);
    if (!message) return false;

    // Check if user is participant
    const participant = await LiveClassParticipant.findOne({
      liveClass: message.liveClass,
      user: userId,
      status: 'joined'
    });

    if (!participant) {
      throw new Error('User not authorized to react to messages');
    }

    let reaction = message.reactions.find(r => r.emoji === emoji);
    
    if (reaction) {
      // Toggle reaction
      const userIndex = reaction.users.findIndex(u => u.toString() === userId);
      if (userIndex > -1) {
        reaction.users.splice(userIndex, 1);
        reaction.count--;
        if (reaction.count === 0) {
          message.reactions = message.reactions.filter(r => r.emoji !== emoji);
        }
      } else {
        reaction.users.push(new Types.ObjectId(userId));
        reaction.count++;
      }
    } else {
      // Add new reaction
      message.reactions.push({
        emoji,
        users: [new Types.ObjectId(userId)],
        count: 1
      });
    }

    await message.save();
    return true;
  }

  async deleteMessage(messageId: string, userId: string): Promise<boolean> {
    const message = await LiveClassChat.findById(messageId);
    if (!message) return false;

    // Check if user owns the message or is a moderator
    const isOwner = message.user.toString() === userId;
    
    if (!isOwner) {
      const moderator = await LiveClassParticipant.findOne({
        liveClass: message.liveClass,
        user: userId,
        status: 'joined'
      });

      if (!moderator || !moderator.permissions.canModerate) {
        throw new Error('User not authorized to delete this message');
      }
    }

    await LiveClassChat.findByIdAndDelete(messageId);
    return true;
  }

  async sendAnnouncement(liveClassId: string, instructorId: string, message: string): Promise<ILiveClassChat | null> {
    // Check if user is instructor
    const liveClass = await LiveClass.findById(liveClassId);
    if (!liveClass || liveClass.instructor.toString() !== instructorId) {
      throw new Error('Only instructor can send announcements');
    }

    const announcement = new LiveClassChat({
      liveClass: liveClassId,
      user: instructorId,
      message,
      type: 'announcement'
    });

    await announcement.save();
    await announcement.populate('user', 'name avatar role');

    return announcement;
  }

  async getChatAnalytics(liveClassId: string): Promise<{
    totalMessages: number;
    activeUsers: number;
    messagesPerMinute: number;
    topEmojis: Array<{ emoji: string; count: number }>;
    moderationActions: number;
  }> {
    const liveClass = await LiveClass.findById(liveClassId);
    if (!liveClass) {
      throw new Error('Live class not found');
    }

    const startTime = liveClass.actualStart || liveClass.scheduledStart;
    const endTime = liveClass.actualEnd || new Date();

    const [
      totalMessages,
      activeUsers,
      reactionStats,
      moderationActions
    ] = await Promise.all([
      LiveClassChat.countDocuments({ liveClass: liveClassId }),
      LiveClassChat.distinct('user', { liveClass: liveClassId }).then(users => users.length),
      this.getReactionStats(liveClassId),
      LiveClassChat.countDocuments({
        liveClass: liveClassId,
        'moderation.isHidden': true
      })
    ]);

    const durationMinutes = Math.max(1, (endTime.getTime() - startTime.getTime()) / (1000 * 60));
    const messagesPerMinute = totalMessages / durationMinutes;

    return {
      totalMessages,
      activeUsers,
      messagesPerMinute: Math.round(messagesPerMinute * 100) / 100,
      topEmojis: reactionStats,
      moderationActions
    };
  }

  private async moderateMessage(message: string, moderationEnabled: boolean): Promise<string> {
    if (!moderationEnabled) return message;

    // Basic profanity filtering and content moderation
    const inappropriateWords = [
      // Add inappropriate words list
    ];

    let moderatedMessage = message;
    
    inappropriateWords.forEach(word => {
      const regex = new RegExp(word, 'gi');
      moderatedMessage = moderatedMessage.replace(regex, '*'.repeat(word.length));
    });

    return moderatedMessage;
  }

  private async getReactionStats(liveClassId: string): Promise<Array<{ emoji: string; count: number }>> {
    const messages = await LiveClassChat.find({
      liveClass: liveClassId,
      'reactions.0': { $exists: true }
    }, 'reactions');

    const emojiCounts: { [emoji: string]: number } = {};

    messages.forEach(message => {
      message.reactions.forEach(reaction => {
        emojiCounts[reaction.emoji] = (emojiCounts[reaction.emoji] || 0) + reaction.count;
      });
    });

    return Object.entries(emojiCounts)
      .map(([emoji, count]) => ({ emoji, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);
  }
}

export default new LiveClassChatService();
