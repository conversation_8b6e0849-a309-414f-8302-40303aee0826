import React, { createContext, useContext, ReactNode, useEffect } from 'react';
import { AppState, AppStateStatus } from 'react-native';
import { useAppSelector, useAppDispatch } from '@/hooks/redux';
import { SecurityService } from '@/services/security';
import { performSecurityCheck, setScreenRecordingDetected } from '@/store/slices/securitySlice';

interface SecurityContextType {
  isSecure: boolean;
  securityLevel: 'low' | 'medium' | 'high';
  violations: any[];
  checkSecurity: () => Promise<void>;
}

const SecurityContext = createContext<SecurityContextType | undefined>(undefined);

interface SecurityProviderProps {
  children: ReactNode;
}

export const SecurityProvider: React.FC<SecurityProviderProps> = ({ children }) => {
  const dispatch = useAppDispatch();
  const { securityLevel, violations, isRooted, isDebuggingEnabled } = useAppSelector(
    state => state.security
  );

  useEffect(() => {
    initializeSecurity();
    setupSecurityMonitoring();
  }, []);

  const initializeSecurity = async () => {
    try {
      await SecurityService.initializeSecurity();
      dispatch(performSecurityCheck());
    } catch (error) {
      console.error('Security initialization failed:', error);
    }
  };

  const setupSecurityMonitoring = () => {
    // Monitor app state changes for security
    const handleAppStateChange = (nextAppState: AppStateStatus) => {
      if (nextAppState === 'active') {
        // Check for screen recording when app becomes active
        checkScreenRecording();
      }
    };

    const subscription = AppState.addEventListener('change', handleAppStateChange);

    // Set up periodic security checks
    const securityCheckInterval = setInterval(() => {
      performPeriodicSecurityCheck();
    }, 60000); // Check every minute

    return () => {
      subscription?.remove();
      clearInterval(securityCheckInterval);
    };
  };

  const checkScreenRecording = async () => {
    try {
      const isRecording = await SecurityService.isScreenRecording();
      if (isRecording) {
        dispatch(setScreenRecordingDetected(true));
      }
    } catch (error) {
      console.error('Screen recording check failed:', error);
    }
  };

  const performPeriodicSecurityCheck = async () => {
    try {
      dispatch(performSecurityCheck());
    } catch (error) {
      console.error('Periodic security check failed:', error);
    }
  };

  const checkSecurity = async () => {
    try {
      dispatch(performSecurityCheck());
    } catch (error) {
      console.error('Manual security check failed:', error);
    }
  };

  const isSecure = securityLevel === 'high' && !isRooted && !isDebuggingEnabled;

  const value = {
    isSecure,
    securityLevel,
    violations,
    checkSecurity,
  };

  return (
    <SecurityContext.Provider value={value}>
      {children}
    </SecurityContext.Provider>
  );
};

export const useSecurity = (): SecurityContextType => {
  const context = useContext(SecurityContext);
  if (!context) {
    throw new Error('useSecurity must be used within a SecurityProvider');
  }
  return context;
};
