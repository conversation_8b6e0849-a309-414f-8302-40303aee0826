# RJWU EduTech Backend

Backend service for the RJWU EduTech platform built with Node.js, Express.js, TypeScript, and MongoDB.

## Features

- **Authentication & Authorization**: JWT-based authentication with role-based access control
- **User Management**: Complete user lifecycle management with different roles
- **Batch Management**: Course batch creation and student enrollment
- **Content Security**: Watermarking and DRM protection for educational content
- **Rate Limiting**: Protection against abuse and DDoS attacks
- **Comprehensive Logging**: Security and request logging with Winston
- **Input Validation & Sanitization**: Comprehensive data validation and XSS protection
- **Error Handling**: Centralized error handling with detailed logging
- **Database Integration**: MongoDB with Mongoose ODM
- **Testing**: Jest-based unit and integration tests

## Tech Stack

- **Runtime**: Node.js with TypeScript
- **Framework**: Express.js
- **Database**: MongoDB with Mongoose
- **Authentication**: JWT with bcrypt
- **Validation**: express-validator
- **Security**: Helmet, CORS, Rate Limiting
- **Logging**: Winston
- **Testing**: Jest with Supertest
- **Code Quality**: ESLint, Prettier

## Quick Start

### Prerequisites

- Node.js (v16 or higher)
- MongoDB (v4.4 or higher)
- Redis (for session management)

### Installation

1. Install dependencies:
```bash
npm install
```

2. Set up environment variables:
```bash
cp .env.example .env
# Edit .env with your configuration
```

3. Start development server:
```bash
npm run dev
```

### Available Scripts

- `npm run dev` - Start development server with auto-reload
- `npm run build` - Build for production
- `npm start` - Start production server
- `npm test` - Run test suite
- `npm run test:watch` - Run tests in watch mode
- `npm run lint` - Lint code
- `npm run lint:fix` - Fix linting issues

## API Documentation

### Base URL
```
http://localhost:3000/api/v1
```

### Authentication Endpoints

#### Register User
```http
POST /auth/register
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "Password123",
  "firstName": "John",
  "lastName": "Doe",
  "role": "student"
}
```

#### Login
```http
POST /auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "Password123"
}
```

#### Get Profile
```http
GET /auth/profile
Authorization: Bearer <token>
```

### User Management Endpoints

#### Get All Users (Admin only)
```http
GET /users?page=1&limit=10&role=student&search=john
Authorization: Bearer <token>
```

#### Create User (Admin only)
```http
POST /users
Authorization: Bearer <token>
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "Password123",
  "firstName": "Jane",
  "lastName": "Smith",
  "role": "teacher"
}
```

### Batch Management Endpoints

#### Get All Batches
```http
GET /batches?page=1&limit=10&isActive=true
Authorization: Bearer <token>
```

#### Create Batch (Admin only)
```http
POST /batches
Authorization: Bearer <token>
Content-Type: application/json

{
  "name": "Mathematics Batch 2024",
  "description": "Advanced mathematics course",
  "institutionId": "inst_123",
  "startDate": "2024-01-15",
  "endDate": "2024-06-15",
  "maxStudents": 50,
  "fee": 15000
}
```

## Database Models

### User Model
- Authentication and authorization
- Role-based permissions (student, teacher, admin, super_admin)
- Profile information and preferences
- Batch and subject associations

### Batch Model
- Course batch information
- Student and teacher associations
- Enrollment limits and fees
- Institution-specific batches

### Subject & Chapter Models
- Hierarchical content organization
- Subject-specific teachers
- Chapter-wise content delivery

### Content Models (Video, Note, Test)
- Educational content with access controls
- Watermarking and DRM protection
- Performance tracking

### Payment Model
- Transaction tracking
- Multiple payment gateway support
- Refund and billing management

## Security Features

### Authentication & Authorization
- JWT-based stateless authentication
- Role-based access control (RBAC)
- Permission-based fine-grained access
- Secure password hashing with bcrypt

### Input Security
- Request validation with express-validator
- XSS protection through input sanitization
- SQL injection prevention
- CSRF protection

### Rate Limiting
- General API rate limiting
- Stricter limits for authentication endpoints
- Upload-specific rate limiting
- IP-based request tracking

### Security Headers
- Helmet.js for security headers
- CORS configuration
- Content Security Policy
- HTTPS enforcement

## Environment Variables

```env
# Server Configuration
PORT=3000
NODE_ENV=development

# Database
MONGODB_URI=mongodb://localhost:27017/rjwu_edutech

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRE=7d

# Redis
REDIS_URL=redis://localhost:6379

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
```

## Error Handling

The API uses consistent error response format:

```json
{
  "success": false,
  "message": "Error description",
  "error": "ERROR_CODE"
}
```

### Common HTTP Status Codes
- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `429` - Too Many Requests
- `500` - Internal Server Error

## Testing

Run the test suite:
```bash
npm test
```

Run tests in watch mode:
```bash
npm run test:watch
```

### Test Structure
- Unit tests for models and utilities
- Integration tests for API endpoints
- Authentication and authorization tests
- Database operation tests

## Deployment

### Production Build
```bash
npm run build
npm start
```

### Docker Deployment
```dockerfile
FROM node:16-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY dist ./dist
EXPOSE 3000
CMD ["npm", "start"]
```

### Environment Setup
- Use environment-specific `.env` files
- Configure MongoDB connection string
- Set up Redis for session management
- Configure SMTP for email services

## Contributing

1. Follow TypeScript best practices
2. Maintain test coverage above 80%
3. Use conventional commit messages
4. Update documentation for API changes
5. Follow the established code style

## License

This project is licensed under the MIT License.
