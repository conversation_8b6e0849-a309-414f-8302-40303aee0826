import mongoose, { Schema } from 'mongoose';
import { IVideo } from '@/types';

const videoSchema = new Schema<IVideo>({
  title: {
    type: String,
    required: [true, 'Video title is required'],
    trim: true,
    maxlength: [200, 'Video title cannot exceed 200 characters']
  },
  description: {
    type: String,
    required: [true, 'Video description is required'],
    maxlength: [1000, 'Description cannot exceed 1000 characters']
  },
  url: {
    type: String,
    required: [true, 'Video URL is required'],
    match: [/^https?:\/\/.+/, 'Please enter a valid URL']
  },
  duration: {
    type: Number,
    required: [true, 'Video duration is required'],
    min: [1, 'Duration must be at least 1 second']
  },
  thumbnail: {
    type: String,
    match: [/^https?:\/\/.+/, 'Please enter a valid thumbnail URL']
  },
  chapterId: {
    type: Schema.Types.ObjectId,
    ref: 'Chapter',
    required: [true, 'Chapter ID is required']
  },
  subjectId: {
    type: Schema.Types.ObjectId,
    ref: 'Subject',
    required: [true, 'Subject ID is required']
  },
  order: {
    type: Number,
    required: [true, 'Video order is required'],
    min: [1, 'Order must be at least 1']
  },
  isActive: {
    type: Boolean,
    default: true
  },
  watermark: {
    type: Boolean,
    default: true
  },
  drmProtected: {
    type: Boolean,
    default: false
  },
  accessLevel: {
    type: String,
    enum: ['free', 'premium', 'batch_only'],
    default: 'batch_only'
  },
  views: {
    type: Number,
    default: 0,
    min: [0, 'Views cannot be negative']
  },
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  }
}, {
  timestamps: true
});

videoSchema.index({ chapterId: 1, order: 1 }, { unique: true });
videoSchema.index({ subjectId: 1 });
videoSchema.index({ accessLevel: 1 });
videoSchema.index({ isActive: 1 });

export default mongoose.model<IVideo>('Video', videoSchema);
