import React, { useState } from 'react'
import { Routes, Route } from 'react-router-dom'
import { motion } from 'framer-motion'
import {
  CurrencyRupeeIcon,
  CreditCardIcon,
  BanknotesIcon,
  ArrowPathIcon,
  CheckCircleIcon,
  XCircleIcon,
} from '@heroicons/react/24/outline'
import { DataTable } from '../components/DataTable'
import { RefundModal } from '../components/modals/RefundModal'

export const PaymentsPage: React.FC = () => {
  const [showRefundModal, setShowRefundModal] = useState(false)
  const [selectedPayment, setSelectedPayment] = useState<any>(null)

  const columns = [
    { key: 'transactionId', header: 'Transaction ID', sortable: true },
    { key: 'student', header: 'Student' },
    { key: 'course', header: 'Course/Batch' },
    { key: 'amount', header: 'Amount' },
    { key: 'gateway', header: 'Gateway' },
    { key: 'date', header: 'Date', sortable: true },
    { key: 'status', header: 'Status' },
    { key: 'actions', header: 'Actions' },
  ]

  const mockPayments = [
    {
      id: '1',
      transactionId: 'TXN123456789',
      student: 'Rahul Kumar',
      course: 'JEE Mains 2024 - Batch A',
      amount: '₹25,000',
      gateway: 'Razorpay',
      date: '2024-01-15',
      status: 'Success',
    },
    {
      id: '2',
      transactionId: 'TXN987654321',
      student: 'Priya Sharma',
      course: 'NEET 2024 - Biology',
      amount: '₹22,000',
      gateway: 'Payu',
      date: '2024-01-14',
      status: 'Pending',
    },
    {
      id: '3',
      transactionId: 'TXN456789123',
      student: 'Amit Singh',
      course: 'Foundation Course',
      amount: '₹15,000',
      gateway: 'Phonepe',
      date: '2024-01-13',
      status: 'Failed',
    },
  ]

  const stats = [
    { name: 'Total Revenue', value: '₹45.2L', icon: CurrencyRupeeIcon, color: 'bg-green-500' },
    { name: 'Successful Payments', value: '2,847', icon: CheckCircleIcon, color: 'bg-blue-500' },
    { name: 'Pending Payments', value: '127', icon: ArrowPathIcon, color: 'bg-yellow-500' },
    { name: 'Failed Payments', value: '89', icon: XCircleIcon, color: 'bg-red-500' },
  ]

  return (
    <Routes>
      <Route path="/" element={
        <div className="space-y-6">
          {/* Header */}
          <div className="md:flex md:items-center md:justify-between">
            <div className="flex-1 min-w-0">
              <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
                Payment Management
              </h2>
              <p className="mt-1 text-sm text-gray-500">
                Monitor transactions, process refunds and manage payment gateways
              </p>
            </div>
            <div className="mt-4 flex md:mt-0 md:ml-4 space-x-2">
              <button className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                <BanknotesIcon className="-ml-1 mr-2 h-5 w-5" />
                Export Report
              </button>
              <button className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700">
                <CreditCardIcon className="-ml-1 mr-2 h-5 w-5" />
                Gateway Settings
              </button>
            </div>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {stats.map((stat, index) => (
              <motion.div
                key={stat.name}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className="bg-white rounded-lg p-6 shadow-sm border border-gray-200"
              >
                <div className="flex items-center">
                  <div className={`flex-shrink-0 p-3 rounded-md ${stat.color}`}>
                    <stat.icon className="h-6 w-6 text-white" />
                  </div>
                  <div className="ml-5">
                    <p className="text-sm font-medium text-gray-500">{stat.name}</p>
                    <p className="text-2xl font-semibold text-gray-900">{stat.value}</p>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>

          {/* Gateway Status */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Payment Gateway Status</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="flex items-center justify-between p-4 bg-green-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className="w-3 h-3 bg-green-400 rounded-full"></div>
                  <span className="font-medium text-green-900">Razorpay</span>
                </div>
                <span className="text-sm text-green-600">Active</span>
              </div>
              <div className="flex items-center justify-between p-4 bg-green-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className="w-3 h-3 bg-green-400 rounded-full"></div>
                  <span className="font-medium text-green-900">PayU</span>
                </div>
                <span className="text-sm text-green-600">Active</span>
              </div>
              <div className="flex items-center justify-between p-4 bg-yellow-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className="w-3 h-3 bg-yellow-400 rounded-full"></div>
                  <span className="font-medium text-yellow-900">PhonePe</span>
                </div>
                <span className="text-sm text-yellow-600">Maintenance</span>
              </div>
            </div>
          </div>

          {/* Filters */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
              <input
                type="text"
                placeholder="Search transactions..."
                className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              />
              <select className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                <option value="">All Gateways</option>
                <option value="razorpay">Razorpay</option>
                <option value="payu">PayU</option>
                <option value="phonepe">PhonePe</option>
                <option value="paytm">Paytm</option>
              </select>
              <select className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                <option value="">All Status</option>
                <option value="success">Success</option>
                <option value="pending">Pending</option>
                <option value="failed">Failed</option>
                <option value="refunded">Refunded</option>
              </select>
              <input
                type="date"
                className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              />
              <input
                type="date"
                className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              />
            </div>
          </div>

          {/* Payments Table */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            <DataTable
              columns={columns}
              data={mockPayments}
              onRowEdit={(payment) => {
                setSelectedPayment(payment)
                setShowRefundModal(true)
              }}
            />
          </div>

          {/* Refund Modal */}
          <RefundModal
            open={showRefundModal}
            onClose={() => {
              setShowRefundModal(false)
              setSelectedPayment(null)
            }}
            payment={selectedPayment}
          />
        </div>
      } />
    </Routes>
  )
}
