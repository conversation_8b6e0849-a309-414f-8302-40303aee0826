import React, { useState } from 'react'
import { useForm } from 'react-hook-form'
import { LoadingSpinner } from '../LoadingSpinner'
import { ServerIcon, DatabaseIcon, ExclamationTriangleIcon } from '@heroicons/react/24/outline'

interface SystemSettingsForm {
  maxFileSize: number
  allowedFileTypes: string
  cacheEnabled: boolean
  cacheTTL: number
  compressionEnabled: boolean
  compressionLevel: number
  backupEnabled: boolean
  backupFrequency: string
  backupRetention: number
  maintenanceMode: boolean
  maintenanceMessage: string
  debugMode: boolean
  logLevel: string
  maxLogSize: number
  apiRateLimit: number
  sessionCleanup: boolean
  sessionCleanupFrequency: number
  databaseOptimization: boolean
  optimizationSchedule: string
}

export const SystemSettings: React.FC = () => {
  const [loading, setLoading] = useState(false)
  const [systemInfo, setSystemInfo] = useState({
    serverUptime: '15 days, 4 hours',
    memoryUsage: '68%',
    diskUsage: '42%',
    cpuUsage: '23%',
    databaseSize: '2.4 GB',
    activeUsers: 156,
    totalFiles: 12847,
    lastBackup: '2024-01-15 03:00:00',
  })

  const { register, handleSubmit, formState: { errors }, watch } = useForm<SystemSettingsForm>({
    defaultValues: {
      maxFileSize: 100,
      allowedFileTypes: 'jpg,jpeg,png,gif,pdf,mp4,mov,avi,mp3,wav',
      cacheEnabled: true,
      cacheTTL: 3600,
      compressionEnabled: true,
      compressionLevel: 6,
      backupEnabled: true,
      backupFrequency: 'daily',
      backupRetention: 30,
      maintenanceMode: false,
      maintenanceMessage: 'System is under maintenance. Please try again later.',
      debugMode: false,
      logLevel: 'info',
      maxLogSize: 100,
      apiRateLimit: 1000,
      sessionCleanup: true,
      sessionCleanupFrequency: 24,
      databaseOptimization: true,
      optimizationSchedule: '02:00',
    }
  })

  const watchedValues = {
    maintenanceMode: watch('maintenanceMode'),
    backupEnabled: watch('backupEnabled'),
    debugMode: watch('debugMode'),
  }

  const onSubmit = async (data: SystemSettingsForm) => {
    setLoading(true)
    try {
      // API call to save system settings
      console.log('System settings:', data)
      await new Promise(resolve => setTimeout(resolve, 1000)) // Mock API call
    } catch (error) {
      console.error('Error saving system settings:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleSystemAction = async (action: string) => {
    setLoading(true)
    try {
      // API call for system actions
      console.log('System action:', action)
      await new Promise(resolve => setTimeout(resolve, 2000)) // Mock API call
    } catch (error) {
      console.error('Error performing system action:', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      {/* System Information */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center space-x-2 mb-4">
          <ServerIcon className="h-5 w-5 text-primary-600" />
          <h3 className="text-lg font-medium text-gray-900">System Information</h3>
        </div>
        
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center p-4 bg-gray-50 rounded-lg">
            <div className="text-2xl font-bold text-gray-900">{systemInfo.serverUptime}</div>
            <div className="text-sm text-gray-500">Server Uptime</div>
          </div>
          <div className="text-center p-4 bg-gray-50 rounded-lg">
            <div className="text-2xl font-bold text-gray-900">{systemInfo.memoryUsage}</div>
            <div className="text-sm text-gray-500">Memory Usage</div>
          </div>
          <div className="text-center p-4 bg-gray-50 rounded-lg">
            <div className="text-2xl font-bold text-gray-900">{systemInfo.diskUsage}</div>
            <div className="text-sm text-gray-500">Disk Usage</div>
          </div>
          <div className="text-center p-4 bg-gray-50 rounded-lg">
            <div className="text-2xl font-bold text-gray-900">{systemInfo.cpuUsage}</div>
            <div className="text-sm text-gray-500">CPU Usage</div>
          </div>
          <div className="text-center p-4 bg-gray-50 rounded-lg">
            <div className="text-2xl font-bold text-gray-900">{systemInfo.databaseSize}</div>
            <div className="text-sm text-gray-500">Database Size</div>
          </div>
          <div className="text-center p-4 bg-gray-50 rounded-lg">
            <div className="text-2xl font-bold text-gray-900">{systemInfo.activeUsers}</div>
            <div className="text-sm text-gray-500">Active Users</div>
          </div>
          <div className="text-center p-4 bg-gray-50 rounded-lg">
            <div className="text-2xl font-bold text-gray-900">{systemInfo.totalFiles}</div>
            <div className="text-sm text-gray-500">Total Files</div>
          </div>
          <div className="text-center p-4 bg-gray-50 rounded-lg">
            <div className="text-2xl font-bold text-gray-900">{systemInfo.lastBackup}</div>
            <div className="text-sm text-gray-500">Last Backup</div>
          </div>
        </div>
      </div>

      {/* System Actions */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">System Actions</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <button
            onClick={() => handleSystemAction('clear-cache')}
            disabled={loading}
            className="p-4 border border-gray-300 rounded-lg hover:bg-gray-50 text-center disabled:opacity-50"
          >
            <DatabaseIcon className="h-8 w-8 mx-auto text-blue-600 mb-2" />
            <div className="font-medium">Clear Cache</div>
            <div className="text-sm text-gray-500">Clear all cached data</div>
          </button>

          <button
            onClick={() => handleSystemAction('optimize-database')}
            disabled={loading}
            className="p-4 border border-gray-300 rounded-lg hover:bg-gray-50 text-center disabled:opacity-50"
          >
            <DatabaseIcon className="h-8 w-8 mx-auto text-green-600 mb-2" />
            <div className="font-medium">Optimize Database</div>
            <div className="text-sm text-gray-500">Optimize database tables</div>
          </button>

          <button
            onClick={() => handleSystemAction('create-backup')}
            disabled={loading}
            className="p-4 border border-gray-300 rounded-lg hover:bg-gray-50 text-center disabled:opacity-50"
          >
            <ServerIcon className="h-8 w-8 mx-auto text-purple-600 mb-2" />
            <div className="font-medium">Create Backup</div>
            <div className="text-sm text-gray-500">Manual system backup</div>
          </button>
        </div>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {/* File Management */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">File Management</h3>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <div>
              <label htmlFor="maxFileSize" className="block text-sm font-medium text-gray-700">
                Max File Size (MB)
              </label>
              <input
                {...register('maxFileSize', {
                  required: 'Max file size is required',
                  min: { value: 1, message: 'Must be at least 1 MB' },
                  max: { value: 1000, message: 'Cannot exceed 1000 MB' }
                })}
                type="number"
                min="1"
                max="1000"
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
              />
              {errors.maxFileSize && (
                <p className="mt-1 text-sm text-red-600">{errors.maxFileSize.message}</p>
              )}
            </div>

            <div>
              <label htmlFor="allowedFileTypes" className="block text-sm font-medium text-gray-700">
                Allowed File Types (comma separated)
              </label>
              <input
                {...register('allowedFileTypes', { required: 'Allowed file types is required' })}
                type="text"
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                placeholder="jpg,png,pdf,mp4"
              />
              {errors.allowedFileTypes && (
                <p className="mt-1 text-sm text-red-600">{errors.allowedFileTypes.message}</p>
              )}
            </div>
          </div>
        </div>

        {/* Performance Settings */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Performance Settings</h3>
          
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <label htmlFor="cacheEnabled" className="text-sm font-medium text-gray-900">
                  Enable Caching
                </label>
                <p className="text-sm text-gray-500">
                  Enable server-side caching for better performance
                </p>
              </div>
              <input
                {...register('cacheEnabled')}
                type="checkbox"
                className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
              />
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
              <div>
                <label htmlFor="cacheTTL" className="block text-sm font-medium text-gray-700">
                  Cache TTL (seconds)
                </label>
                <input
                  {...register('cacheTTL', {
                    required: 'Cache TTL is required',
                    min: { value: 300, message: 'Minimum 300 seconds' },
                    max: { value: 86400, message: 'Maximum 24 hours' }
                  })}
                  type="number"
                  min="300"
                  max="86400"
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                />
                {errors.cacheTTL && (
                  <p className="mt-1 text-sm text-red-600">{errors.cacheTTL.message}</p>
                )}
              </div>

              <div>
                <label htmlFor="apiRateLimit" className="block text-sm font-medium text-gray-700">
                  API Rate Limit (requests/hour)
                </label>
                <input
                  {...register('apiRateLimit', {
                    required: 'API rate limit is required',
                    min: { value: 100, message: 'Minimum 100 requests' },
                    max: { value: 10000, message: 'Maximum 10000 requests' }
                  })}
                  type="number"
                  min="100"
                  max="10000"
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                />
                {errors.apiRateLimit && (
                  <p className="mt-1 text-sm text-red-600">{errors.apiRateLimit.message}</p>
                )}
              </div>
            </div>

            <div className="flex items-center justify-between">
              <div>
                <label htmlFor="compressionEnabled" className="text-sm font-medium text-gray-900">
                  Enable Compression
                </label>
                <p className="text-sm text-gray-500">
                  Compress responses to reduce bandwidth usage
                </p>
              </div>
              <input
                {...register('compressionEnabled')}
                type="checkbox"
                className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
              />
            </div>
          </div>
        </div>

        {/* Backup Settings */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Backup Settings</h3>
          
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <label htmlFor="backupEnabled" className="text-sm font-medium text-gray-900">
                  Enable Automatic Backups
                </label>
                <p className="text-sm text-gray-500">
                  Automatically create system backups
                </p>
              </div>
              <input
                {...register('backupEnabled')}
                type="checkbox"
                className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
              />
            </div>

            {watchedValues.backupEnabled && (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                <div>
                  <label htmlFor="backupFrequency" className="block text-sm font-medium text-gray-700">
                    Backup Frequency
                  </label>
                  <select
                    {...register('backupFrequency')}
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                  >
                    <option value="hourly">Hourly</option>
                    <option value="daily">Daily</option>
                    <option value="weekly">Weekly</option>
                    <option value="monthly">Monthly</option>
                  </select>
                </div>

                <div>
                  <label htmlFor="backupRetention" className="block text-sm font-medium text-gray-700">
                    Backup Retention (days)
                  </label>
                  <input
                    {...register('backupRetention', {
                      required: 'Backup retention is required',
                      min: { value: 1, message: 'Minimum 1 day' },
                      max: { value: 365, message: 'Maximum 365 days' }
                    })}
                    type="number"
                    min="1"
                    max="365"
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                  />
                  {errors.backupRetention && (
                    <p className="mt-1 text-sm text-red-600">{errors.backupRetention.message}</p>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Maintenance Mode */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Maintenance Mode</h3>
          
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <label htmlFor="maintenanceMode" className="text-sm font-medium text-gray-900">
                  Enable Maintenance Mode
                </label>
                <p className="text-sm text-gray-500">
                  Put the platform in maintenance mode
                </p>
              </div>
              <input
                {...register('maintenanceMode')}
                type="checkbox"
                className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
              />
            </div>

            {watchedValues.maintenanceMode && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <div className="flex">
                  <ExclamationTriangleIcon className="h-5 w-5 text-red-400 mr-2" />
                  <div className="flex-1">
                    <p className="text-sm font-medium text-red-800">
                      Warning: Maintenance Mode is Enabled
                    </p>
                    <p className="text-sm text-red-700 mt-1">
                      Users will not be able to access the platform.
                    </p>
                    <div className="mt-3">
                      <label htmlFor="maintenanceMessage" className="block text-sm font-medium text-red-800">
                        Maintenance Message
                      </label>
                      <textarea
                        {...register('maintenanceMessage')}
                        rows={3}
                        className="mt-1 block w-full rounded-md border-red-300 shadow-sm focus:border-red-500 focus:ring-red-500"
                      />
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Logging Settings */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Logging Settings</h3>
          
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <label htmlFor="debugMode" className="text-sm font-medium text-gray-900">
                  Enable Debug Mode
                </label>
                <p className="text-sm text-gray-500">
                  Enable detailed logging for debugging
                </p>
              </div>
              <input
                {...register('debugMode')}
                type="checkbox"
                className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
              />
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
              <div>
                <label htmlFor="logLevel" className="block text-sm font-medium text-gray-700">
                  Log Level
                </label>
                <select
                  {...register('logLevel')}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                >
                  <option value="error">Error</option>
                  <option value="warn">Warning</option>
                  <option value="info">Info</option>
                  <option value="debug">Debug</option>
                </select>
              </div>

              <div>
                <label htmlFor="maxLogSize" className="block text-sm font-medium text-gray-700">
                  Max Log File Size (MB)
                </label>
                <input
                  {...register('maxLogSize', {
                    required: 'Max log size is required',
                    min: { value: 10, message: 'Minimum 10 MB' },
                    max: { value: 1000, message: 'Maximum 1000 MB' }
                  })}
                  type="number"
                  min="10"
                  max="1000"
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                />
                {errors.maxLogSize && (
                  <p className="mt-1 text-sm text-red-600">{errors.maxLogSize.message}</p>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Save Button */}
        <div className="flex justify-end">
          <button
            type="submit"
            disabled={loading}
            className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"
          >
            {loading ? (
              <LoadingSpinner size="sm" />
            ) : (
              'Save System Settings'
            )}
          </button>
        </div>
      </form>
    </div>
  )
}
