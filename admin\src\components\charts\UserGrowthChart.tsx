import React from 'react'
import {
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from 'recharts'

const data = [
  { month: 'Jan', users: 1200, newUsers: 150 },
  { month: 'Feb', users: 1450, newUsers: 250 },
  { month: 'Mar', users: 1680, newUsers: 230 },
  { month: 'Apr', users: 1950, newUsers: 270 },
  { month: 'May', users: 2200, newUsers: 250 },
  { month: 'Jun', users: 2480, newUsers: 280 },
  { month: 'Jul', users: 2750, newUsers: 270 },
  { month: 'Aug', users: 3020, newUsers: 270 },
  { month: 'Sep', users: 3350, newUsers: 330 },
  { month: 'Oct', users: 3680, newUsers: 330 },
  { month: 'Nov', users: 4050, newUsers: 370 },
  { month: 'Dec', users: 4420, newUsers: 370 },
]

export const UserGrowthChart: React.FC = () => {
  return (
    <div className="h-80">
      <ResponsiveContainer width="100%" height="100%">
        <AreaChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
          <defs>
            <linearGradient id="colorUsers" x1="0" y1="0" x2="0" y2="1">
              <stop offset="5%" stopColor="#8884d8" stopOpacity={0.8} />
              <stop offset="95%" stopColor="#8884d8" stopOpacity={0.1} />
            </linearGradient>
            <linearGradient id="colorNewUsers" x1="0" y1="0" x2="0" y2="1">
              <stop offset="5%" stopColor="#82ca9d" stopOpacity={0.8} />
              <stop offset="95%" stopColor="#82ca9d" stopOpacity={0.1} />
            </linearGradient>
          </defs>
          <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
          <XAxis 
            dataKey="month" 
            axisLine={false}
            tickLine={false}
            tick={{ fontSize: 12, fill: '#6b7280' }}
          />
          <YAxis 
            axisLine={false}
            tickLine={false}
            tick={{ fontSize: 12, fill: '#6b7280' }}
          />
          <Tooltip
            contentStyle={{
              backgroundColor: '#374151',
              border: 'none',
              borderRadius: '8px',
              color: 'white',
            }}
            labelStyle={{ color: '#d1d5db' }}
          />
          <Area
            type="monotone"
            dataKey="users"
            stackId="1"
            stroke="#8884d8"
            fill="url(#colorUsers)"
          />
          <Area
            type="monotone"
            dataKey="newUsers"
            stackId="2"
            stroke="#82ca9d"
            fill="url(#colorNewUsers)"
          />
        </AreaChart>
      </ResponsiveContainer>
    </div>
  )
}
