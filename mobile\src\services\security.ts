import { NativeModules, Platform } from 'react-native';
import ReactNativeBiometrics from 'react-native-biometrics';
import JailMonkey from 'react-native-jailmonkey';
import DeviceInfo from 'react-native-device-info';
import { StorageService } from './storage';
import { SECURITY_CONFIG } from '@/constants';

const { SecurityModule } = NativeModules;

interface BiometricAuthResult {
  success: boolean;
  error?: string;
  signature?: string;
}

interface SecurityCheckResult {
  isRooted: boolean;
  isDebugging: boolean;
  isEmulator: boolean;
  deviceId: string;
  securityLevel: 'low' | 'medium' | 'high';
}

export class SecurityService {
  private static rnBiometrics = new ReactNativeBiometrics();

  // Biometric Authentication
  static async isBiometricAvailable(): Promise<boolean> {
    try {
      const { available } = await this.rnBiometrics.isSensorAvailable();
      return available;
    } catch (error) {
      console.error('Biometric availability check failed:', error);
      return false;
    }
  }

  static async getBiometryType(): Promise<string> {
    try {
      const { biometryType } = await this.rnBiometrics.isSensorAvailable();
      return biometryType || 'none';
    } catch (error) {
      console.error('Biometry type check failed:', error);
      return 'none';
    }
  }

  static async authenticateWithBiometric(
    promptMessage: string = 'Authenticate with biometric'
  ): Promise<BiometricAuthResult> {
    try {
      const { success, signature, error } = await this.rnBiometrics.simplePrompt({
        promptMessage,
        cancelButtonText: 'Cancel',
      });

      return {
        success,
        signature,
        error: error || undefined,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Biometric authentication failed',
      };
    }
  }

  static async storeBiometricCredentials(credentials: any): Promise<void> {
    try {
      await StorageService.setSecureItem('biometric_credentials', JSON.stringify(credentials));
    } catch (error) {
      throw new Error('Failed to store biometric credentials');
    }
  }

  static async getBiometricCredentials(): Promise<any> {
    try {
      const credentials = await StorageService.getSecureItem('biometric_credentials');
      return credentials ? JSON.parse(credentials) : null;
    } catch (error) {
      return null;
    }
  }

  static async clearBiometricCredentials(): Promise<void> {
    try {
      await StorageService.removeSecureItem('biometric_credentials');
    } catch (error) {
      console.error('Failed to clear biometric credentials:', error);
    }
  }

  // Device Security Checks
  static async isDeviceRooted(): Promise<boolean> {
    try {
      if (Platform.OS === 'android') {
        return JailMonkey.isJailBroken();
      } else {
        return JailMonkey.isJailBroken();
      }
    } catch (error) {
      console.error('Root detection failed:', error);
      return false;
    }
  }

  static async isDebuggingEnabled(): Promise<boolean> {
    try {
      return JailMonkey.isDebuggingEnabled();
    } catch (error) {
      console.error('Debug detection failed:', error);
      return false;
    }
  }

  static async isEmulator(): Promise<boolean> {
    try {
      return await DeviceInfo.isEmulator();
    } catch (error) {
      console.error('Emulator detection failed:', error);
      return false;
    }
  }

  static async performSecurityCheck(): Promise<SecurityCheckResult> {
    try {
      const [isRooted, isDebugging, isEmulator, deviceId] = await Promise.all([
        this.isDeviceRooted(),
        this.isDebuggingEnabled(),
        this.isEmulator(),
        this.generateDeviceFingerprint(),
      ]);

      let securityLevel: 'low' | 'medium' | 'high' = 'high';
      
      if (isRooted || isDebugging) {
        securityLevel = 'low';
      } else if (isEmulator) {
        securityLevel = 'medium';
      }

      return {
        isRooted,
        isDebugging,
        isEmulator,
        deviceId,
        securityLevel,
      };
    } catch (error) {
      console.error('Security check failed:', error);
      throw error;
    }
  }

  // Screen Protection
  static async blockScreenshots(enabled: boolean): Promise<void> {
    try {
      if (Platform.OS === 'android' && SecurityModule) {
        await SecurityModule.blockScreenshots(enabled);
      } else if (Platform.OS === 'ios' && SecurityModule) {
        await SecurityModule.blockScreenshots(enabled);
      }
    } catch (error) {
      console.error('Screenshot blocking failed:', error);
    }
  }

  static async isScreenRecording(): Promise<boolean> {
    try {
      if (Platform.OS === 'ios' && SecurityModule) {
        return await SecurityModule.isScreenRecording();
      }
      return false;
    } catch (error) {
      console.error('Screen recording detection failed:', error);
      return false;
    }
  }

  static async detectScreenshotAttempt(): Promise<boolean> {
    try {
      if (SecurityModule) {
        return await SecurityModule.detectScreenshotAttempt();
      }
      return false;
    } catch (error) {
      console.error('Screenshot attempt detection failed:', error);
      return false;
    }
  }

  // Device Fingerprinting
  static async generateDeviceFingerprint(): Promise<string> {
    try {
      const [
        deviceId,
        brand,
        model,
        systemVersion,
        buildNumber,
        bundleId,
        deviceName,
      ] = await Promise.all([
        DeviceInfo.getUniqueId(),
        DeviceInfo.getBrand(),
        DeviceInfo.getModel(),
        DeviceInfo.getSystemVersion(),
        DeviceInfo.getBuildNumber(),
        DeviceInfo.getBundleId(),
        DeviceInfo.getDeviceName(),
      ]);

      const fingerprint = `${deviceId}-${brand}-${model}-${systemVersion}-${buildNumber}-${bundleId}-${deviceName}`;
      return btoa(fingerprint); // Base64 encode
    } catch (error) {
      console.error('Device fingerprint generation failed:', error);
      return 'unknown';
    }
  }

  // Certificate Pinning
  static async validateCertificatePinning(url: string): Promise<boolean> {
    try {
      if (!SECURITY_CONFIG.CERTIFICATE_PINS.length) {
        return true;
      }

      if (SecurityModule && SecurityModule.validateCertificatePinning) {
        return await SecurityModule.validateCertificatePinning(
          url,
          SECURITY_CONFIG.CERTIFICATE_PINS
        );
      }

      // Fallback: always allow in development
      return __DEV__ ? true : false;
    } catch (error) {
      console.error('Certificate pinning validation failed:', error);
      return false;
    }
  }

  // Violation Reporting
  static async reportViolation(violation: {
    type: string;
    details: string;
    severity: string;
  }): Promise<string> {
    try {
      const violationId = Date.now().toString();
      const deviceInfo = await this.generateDeviceFingerprint();
      
      const violationData = {
        id: violationId,
        ...violation,
        deviceInfo,
        timestamp: new Date().toISOString(),
        platform: Platform.OS,
      };

      // Store locally
      const violations = await this.getStoredViolations();
      violations.push(violationData);
      await StorageService.setItem('security_violations', JSON.stringify(violations));

      // TODO: Send to server
      console.warn('Security violation reported:', violationData);

      return violationId;
    } catch (error) {
      console.error('Failed to report violation:', error);
      return 'unknown';
    }
  }

  // Data Encryption/Decryption
  static async encryptData(data: string, key?: string): Promise<string> {
    try {
      // Use native module if available, otherwise fallback to basic encoding
      if (SecurityModule && SecurityModule.encryptData) {
        return await SecurityModule.encryptData(data, key);
      }
      
      // Fallback: Base64 encoding (not secure, for demo purposes)
      return btoa(data);
    } catch (error) {
      console.error('Data encryption failed:', error);
      throw error;
    }
  }

  static async decryptData(encryptedData: string, key?: string): Promise<string> {
    try {
      // Use native module if available, otherwise fallback to basic decoding
      if (SecurityModule && SecurityModule.decryptData) {
        return await SecurityModule.decryptData(encryptedData, key);
      }
      
      // Fallback: Base64 decoding
      return atob(encryptedData);
    } catch (error) {
      console.error('Data decryption failed:', error);
      throw error;
    }
  }

  // Secure Storage
  static async clearSecureData(): Promise<void> {
    try {
      await Promise.all([
        this.clearBiometricCredentials(),
        StorageService.removeSecureItem('security_violations'),
        StorageService.removeSecureItem('device_fingerprint'),
      ]);
    } catch (error) {
      console.error('Failed to clear secure data:', error);
    }
  }

  // Helper Methods
  private static async getStoredViolations(): Promise<any[]> {
    try {
      const violations = await StorageService.getItem('security_violations');
      return violations ? JSON.parse(violations) : [];
    } catch (error) {
      return [];
    }
  }

  static async initializeSecurity(): Promise<void> {
    try {
      // Perform initial security checks
      const securityCheck = await this.performSecurityCheck();
      
      // Enable screenshot blocking if configured
      if (SECURITY_CONFIG.SCREENSHOT_BLOCKING_ENABLED) {
        await this.blockScreenshots(true);
      }

      // Store device fingerprint
      await StorageService.setItem('device_fingerprint', securityCheck.deviceId);

      console.log('Security initialized:', securityCheck);
    } catch (error) {
      console.error('Security initialization failed:', error);
    }
  }
}
