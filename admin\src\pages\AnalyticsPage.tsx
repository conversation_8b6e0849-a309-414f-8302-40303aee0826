import React, { useState } from 'react'
import { Routes, Route } from 'react-router-dom'
import { motion } from 'framer-motion'
import {
  ChartBarIcon,
  UsersIcon,
  EyeIcon,
  ClockIcon,
  TrendingUpIcon,
  TrendingDownIcon,
} from '@heroicons/react/24/outline'
import { RevenueChart } from '../components/charts/RevenueChart'
import { UserGrowthChart } from '../components/charts/UserGrowthChart'
import { EngagementChart } from '../components/charts/EngagementChart'
import { PerformanceMetrics } from '../components/analytics/PerformanceMetrics'

export const AnalyticsPage: React.FC = () => {
  const [timeRange, setTimeRange] = useState('30d')
  const [activeTab, setActiveTab] = useState('overview')

  const stats = [
    {
      name: 'Total Users',
      value: '12,847',
      change: '+12.5%',
      trend: 'up',
      icon: UsersIcon,
      color: 'bg-blue-500',
    },
    {
      name: 'Page Views',
      value: '2.4M',
      change: '+8.1%',
      trend: 'up',
      icon: EyeIcon,
      color: 'bg-green-500',
    },
    {
      name: 'Avg. Session',
      value: '24m 32s',
      change: '-2.3%',
      trend: 'down',
      icon: ClockIcon,
      color: 'bg-purple-500',
    },
    {
      name: 'Conversion Rate',
      value: '3.2%',
      change: '+1.8%',
      trend: 'up',
      icon: TrendingUpIcon,
      color: 'bg-orange-500',
    },
  ]

  const tabs = [
    { id: 'overview', name: 'Overview' },
    { id: 'users', name: 'User Analytics' },
    { id: 'content', name: 'Content Performance' },
    { id: 'revenue', name: 'Revenue Analytics' },
    { id: 'engagement', name: 'Engagement' },
  ]

  return (
    <Routes>
      <Route path="/" element={
        <div className="space-y-6">
          {/* Header */}
          <div className="md:flex md:items-center md:justify-between">
            <div className="flex-1 min-w-0">
              <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
                Analytics & Reports
              </h2>
              <p className="mt-1 text-sm text-gray-500">
                Comprehensive insights into platform performance and user behavior
              </p>
            </div>
            <div className="mt-4 flex md:mt-0 md:ml-4 space-x-2">
              <select
                value={timeRange}
                onChange={(e) => setTimeRange(e.target.value)}
                className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              >
                <option value="7d">Last 7 days</option>
                <option value="30d">Last 30 days</option>
                <option value="90d">Last 90 days</option>
                <option value="1y">Last year</option>
              </select>
              <button className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700">
                <ChartBarIcon className="-ml-1 mr-2 h-5 w-5" />
                Export Report
              </button>
            </div>
          </div>

          {/* Tabs */}
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`py-2 px-1 border-b-2 font-medium text-sm ${
                    activeTab === tab.id
                      ? 'border-primary-500 text-primary-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  {tab.name}
                </button>
              ))}
            </nav>
          </div>

          {/* Overview Tab */}
          {activeTab === 'overview' && (
            <div className="space-y-6">
              {/* Key Metrics */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                {stats.map((stat, index) => (
                  <motion.div
                    key={stat.name}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="bg-white rounded-lg p-6 shadow-sm border border-gray-200"
                  >
                    <div className="flex items-center">
                      <div className={`flex-shrink-0 p-3 rounded-md ${stat.color}`}>
                        <stat.icon className="h-6 w-6 text-white" />
                      </div>
                      <div className="ml-5 w-0 flex-1">
                        <dl>
                          <dt className="text-sm font-medium text-gray-500 truncate">{stat.name}</dt>
                          <dd className="flex items-baseline">
                            <div className="text-2xl font-semibold text-gray-900">{stat.value}</div>
                            <div className={`ml-2 flex items-baseline text-sm font-semibold ${
                              stat.trend === 'up' ? 'text-green-600' : 'text-red-600'
                            }`}>
                              {stat.trend === 'up' ? (
                                <TrendingUpIcon className="self-center flex-shrink-0 h-4 w-4" />
                              ) : (
                                <TrendingDownIcon className="self-center flex-shrink-0 h-4 w-4" />
                              )}
                              <span className="sr-only">{stat.trend === 'up' ? 'Increased' : 'Decreased'} by</span>
                              {stat.change}
                            </div>
                          </dd>
                        </dl>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>

              {/* Charts */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.2 }}
                  className="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
                >
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Revenue Trends</h3>
                  <RevenueChart />
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.3 }}
                  className="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
                >
                  <h3 className="text-lg font-medium text-gray-900 mb-4">User Growth</h3>
                  <UserGrowthChart />
                </motion.div>
              </div>

              {/* Top Courses */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
                className="bg-white rounded-lg shadow-sm border border-gray-200"
              >
                <div className="px-6 py-4 border-b border-gray-200">
                  <h3 className="text-lg font-medium text-gray-900">Top Performing Courses</h3>
                </div>
                <div className="p-6">
                  <div className="space-y-4">
                    {[
                      { name: 'JEE Mathematics Advanced', students: 1247, revenue: '₹31.2L', growth: '+15%' },
                      { name: 'NEET Biology Complete', students: 967, revenue: '₹21.3L', growth: '+8%' },
                      { name: 'Physics Foundation', students: 543, revenue: '₹8.1L', growth: '+22%' },
                      { name: 'Chemistry Organic', students: 432, revenue: '₹9.5L', growth: '+5%' },
                    ].map((course, index) => (
                      <div key={course.name} className="flex items-center justify-between py-3 border-b last:border-b-0">
                        <div className="flex items-center space-x-4">
                          <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
                            <span className="text-primary-600 font-medium text-sm">{index + 1}</span>
                          </div>
                          <div>
                            <p className="text-sm font-medium text-gray-900">{course.name}</p>
                            <p className="text-sm text-gray-500">{course.students} students enrolled</p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="text-sm font-medium text-gray-900">{course.revenue}</p>
                          <p className="text-sm text-green-600">{course.growth}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </motion.div>
            </div>
          )}

          {/* Engagement Tab */}
          {activeTab === 'engagement' && (
            <div className="space-y-6">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
              >
                <h3 className="text-lg font-medium text-gray-900 mb-4">User Engagement</h3>
                <EngagementChart />
              </motion.div>

              <PerformanceMetrics />
            </div>
          )}

          {/* Other tabs would have similar content structure */}
          {activeTab !== 'overview' && activeTab !== 'engagement' && (
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-12 text-center">
              <ChartBarIcon className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">
                {tabs.find(tab => tab.id === activeTab)?.name} Analytics
              </h3>
              <p className="mt-1 text-sm text-gray-500">
                Detailed analytics for this section are coming soon.
              </p>
            </div>
          )}
        </div>
      } />
    </Routes>
  )
}
