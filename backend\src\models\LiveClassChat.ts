import { Schema, model, Document, Types } from 'mongoose';

export interface ILiveClassChat extends Document {
  _id: Types.ObjectId;
  liveClass: Types.ObjectId;
  user: Types.ObjectId;
  message: string;
  type: 'text' | 'emoji' | 'file' | 'poll' | 'announcement' | 'system';
  metadata: {
    fileUrl?: string;
    fileName?: string;
    fileSize?: number;
    pollId?: Types.ObjectId;
    mentions?: Types.ObjectId[];
    replyTo?: Types.ObjectId;
  };
  moderation: {
    isHidden: boolean;
    hiddenBy?: Types.ObjectId;
    hiddenAt?: Date;
    hiddenReason?: string;
    isPinned: boolean;
    pinnedBy?: Types.ObjectId;
    pinnedAt?: Date;
  };
  reactions: [{
    emoji: string;
    users: Types.ObjectId[];
    count: number;
  }];
  timestamp: Date;
  createdAt: Date;
  updatedAt: Date;
}

const LiveClassChatSchema = new Schema<ILiveClassChat>({
  liveClass: {
    type: Schema.Types.ObjectId,
    ref: 'LiveClass',
    required: true
  },
  user: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  message: {
    type: String,
    required: true,
    maxlength: 1000
  },
  type: {
    type: String,
    enum: ['text', 'emoji', 'file', 'poll', 'announcement', 'system'],
    default: 'text'
  },
  metadata: {
    fileUrl: String,
    fileName: String,
    fileSize: Number,
    pollId: {
      type: Schema.Types.ObjectId,
      ref: 'LiveClassPoll'
    },
    mentions: [{
      type: Schema.Types.ObjectId,
      ref: 'User'
    }],
    replyTo: {
      type: Schema.Types.ObjectId,
      ref: 'LiveClassChat'
    }
  },
  moderation: {
    isHidden: {
      type: Boolean,
      default: false
    },
    hiddenBy: {
      type: Schema.Types.ObjectId,
      ref: 'User'
    },
    hiddenAt: Date,
    hiddenReason: String,
    isPinned: {
      type: Boolean,
      default: false
    },
    pinnedBy: {
      type: Schema.Types.ObjectId,
      ref: 'User'
    },
    pinnedAt: Date
  },
  reactions: [{
    emoji: {
      type: String,
      required: true
    },
    users: [{
      type: Schema.Types.ObjectId,
      ref: 'User'
    }],
    count: {
      type: Number,
      default: 0
    }
  }],
  timestamp: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for performance
LiveClassChatSchema.index({ liveClass: 1, timestamp: -1 });
LiveClassChatSchema.index({ liveClass: 1, type: 1, timestamp: -1 });
LiveClassChatSchema.index({ user: 1, timestamp: -1 });
LiveClassChatSchema.index({ 'moderation.isPinned': 1, liveClass: 1 });

export default model<ILiveClassChat>('LiveClassChat', LiveClassChatSchema);
