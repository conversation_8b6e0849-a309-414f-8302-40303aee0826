import mongoose, { Schema } from 'mongoose';
import { ISubject } from '@/types';

const subjectSchema = new Schema<ISubject>({
  name: {
    type: String,
    required: [true, 'Subject name is required'],
    trim: true,
    maxlength: [100, 'Subject name cannot exceed 100 characters']
  },
  description: {
    type: String,
    required: [true, 'Subject description is required'],
    maxlength: [500, 'Description cannot exceed 500 characters']
  },
  code: {
    type: String,
    required: [true, 'Subject code is required'],
    unique: true,
    uppercase: true,
    match: [/^[A-Z0-9]{3,10}$/, 'Subject code must be 3-10 alphanumeric characters']
  },
  institutionId: {
    type: String,
    required: [true, 'Institution ID is required']
  },
  batchId: {
    type: Schema.Types.ObjectId,
    ref: 'Batch',
    required: [true, 'Batch ID is required']
  },
  teachers: [{
    type: Schema.Types.ObjectId,
    ref: 'User'
  }],
  chapters: [{
    type: Schema.Types.ObjectId,
    ref: 'Chapter'
  }],
  isActive: {
    type: Boolean,
    default: true
  },
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  }
}, {
  timestamps: true
});

subjectSchema.index({ code: 1 });
subjectSchema.index({ institutionId: 1 });
subjectSchema.index({ batchId: 1 });
subjectSchema.index({ isActive: 1 });

export default mongoose.model<ISubject>('Subject', subjectSchema);
