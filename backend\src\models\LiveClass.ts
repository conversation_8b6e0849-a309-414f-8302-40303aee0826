import { Schema, model, Document, Types } from 'mongoose';

export interface ILiveClass extends Document {
  _id: Types.ObjectId;
  title: string;
  description: string;
  instructor: Types.ObjectId;
  course: Types.ObjectId;
  scheduledStart: Date;
  scheduledEnd: Date;
  actualStart?: Date;
  actualEnd?: Date;
  status: 'scheduled' | 'live' | 'ended' | 'cancelled';
  streamingOptions: {
    youtube?: {
      streamKey: string;
      streamUrl: string;
      broadcastId?: string;
    };
    webrtc?: {
      roomId: string;
      signalServer: string;
    };
    jitsi?: {
      roomName: string;
      domain: string;
    };
  };
  maxParticipants: number;
  isRecorded: boolean;
  recordingUrl?: string;
  recordingSettings: {
    autoRecord: boolean;
    includeChat: boolean;
    includePolls: boolean;
    watermark: boolean;
  };
  chatSettings: {
    enabled: boolean;
    moderationEnabled: boolean;
    slowMode: number; // seconds between messages
    allowedRoles: string[];
  };
  pollSettings: {
    enabled: boolean;
    allowAnonymous: boolean;
  };
  attendanceSettings: {
    required: boolean;
    markingMethod: 'automatic' | 'manual' | 'both';
    minimumDuration: number; // minutes
  };
  breakoutRooms: {
    enabled: boolean;
    maxRooms: number;
    autoAssign: boolean;
  };
  securitySettings: {
    waitingRoom: boolean;
    passwordProtected: boolean;
    password?: string;
    allowScreenShare: string[]; // user roles
    moderatorApproval: boolean;
  };
  metadata: {
    tags: string[];
    category: string;
    language: string;
    level: string;
  };
  analytics: {
    totalJoined: number;
    peakConcurrent: number;
    averageDuration: number;
    engagementScore: number;
  };
  createdAt: Date;
  updatedAt: Date;
}

const LiveClassSchema = new Schema<ILiveClass>({
  title: {
    type: String,
    required: true,
    trim: true,
    maxlength: 200
  },
  description: {
    type: String,
    required: true,
    maxlength: 1000
  },
  instructor: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  course: {
    type: Schema.Types.ObjectId,
    ref: 'Course',
    required: true
  },
  scheduledStart: {
    type: Date,
    required: true
  },
  scheduledEnd: {
    type: Date,
    required: true
  },
  actualStart: Date,
  actualEnd: Date,
  status: {
    type: String,
    enum: ['scheduled', 'live', 'ended', 'cancelled'],
    default: 'scheduled'
  },
  streamingOptions: {
    youtube: {
      streamKey: String,
      streamUrl: String,
      broadcastId: String
    },
    webrtc: {
      roomId: String,
      signalServer: String
    },
    jitsi: {
      roomName: String,
      domain: String
    }
  },
  maxParticipants: {
    type: Number,
    default: 500,
    min: 1,
    max: 10000
  },
  isRecorded: {
    type: Boolean,
    default: true
  },
  recordingUrl: String,
  recordingSettings: {
    autoRecord: {
      type: Boolean,
      default: true
    },
    includeChat: {
      type: Boolean,
      default: true
    },
    includePolls: {
      type: Boolean,
      default: true
    },
    watermark: {
      type: Boolean,
      default: true
    }
  },
  chatSettings: {
    enabled: {
      type: Boolean,
      default: true
    },
    moderationEnabled: {
      type: Boolean,
      default: true
    },
    slowMode: {
      type: Number,
      default: 0
    },
    allowedRoles: [{
      type: String,
      enum: ['student', 'teacher', 'admin', 'moderator']
    }]
  },
  pollSettings: {
    enabled: {
      type: Boolean,
      default: true
    },
    allowAnonymous: {
      type: Boolean,
      default: false
    }
  },
  attendanceSettings: {
    required: {
      type: Boolean,
      default: true
    },
    markingMethod: {
      type: String,
      enum: ['automatic', 'manual', 'both'],
      default: 'automatic'
    },
    minimumDuration: {
      type: Number,
      default: 15
    }
  },
  breakoutRooms: {
    enabled: {
      type: Boolean,
      default: false
    },
    maxRooms: {
      type: Number,
      default: 10
    },
    autoAssign: {
      type: Boolean,
      default: false
    }
  },
  securitySettings: {
    waitingRoom: {
      type: Boolean,
      default: false
    },
    passwordProtected: {
      type: Boolean,
      default: false
    },
    password: String,
    allowScreenShare: [{
      type: String,
      enum: ['teacher', 'admin', 'moderator']
    }],
    moderatorApproval: {
      type: Boolean,
      default: false
    }
  },
  metadata: {
    tags: [String],
    category: String,
    language: {
      type: String,
      default: 'hi'
    },
    level: {
      type: String,
      enum: ['beginner', 'intermediate', 'advanced'],
      default: 'beginner'
    }
  },
  analytics: {
    totalJoined: {
      type: Number,
      default: 0
    },
    peakConcurrent: {
      type: Number,
      default: 0
    },
    averageDuration: {
      type: Number,
      default: 0
    },
    engagementScore: {
      type: Number,
      default: 0
    }
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for performance
LiveClassSchema.index({ instructor: 1, scheduledStart: -1 });
LiveClassSchema.index({ course: 1, scheduledStart: -1 });
LiveClassSchema.index({ status: 1, scheduledStart: 1 });
LiveClassSchema.index({ 'metadata.tags': 1 });

// Virtual for duration
LiveClassSchema.virtual('duration').get(function() {
  if (this.actualStart && this.actualEnd) {
    return Math.floor((this.actualEnd.getTime() - this.actualStart.getTime()) / (1000 * 60));
  }
  return Math.floor((this.scheduledEnd.getTime() - this.scheduledStart.getTime()) / (1000 * 60));
});

// Virtual for participant count
LiveClassSchema.virtual('participantCount', {
  ref: 'LiveClassParticipant',
  localField: '_id',
  foreignField: 'liveClass',
  count: true
});

export default model<ILiveClass>('LiveClass', LiveClassSchema);
