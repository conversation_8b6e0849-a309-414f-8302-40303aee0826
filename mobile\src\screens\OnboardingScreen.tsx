import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  FlatList,
  Animated,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import LinearGradient from 'react-native-linear-gradient';
import { useNavigation } from '@react-navigation/native';
import { useTheme } from '@/context/ThemeContext';
import { useI18n } from '@/context/I18nContext';
import { StorageService } from '@/services/storage';
import { STORAGE_KEYS } from '@/constants';

const { width, height } = Dimensions.get('window');

interface OnboardingItem {
  id: string;
  icon: string;
  title: string;
  description: string;
  gradient: string[];
}

export const OnboardingScreen: React.FC = () => {
  const navigation = useNavigation();
  const { colors } = useTheme();
  const { t } = useI18n();
  const [currentIndex, setCurrentIndex] = useState(0);
  const flatListRef = useRef<FlatList>(null);
  const scrollX = useRef(new Animated.Value(0)).current;

  const onboardingData: OnboardingItem[] = [
    {
      id: '1',
      icon: 'school',
      title: t('onboarding.features.learn.title'),
      description: t('onboarding.features.learn.description'),
      gradient: [colors.primary, colors.secondary],
    },
    {
      id: '2',
      icon: 'security',
      title: t('onboarding.features.secure.title'),
      description: t('onboarding.features.secure.description'),
      gradient: [colors.secondary, colors.primary],
    },
    {
      id: '3',
      icon: 'trending-up',
      title: t('onboarding.features.progress.title'),
      description: t('onboarding.features.progress.description'),
      gradient: [colors.primary, colors.info],
    },
  ];

  const handleNext = () => {
    if (currentIndex < onboardingData.length - 1) {
      const nextIndex = currentIndex + 1;
      flatListRef.current?.scrollToIndex({ index: nextIndex, animated: true });
      setCurrentIndex(nextIndex);
    } else {
      completeOnboarding();
    }
  };

  const handleSkip = () => {
    completeOnboarding();
  };

  const completeOnboarding = async () => {
    try {
      StorageService.setBoolean(STORAGE_KEYS.ONBOARDING_COMPLETED, true);
      navigation.navigate('Auth' as never);
    } catch (error) {
      console.error('Error completing onboarding:', error);
    }
  };

  const renderOnboardingItem = ({ item }: { item: OnboardingItem }) => (
    <LinearGradient colors={item.gradient} style={styles.slide}>
      <View style={styles.content}>
        <View style={styles.iconContainer}>
          <Icon name={item.icon} size={80} color={colors.background} />
        </View>
        
        <View style={styles.textContainer}>
          <Text style={[styles.title, { color: colors.background }]}>
            {item.title}
          </Text>
          <Text style={[styles.description, { color: colors.background }]}>
            {item.description}
          </Text>
        </View>
      </View>
    </LinearGradient>
  );

  const renderPagination = () => (
    <View style={styles.pagination}>
      {onboardingData.map((_, index) => {
        const inputRange = [
          (index - 1) * width,
          index * width,
          (index + 1) * width,
        ];

        const dotWidth = scrollX.interpolate({
          inputRange,
          outputRange: [8, 20, 8],
          extrapolate: 'clamp',
        });

        const opacity = scrollX.interpolate({
          inputRange,
          outputRange: [0.3, 1, 0.3],
          extrapolate: 'clamp',
        });

        return (
          <Animated.View
            key={index}
            style={[
              styles.dot,
              {
                width: dotWidth,
                opacity,
                backgroundColor: colors.background,
              },
            ]}
          />
        );
      })}
    </View>
  );

  const onViewableItemsChanged = useRef(({ viewableItems }: any) => {
    if (viewableItems.length > 0) {
      setCurrentIndex(viewableItems[0].index);
    }
  }).current;

  const viewabilityConfig = useRef({
    itemVisiblePercentThreshold: 50,
  }).current;

  return (
    <View style={styles.container}>
      <FlatList
        ref={flatListRef}
        data={onboardingData}
        renderItem={renderOnboardingItem}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        onScroll={Animated.event(
          [{ nativeEvent: { contentOffset: { x: scrollX } } }],
          { useNativeDriver: false }
        )}
        onViewableItemsChanged={onViewableItemsChanged}
        viewabilityConfig={viewabilityConfig}
        keyExtractor={(item) => item.id}
      />

      <View style={[styles.footer, { backgroundColor: colors.background }]}>
        {renderPagination()}
        
        <View style={styles.buttons}>
          <TouchableOpacity
            style={[styles.skipButton, { borderColor: colors.textSecondary }]}
            onPress={handleSkip}
          >
            <Text style={[styles.skipText, { color: colors.textSecondary }]}>
              {t('common.skip')}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.nextButton, { backgroundColor: colors.primary }]}
            onPress={handleNext}
          >
            <Text style={[styles.nextText, { color: colors.background }]}>
              {currentIndex === onboardingData.length - 1
                ? t('onboarding.getStarted')
                : t('common.next')}
            </Text>
            <Icon
              name="arrow-forward"
              size={20}
              color={colors.background}
              style={styles.nextIcon}
            />
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  slide: {
    width,
    height: height * 0.75,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  iconContainer: {
    marginBottom: 40,
    padding: 20,
    borderRadius: 50,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  textContainer: {
    alignItems: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
  },
  description: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 24,
    opacity: 0.9,
  },
  footer: {
    height: height * 0.25,
    paddingHorizontal: 20,
    paddingTop: 20,
    justifyContent: 'space-between',
  },
  pagination: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
  },
  dot: {
    height: 8,
    borderRadius: 4,
    marginHorizontal: 4,
  },
  buttons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  skipButton: {
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderWidth: 1,
    borderRadius: 25,
  },
  skipText: {
    fontSize: 16,
    fontWeight: '500',
  },
  nextButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 25,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  nextText: {
    fontSize: 16,
    fontWeight: '600',
  },
  nextIcon: {
    marginLeft: 8,
  },
});
