import React, { useState } from 'react'
import { Routes, Route } from 'react-router-dom'
import { motion } from 'framer-motion'
import {
  BellIcon,
  PaperAirplaneIcon,
  ClockIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  PlusIcon,
} from '@heroicons/react/24/outline'
import { DataTable } from '../components/DataTable'
import { NotificationModal } from '../components/modals/NotificationModal'

export const NotificationsPage: React.FC = () => {
  const [showNotificationModal, setShowNotificationModal] = useState(false)
  const [selectedNotification, setSelectedNotification] = useState<any>(null)

  const columns = [
    { key: 'title', header: 'Title', sortable: true },
    { key: 'type', header: 'Type' },
    { key: 'audience', header: 'Audience' },
    { key: 'sent', header: 'Sent' },
    { key: 'delivered', header: 'Delivered' },
    { key: 'opened', header: 'Opened' },
    { key: 'scheduledAt', header: 'Scheduled', sortable: true },
    { key: 'status', header: 'Status' },
    { key: 'actions', header: 'Actions' },
  ]

  const mockNotifications = [
    {
      id: '1',
      title: 'New JEE Mock Test Available',
      type: 'Course Update',
      audience: 'JEE Students (1,247)',
      sent: '1,247',
      delivered: '1,243',
      opened: '892',
      scheduledAt: '2024-01-15 10:00',
      status: 'Sent',
    },
    {
      id: '2',
      title: 'Payment Reminder - Course Fee Due',
      type: 'Payment',
      audience: 'Pending Payments (89)',
      sent: '89',
      delivered: '87',
      opened: '45',
      scheduledAt: '2024-01-16 09:00',
      status: 'Scheduled',
    },
    {
      id: '3',
      title: 'Live Class Tomorrow - Physics',
      type: 'Class Reminder',
      audience: 'Physics Batch (450)',
      sent: '450',
      delivered: '447',
      opened: '398',
      scheduledAt: '2024-01-14 18:00',
      status: 'Sent',
    },
  ]

  const stats = [
    { name: 'Total Sent', value: '12,847', icon: PaperAirplaneIcon, color: 'bg-blue-500' },
    { name: 'Delivery Rate', value: '98.7%', icon: CheckCircleIcon, color: 'bg-green-500' },
    { name: 'Open Rate', value: '67.3%', icon: BellIcon, color: 'bg-purple-500' },
    { name: 'Scheduled', value: '23', icon: ClockIcon, color: 'bg-orange-500' },
  ]

  const notificationTypes = [
    {
      name: 'Course Updates',
      count: 156,
      color: 'bg-blue-100 text-blue-800',
      icon: '📚',
    },
    {
      name: 'Payment Reminders',
      count: 89,
      color: 'bg-yellow-100 text-yellow-800',
      icon: '💳',
    },
    {
      name: 'Class Reminders',
      count: 234,
      color: 'bg-green-100 text-green-800',
      icon: '⏰',
    },
    {
      name: 'System Alerts',
      count: 12,
      color: 'bg-red-100 text-red-800',
      icon: '⚠️',
    },
  ]

  return (
    <Routes>
      <Route path="/" element={
        <div className="space-y-6">
          {/* Header */}
          <div className="md:flex md:items-center md:justify-between">
            <div className="flex-1 min-w-0">
              <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
                Notification Center
              </h2>
              <p className="mt-1 text-sm text-gray-500">
                Send targeted notifications and manage communication campaigns
              </p>
            </div>
            <div className="mt-4 flex md:mt-0 md:ml-4 space-x-2">
              <button className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                <ClockIcon className="-ml-1 mr-2 h-5 w-5" />
                Scheduled
              </button>
              <button
                onClick={() => setShowNotificationModal(true)}
                className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700"
              >
                <PlusIcon className="-ml-1 mr-2 h-5 w-5" />
                Create Notification
              </button>
            </div>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {stats.map((stat, index) => (
              <motion.div
                key={stat.name}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className="bg-white rounded-lg p-6 shadow-sm border border-gray-200"
              >
                <div className="flex items-center">
                  <div className={`flex-shrink-0 p-3 rounded-md ${stat.color}`}>
                    <stat.icon className="h-6 w-6 text-white" />
                  </div>
                  <div className="ml-5">
                    <p className="text-sm font-medium text-gray-500">{stat.name}</p>
                    <p className="text-2xl font-semibold text-gray-900">{stat.value}</p>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>

          {/* Notification Types */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
          >
            <h3 className="text-lg font-medium text-gray-900 mb-4">Notification Types (Last 30 Days)</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {notificationTypes.map((type) => (
                <div key={type.name} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <span className="text-2xl">{type.icon}</span>
                    <div>
                      <p className="text-sm font-medium text-gray-900">{type.name}</p>
                      <p className="text-sm text-gray-500">{type.count} sent</p>
                    </div>
                  </div>
                  <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${type.color}`}>
                    Active
                  </span>
                </div>
              ))}
            </div>
          </motion.div>

          {/* Quick Actions */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
          >
            <h3 className="text-lg font-medium text-gray-900 mb-4">Quick Actions</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <button className="flex items-center justify-center p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-primary-500 hover:bg-primary-50 transition-colors">
                <div className="text-center">
                  <BellIcon className="mx-auto h-8 w-8 text-gray-400" />
                  <span className="mt-2 block text-sm font-medium text-gray-900">
                    Bulk Announcement
                  </span>
                </div>
              </button>
              <button className="flex items-center justify-center p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-primary-500 hover:bg-primary-50 transition-colors">
                <div className="text-center">
                  <ExclamationTriangleIcon className="mx-auto h-8 w-8 text-gray-400" />
                  <span className="mt-2 block text-sm font-medium text-gray-900">
                    System Alert
                  </span>
                </div>
              </button>
              <button className="flex items-center justify-center p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-primary-500 hover:bg-primary-50 transition-colors">
                <div className="text-center">
                  <ClockIcon className="mx-auto h-8 w-8 text-gray-400" />
                  <span className="mt-2 block text-sm font-medium text-gray-900">
                    Schedule Campaign
                  </span>
                </div>
              </button>
            </div>
          </motion.div>

          {/* Filters */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <input
                type="text"
                placeholder="Search notifications..."
                className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              />
              <select className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                <option value="">All Types</option>
                <option value="course">Course Updates</option>
                <option value="payment">Payment Reminders</option>
                <option value="class">Class Reminders</option>
                <option value="system">System Alerts</option>
              </select>
              <select className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                <option value="">All Status</option>
                <option value="sent">Sent</option>
                <option value="scheduled">Scheduled</option>
                <option value="draft">Draft</option>
                <option value="failed">Failed</option>
              </select>
              <select className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                <option value="">All Audiences</option>
                <option value="all-students">All Students</option>
                <option value="jee-students">JEE Students</option>
                <option value="neet-students">NEET Students</option>
                <option value="teachers">Teachers</option>
              </select>
            </div>
          </div>

          {/* Notifications Table */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            <DataTable
              columns={columns}
              data={mockNotifications}
              onRowEdit={(notification) => {
                setSelectedNotification(notification)
                setShowNotificationModal(true)
              }}
            />
          </div>

          {/* Notification Modal */}
          <NotificationModal
            open={showNotificationModal}
            onClose={() => {
              setShowNotificationModal(false)
              setSelectedNotification(null)
            }}
            notification={selectedNotification}
          />
        </div>
      } />
    </Routes>
  )
}
