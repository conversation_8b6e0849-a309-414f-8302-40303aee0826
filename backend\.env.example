# Server Configuration
PORT=3000
NODE_ENV=development

# Database
MONGODB_URI=mongodb://localhost:27017/rjwu_edutech
MONGODB_TEST_URI=mongodb://localhost:27017/rjwu_edutech_test

# JWT
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRE=7d
JWT_REFRESH_SECRET=your-refresh-token-secret
JWT_REFRESH_EXPIRE=30d

# Redis
REDIS_URL=redis://localhost:6379

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
FROM_EMAIL=<EMAIL>
FROM_NAME=RJWU EduTech

# File Upload
MAX_FILE_SIZE=50MB
UPLOAD_PATH=uploads/

# Payment Gateway
RAZORPAY_KEY_ID=your-razorpay-key
RAZORPAY_KEY_SECRET=your-razorpay-secret

# Content Security
WATERMARK_TEXT=RJWU EduTech
DRM_SECRET=your-drm-secret

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Session
SESSION_SECRET=your-session-secret

# CORS
FRONTEND_URL=http://localhost:3001
ADMIN_URL=http://localhost:3002
