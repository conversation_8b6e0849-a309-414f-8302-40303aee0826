import React, { useState } from 'react'
import { Dialog, Transition } from '@headlessui/react'
import { useForm } from 'react-hook-form'
import { 
  XMarkIcon, 
  ChatBubbleLeftRightIcon,
  PaperClipIcon,
  UserIcon 
} from '@heroicons/react/24/outline'
import { LoadingSpinner } from '../LoadingSpinner'

interface TicketModalProps {
  open: boolean
  onClose: () => void
  ticket?: any
}

interface TicketForm {
  subject: string
  description: string
  category: string
  priority: string
  assignTo: string
  status: string
  tags: string
}

export const TicketModal: React.FC<TicketModalProps> = ({ open, onClose, ticket }) => {
  const isEditing = !!ticket
  const [loading, setLoading] = useState(false)
  const [activeTab, setActiveTab] = useState<'details' | 'conversation'>('details')
  
  const { register, handleSubmit, formState: { errors }, reset } = useForm<TicketForm>({
    defaultValues: {
      category: 'technical',
      priority: 'medium',
      status: 'open',
    }
  })

  React.useEffect(() => {
    if (ticket) {
      reset({
        subject: ticket.subject,
        description: ticket.description,
        category: ticket.category?.toLowerCase(),
        priority: ticket.priority?.toLowerCase(),
        assignTo: ticket.assignedTo,
        status: ticket.status?.toLowerCase(),
        tags: ticket.tags?.join(', ') || '',
      })
    } else {
      reset({
        category: 'technical',
        priority: 'medium',
        status: 'open',
      })
    }
  }, [ticket, reset])

  const onSubmit = async (data: TicketForm) => {
    setLoading(true)
    try {
      // API call to create/update ticket
      console.log('Ticket data:', data)
      await new Promise(resolve => setTimeout(resolve, 1000)) // Mock API call
      onClose()
    } catch (error) {
      console.error('Error saving ticket:', error)
    } finally {
      setLoading(false)
    }
  }

  const mockConversation = [
    {
      id: 1,
      author: 'Rahul Kumar',
      role: 'Student',
      message: 'I am unable to access video lectures for JEE Mathematics. When I click on the video, it shows an error message.',
      timestamp: '2024-01-15 10:30 AM',
      avatar: null,
    },
    {
      id: 2,
      author: 'Support Team',
      role: 'Support',
      message: 'Hi Rahul, thank you for contacting us. Can you please try clearing your browser cache and cookies? Also, please let us know which browser you are using.',
      timestamp: '2024-01-15 11:15 AM',
      avatar: null,
    },
    {
      id: 3,
      author: 'Rahul Kumar',
      role: 'Student',
      message: 'I tried clearing cache but still having the same issue. I am using Chrome browser version 120.0.',
      timestamp: '2024-01-15 12:45 PM',
      avatar: null,
    },
  ]

  const getPriorityColor = (priority: string) => {
    switch (priority?.toLowerCase()) {
      case 'urgent':
        return 'bg-red-100 text-red-800'
      case 'high':
        return 'bg-orange-100 text-orange-800'
      case 'medium':
        return 'bg-yellow-100 text-yellow-800'
      case 'low':
        return 'bg-green-100 text-green-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'open':
        return 'bg-blue-100 text-blue-800'
      case 'in progress':
        return 'bg-yellow-100 text-yellow-800'
      case 'resolved':
        return 'bg-green-100 text-green-800'
      case 'closed':
        return 'bg-gray-100 text-gray-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <Transition.Root show={open} as={React.Fragment}>
      <Dialog as="div" className="relative z-50" onClose={onClose}>
        <Transition.Child
          as={React.Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
        </Transition.Child>

        <div className="fixed inset-0 z-10 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <Transition.Child
              as={React.Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
              enterTo="opacity-100 translate-y-0 sm:scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 translate-y-0 sm:scale-100"
              leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            >
              <Dialog.Panel className="relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-5xl sm:p-6">
                <div className="absolute right-0 top-0 hidden pr-4 pt-4 sm:block">
                  <button
                    type="button"
                    className="rounded-md bg-white text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
                    onClick={onClose}
                  >
                    <XMarkIcon className="h-6 w-6" />
                  </button>
                </div>

                <div className="sm:flex sm:items-start">
                  <div className="mx-auto flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-blue-100 sm:mx-0 sm:h-10 sm:w-10">
                    <ChatBubbleLeftRightIcon className="h-6 w-6 text-blue-600" />
                  </div>
                  <div className="mt-3 text-center sm:ml-4 sm:mt-0 sm:text-left w-full">
                    <Dialog.Title as="h3" className="text-base font-semibold leading-6 text-gray-900">
                      {isEditing ? `Ticket ${ticket.ticketId}` : 'Create New Ticket'}
                    </Dialog.Title>

                    {isEditing && (
                      <div className="mt-4 flex items-center space-x-4">
                        <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${getPriorityColor(ticket.priority)}`}>
                          {ticket.priority} Priority
                        </span>
                        <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(ticket.status)}`}>
                          {ticket.status}
                        </span>
                        <span className="text-sm text-gray-500">
                          Created: {ticket.createdAt}
                        </span>
                      </div>
                    )}

                    {/* Tabs */}
                    {isEditing && (
                      <div className="mt-6">
                        <div className="border-b border-gray-200">
                          <nav className="-mb-px flex space-x-8">
                            <button
                              onClick={() => setActiveTab('details')}
                              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                                activeTab === 'details'
                                  ? 'border-primary-500 text-primary-600'
                                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                              }`}
                            >
                              Ticket Details
                            </button>
                            <button
                              onClick={() => setActiveTab('conversation')}
                              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                                activeTab === 'conversation'
                                  ? 'border-primary-500 text-primary-600'
                                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                              }`}
                            >
                              Conversation
                            </button>
                          </nav>
                        </div>
                      </div>
                    )}

                    {/* Content */}
                    <div className="mt-6">
                      {(!isEditing || activeTab === 'details') && (
                        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
                          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                            <div className="lg:col-span-2">
                              <label htmlFor="subject" className="block text-sm font-medium text-gray-700">
                                Subject
                              </label>
                              <input
                                {...register('subject', { required: 'Subject is required' })}
                                type="text"
                                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                                placeholder="Brief description of the issue"
                              />
                              {errors.subject && (
                                <p className="mt-1 text-sm text-red-600">{errors.subject.message}</p>
                              )}
                            </div>

                            <div>
                              <label htmlFor="category" className="block text-sm font-medium text-gray-700">
                                Category
                              </label>
                              <select
                                {...register('category', { required: 'Category is required' })}
                                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                              >
                                <option value="technical">Technical Issues</option>
                                <option value="billing">Billing & Payments</option>
                                <option value="content">Course Content</option>
                                <option value="account">Account Issues</option>
                                <option value="other">Other</option>
                              </select>
                            </div>

                            <div>
                              <label htmlFor="priority" className="block text-sm font-medium text-gray-700">
                                Priority
                              </label>
                              <select
                                {...register('priority', { required: 'Priority is required' })}
                                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                              >
                                <option value="low">Low</option>
                                <option value="medium">Medium</option>
                                <option value="high">High</option>
                                <option value="urgent">Urgent</option>
                              </select>
                            </div>

                            {isEditing && (
                              <>
                                <div>
                                  <label htmlFor="assignTo" className="block text-sm font-medium text-gray-700">
                                    Assign To
                                  </label>
                                  <select
                                    {...register('assignTo')}
                                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                                  >
                                    <option value="">Unassigned</option>
                                    <option value="support-team">Support Team</option>
                                    <option value="finance-team">Finance Team</option>
                                    <option value="admin-team">Admin Team</option>
                                    <option value="technical-team">Technical Team</option>
                                  </select>
                                </div>

                                <div>
                                  <label htmlFor="status" className="block text-sm font-medium text-gray-700">
                                    Status
                                  </label>
                                  <select
                                    {...register('status')}
                                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                                  >
                                    <option value="open">Open</option>
                                    <option value="in-progress">In Progress</option>
                                    <option value="resolved">Resolved</option>
                                    <option value="closed">Closed</option>
                                  </select>
                                </div>
                              </>
                            )}

                            <div className="lg:col-span-2">
                              <label htmlFor="description" className="block text-sm font-medium text-gray-700">
                                Description
                              </label>
                              <textarea
                                {...register('description', { required: 'Description is required' })}
                                rows={4}
                                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                                placeholder="Detailed description of the issue or request..."
                              />
                              {errors.description && (
                                <p className="mt-1 text-sm text-red-600">{errors.description.message}</p>
                              )}
                            </div>

                            <div className="lg:col-span-2">
                              <label htmlFor="tags" className="block text-sm font-medium text-gray-700">
                                Tags (comma separated)
                              </label>
                              <input
                                {...register('tags')}
                                type="text"
                                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                                placeholder="video, payment, access"
                              />
                            </div>
                          </div>

                          <div className="mt-5 sm:mt-4 sm:flex sm:flex-row-reverse">
                            <button
                              type="submit"
                              disabled={loading}
                              className="inline-flex w-full justify-center rounded-md bg-primary-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-primary-500 sm:ml-3 sm:w-auto disabled:opacity-50"
                            >
                              {loading ? (
                                <LoadingSpinner size="sm" />
                              ) : (
                                isEditing ? 'Update Ticket' : 'Create Ticket'
                              )}
                            </button>
                            <button
                              type="button"
                              className="mt-3 inline-flex w-full justify-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 sm:mt-0 sm:w-auto"
                              onClick={onClose}
                            >
                              Cancel
                            </button>
                          </div>
                        </form>
                      )}

                      {isEditing && activeTab === 'conversation' && (
                        <div className="space-y-4">
                          {/* Conversation History */}
                          <div className="max-h-96 overflow-y-auto space-y-4">
                            {mockConversation.map((message) => (
                              <div key={message.id} className="flex space-x-3">
                                <div className="flex-shrink-0">
                                  <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                                    <UserIcon className="h-4 w-4 text-gray-600" />
                                  </div>
                                </div>
                                <div className="flex-1">
                                  <div className="flex items-center space-x-2">
                                    <span className="text-sm font-medium text-gray-900">{message.author}</span>
                                    <span className="text-xs text-gray-500">({message.role})</span>
                                    <span className="text-xs text-gray-500">{message.timestamp}</span>
                                  </div>
                                  <div className="mt-1 bg-gray-50 rounded-lg p-3">
                                    <p className="text-sm text-gray-700">{message.message}</p>
                                  </div>
                                </div>
                              </div>
                            ))}
                          </div>

                          {/* Reply Form */}
                          <div className="border-t pt-4">
                            <label htmlFor="reply" className="block text-sm font-medium text-gray-700 mb-2">
                              Add Reply
                            </label>
                            <textarea
                              rows={3}
                              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                              placeholder="Type your reply..."
                            />
                            <div className="mt-3 flex items-center justify-between">
                              <button className="inline-flex items-center px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-700 bg-white hover:bg-gray-50">
                                <PaperClipIcon className="h-4 w-4 mr-1" />
                                Attach File
                              </button>
                              <button className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700">
                                Send Reply
                              </button>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition.Root>
  )
}
