import { api } from './api';
import { SecurityService } from './security';
import { StorageService } from './storage';
import { User } from '@/types';
import { STORAGE_KEYS } from '@/constants';

interface LoginCredentials {
  email: string;
  password: string;
}

interface RegisterData {
  email: string;
  password: string;
  name: string;
  role: string;
}

interface AuthResponse {
  user: User;
  token: string;
  refreshToken: string;
}

export class AuthService {
  static async login(credentials: LoginCredentials): Promise<AuthResponse> {
    try {
      const response = await api.post<AuthResponse>('/auth/login', credentials);
      
      if (response.success) {
        await this.storeAuthData(response.data);
        return response.data;
      }
      
      throw new Error(response.error || 'Login failed');
    } catch (error) {
      throw error;
    }
  }

  static async loginWithBiometric(): Promise<AuthResponse> {
    try {
      // Check if biometric is available and enabled
      const isBiometricAvailable = await SecurityService.isBiometricAvailable();
      if (!isBiometricAvailable) {
        throw new Error('Biometric authentication not available');
      }

      // Get stored credentials
      const storedCredentials = await SecurityService.getBiometricCredentials();
      if (!storedCredentials) {
        throw new Error('No biometric credentials stored');
      }

      // Prompt for biometric authentication
      const biometricResult = await SecurityService.authenticateWithBiometric(
        'Use your biometric to sign in'
      );

      if (!biometricResult.success) {
        throw new Error(biometricResult.error || 'Biometric authentication failed');
      }

      // Use stored credentials to login
      const response = await api.post<AuthResponse>('/auth/biometric-login', {
        credentials: storedCredentials,
        biometricToken: biometricResult.signature,
      });

      if (response.success) {
        await this.storeAuthData(response.data);
        return response.data;
      }

      throw new Error(response.error || 'Biometric login failed');
    } catch (error) {
      throw error;
    }
  }

  static async register(userData: RegisterData): Promise<AuthResponse> {
    try {
      const response = await api.post<AuthResponse>('/auth/register', userData);
      
      if (response.success) {
        await this.storeAuthData(response.data);
        return response.data;
      }
      
      throw new Error(response.error || 'Registration failed');
    } catch (error) {
      throw error;
    }
  }

  static async refreshToken(): Promise<AuthResponse> {
    try {
      const refreshToken = await StorageService.getSecureItem(STORAGE_KEYS.USER_TOKEN + '_refresh');
      
      if (!refreshToken) {
        throw new Error('No refresh token available');
      }

      const response = await api.post<AuthResponse>('/auth/refresh', {
        refreshToken,
      });

      if (response.success) {
        await this.storeAuthData(response.data);
        return response.data;
      }

      throw new Error(response.error || 'Token refresh failed');
    } catch (error) {
      throw error;
    }
  }

  static async logout(): Promise<void> {
    try {
      // Call logout endpoint
      await api.post('/auth/logout');
    } catch (error) {
      console.warn('Logout request failed:', error);
    } finally {
      // Clear stored data regardless of API call result
      await this.clearAuthData();
    }
  }

  static async changePassword(
    currentPassword: string,
    newPassword: string
  ): Promise<void> {
    try {
      const response = await api.post('/auth/change-password', {
        currentPassword,
        newPassword,
      });

      if (!response.success) {
        throw new Error(response.error || 'Password change failed');
      }
    } catch (error) {
      throw error;
    }
  }

  static async resetPassword(email: string): Promise<void> {
    try {
      const response = await api.post('/auth/reset-password', { email });

      if (!response.success) {
        throw new Error(response.error || 'Password reset failed');
      }
    } catch (error) {
      throw error;
    }
  }

  static async verifyEmail(token: string): Promise<void> {
    try {
      const response = await api.post('/auth/verify-email', { token });

      if (!response.success) {
        throw new Error(response.error || 'Email verification failed');
      }
    } catch (error) {
      throw error;
    }
  }

  static async updateProfile(updates: Partial<User>): Promise<User> {
    try {
      const response = await api.patch<User>('/auth/profile', updates);

      if (response.success) {
        // Update stored user data
        await StorageService.setSecureItem(STORAGE_KEYS.USER_DATA, response.data);
        return response.data;
      }

      throw new Error(response.error || 'Profile update failed');
    } catch (error) {
      throw error;
    }
  }

  static async enableBiometric(credentials: LoginCredentials): Promise<void> {
    try {
      // Verify current credentials
      const verifyResponse = await api.post('/auth/verify-credentials', credentials);
      
      if (!verifyResponse.success) {
        throw new Error('Invalid credentials');
      }

      // Store credentials securely for biometric authentication
      await SecurityService.storeBiometricCredentials(credentials);
    } catch (error) {
      throw error;
    }
  }

  static async disableBiometric(): Promise<void> {
    try {
      await SecurityService.clearBiometricCredentials();
    } catch (error) {
      throw error;
    }
  }

  static async validateSession(): Promise<boolean> {
    try {
      const response = await api.get('/auth/validate-session');
      return response.success;
    } catch (error) {
      return false;
    }
  }

  static async getStoredUser(): Promise<User | null> {
    try {
      const userData = await StorageService.getSecureItem(STORAGE_KEYS.USER_DATA);
      return userData ? JSON.parse(userData) : null;
    } catch (error) {
      return null;
    }
  }

  static async getStoredToken(): Promise<string | null> {
    try {
      return await StorageService.getSecureItem(STORAGE_KEYS.USER_TOKEN);
    } catch (error) {
      return null;
    }
  }

  private static async storeAuthData(authData: AuthResponse): Promise<void> {
    try {
      await Promise.all([
        StorageService.setSecureItem(STORAGE_KEYS.USER_TOKEN, authData.token),
        StorageService.setSecureItem(STORAGE_KEYS.USER_TOKEN + '_refresh', authData.refreshToken),
        StorageService.setSecureItem(STORAGE_KEYS.USER_DATA, JSON.stringify(authData.user)),
      ]);
    } catch (error) {
      console.error('Failed to store auth data:', error);
      throw error;
    }
  }

  private static async clearAuthData(): Promise<void> {
    try {
      await Promise.all([
        StorageService.removeSecureItem(STORAGE_KEYS.USER_TOKEN),
        StorageService.removeSecureItem(STORAGE_KEYS.USER_TOKEN + '_refresh'),
        StorageService.removeSecureItem(STORAGE_KEYS.USER_DATA),
      ]);

      // Clear biometric credentials as well
      await SecurityService.clearBiometricCredentials();
    } catch (error) {
      console.error('Failed to clear auth data:', error);
    }
  }
}
