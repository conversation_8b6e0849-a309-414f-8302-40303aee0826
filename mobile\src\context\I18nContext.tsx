import React, { createContext, useContext, ReactNode } from 'react';
import I18n from 'react-native-i18n';
import { useAppSelector } from '@/hooks/redux';

// Import translations
import en from '@/locales/en.json';
import hi from '@/locales/hi.json';

I18n.translations = {
  en,
  hi,
};

I18n.fallbacks = true;
I18n.defaultLocale = 'en';

interface I18nContextType {
  t: (key: string, options?: any) => string;
  locale: string;
  isRTL: boolean;
}

const I18nContext = createContext<I18nContextType | undefined>(undefined);

interface I18nProviderProps {
  children: ReactNode;
}

export const I18nProvider: React.FC<I18nProviderProps> = ({ children }) => {
  const language = useAppSelector(state => state.settings.language);
  
  I18n.locale = language;
  
  const t = (key: string, options?: any): string => {
    return I18n.t(key, options);
  };

  const isRTL = language === 'ar' || language === 'he'; // Add RTL languages as needed

  const value = {
    t,
    locale: language,
    isRTL,
  };

  return (
    <I18nContext.Provider value={value}>
      {children}
    </I18nContext.Provider>
  );
};

export const useI18n = (): I18nContextType => {
  const context = useContext(I18nContext);
  if (!context) {
    throw new Error('useI18n must be used within an I18nProvider');
  }
  return context;
};
