import React, { useState } from 'react'
import { motion } from 'framer-motion'
import {
  ClipboardDocumentListIcon,
  UserIcon,
  CogIcon,
  ShieldCheckIcon,
  ExclamationTriangleIcon,
  FunnelIcon,
} from '@heroicons/react/24/outline'
import { DataTable } from '../components/DataTable'
import { format } from 'date-fns'

export const AuditLogsPage: React.FC = () => {
  const [selectedLogs, setSelectedLogs] = useState<string[]>([])
  const [filters, setFilters] = useState({
    action: '',
    user: '',
    dateFrom: '',
    dateTo: '',
    severity: '',
  })

  const columns = [
    { key: 'timestamp', header: 'Timestamp', sortable: true },
    { key: 'user', header: 'User' },
    { key: 'action', header: 'Action' },
    { key: 'resource', header: 'Resource' },
    { key: 'ipAddress', header: 'IP Address' },
    { key: 'userAgent', header: 'User Agent' },
    { key: 'severity', header: 'Severity' },
    { key: 'status', header: 'Status' },
  ]

  const mockLogs = [
    {
      id: '1',
      timestamp: '2024-01-15 14:30:25',
      user: '<EMAIL>',
      action: 'User Created',
      resource: 'User: <EMAIL>',
      ipAddress: '*************',
      userAgent: 'Chrome 120.0',
      severity: 'Info',
      status: 'Success',
    },
    {
      id: '2',
      timestamp: '2024-01-15 14:25:18',
      user: '<EMAIL>',
      action: 'Content Upload',
      resource: 'Video: JEE Mathematics Chapter 1',
      ipAddress: '*************',
      userAgent: 'Firefox 121.0',
      severity: 'Info',
      status: 'Success',
    },
    {
      id: '3',
      timestamp: '2024-01-15 14:20:45',
      user: 'system',
      action: 'Failed Login Attempt',
      resource: 'User: <EMAIL>',
      ipAddress: '*************',
      userAgent: 'curl/7.68.0',
      severity: 'Warning',
      status: 'Failed',
    },
    {
      id: '4',
      timestamp: '2024-01-15 14:15:33',
      user: '<EMAIL>',
      action: 'Payment Gateway Updated',
      resource: 'Gateway: Razorpay Settings',
      ipAddress: '*************',
      userAgent: 'Chrome 120.0',
      severity: 'High',
      status: 'Success',
    },
  ]

  const stats = [
    { name: 'Total Events', value: '45,827', icon: ClipboardDocumentListIcon, color: 'bg-blue-500' },
    { name: 'User Actions', value: '38,942', icon: UserIcon, color: 'bg-green-500' },
    { name: 'System Events', value: '5,234', icon: CogIcon, color: 'bg-purple-500' },
    { name: 'Security Events', value: '1,651', icon: ShieldCheckIcon, color: 'bg-red-500' },
  ]

  const actionTypes = [
    { name: 'User Management', count: 1247, percentage: 28 },
    { name: 'Content Operations', count: 967, percentage: 22 },
    { name: 'Payment Processing', count: 543, percentage: 12 },
    { name: 'System Configuration', count: 432, percentage: 10 },
    { name: 'Authentication', count: 1234, percentage: 28 },
  ]

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="md:flex md:items-center md:justify-between">
        <div className="flex-1 min-w-0">
          <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
            Audit Logs
          </h2>
          <p className="mt-1 text-sm text-gray-500">
            Monitor system activity, user actions, and security events
          </p>
        </div>
        <div className="mt-4 flex md:mt-0 md:ml-4 space-x-2">
          <button className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
            <FunnelIcon className="-ml-1 mr-2 h-5 w-5" />
            Advanced Filter
          </button>
          <button className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700">
            <ClipboardDocumentListIcon className="-ml-1 mr-2 h-5 w-5" />
            Export Logs
          </button>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <motion.div
            key={stat.name}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            className="bg-white rounded-lg p-6 shadow-sm border border-gray-200"
          >
            <div className="flex items-center">
              <div className={`flex-shrink-0 p-3 rounded-md ${stat.color}`}>
                <stat.icon className="h-6 w-6 text-white" />
              </div>
              <div className="ml-5">
                <p className="text-sm font-medium text-gray-500">{stat.name}</p>
                <p className="text-2xl font-semibold text-gray-900">{stat.value}</p>
              </div>
            </div>
          </motion.div>
        ))}
      </div>

      {/* Action Types Overview */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
        className="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
      >
        <h3 className="text-lg font-medium text-gray-900 mb-6">Activity Breakdown (Last 30 Days)</h3>
        <div className="space-y-4">
          {actionTypes.map((type, index) => (
            <div key={type.name} className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <span className="text-sm font-medium text-gray-900">{type.name}</span>
                <span className="text-sm text-gray-500">({type.count} events)</span>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-32 bg-gray-200 rounded-full h-2">
                  <motion.div
                    initial={{ width: 0 }}
                    animate={{ width: `${type.percentage}%` }}
                    transition={{ delay: index * 0.1 + 0.5, duration: 0.8 }}
                    className="bg-primary-600 h-2 rounded-full"
                  />
                </div>
                <span className="text-sm font-medium text-gray-900 w-12">{type.percentage}%</span>
              </div>
            </div>
          ))}
        </div>
      </motion.div>

      {/* Recent Security Events */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3 }}
        className="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
      >
        <h3 className="text-lg font-medium text-gray-900 mb-4">Recent Security Events</h3>
        <div className="space-y-3">
          {[
            {
              event: 'Multiple failed login attempts',
              user: 'Unknown',
              ip: '*************',
              time: '2 minutes ago',
              severity: 'high',
            },
            {
              event: 'Admin privilege escalation',
              user: '<EMAIL>',
              ip: '*************',
              time: '15 minutes ago',
              severity: 'medium',
            },
            {
              event: 'Bulk data export',
              user: '<EMAIL>',
              ip: '*************',
              time: '1 hour ago',
              severity: 'low',
            },
          ].map((event, index) => (
            <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center space-x-3">
                <div className={`w-3 h-3 rounded-full ${
                  event.severity === 'high' ? 'bg-red-500' :
                  event.severity === 'medium' ? 'bg-yellow-500' : 'bg-green-500'
                }`} />
                <div>
                  <p className="text-sm font-medium text-gray-900">{event.event}</p>
                  <p className="text-xs text-gray-500">{event.user} from {event.ip}</p>
                </div>
              </div>
              <span className="text-xs text-gray-500">{event.time}</span>
            </div>
          ))}
        </div>
      </motion.div>

      {/* Filters */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Filter Logs</h3>
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
          <input
            type="text"
            placeholder="Search logs..."
            className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          />
          <select
            value={filters.action}
            onChange={(e) => setFilters(prev => ({ ...prev, action: e.target.value }))}
            className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          >
            <option value="">All Actions</option>
            <option value="login">Login/Logout</option>
            <option value="user">User Management</option>
            <option value="content">Content Operations</option>
            <option value="payment">Payment Processing</option>
            <option value="system">System Configuration</option>
          </select>
          <select
            value={filters.severity}
            onChange={(e) => setFilters(prev => ({ ...prev, severity: e.target.value }))}
            className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          >
            <option value="">All Severity</option>
            <option value="info">Info</option>
            <option value="warning">Warning</option>
            <option value="high">High</option>
            <option value="critical">Critical</option>
          </select>
          <input
            type="date"
            value={filters.dateFrom}
            onChange={(e) => setFilters(prev => ({ ...prev, dateFrom: e.target.value }))}
            className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          />
          <input
            type="date"
            value={filters.dateTo}
            onChange={(e) => setFilters(prev => ({ ...prev, dateTo: e.target.value }))}
            className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          />
        </div>
      </div>

      {/* Bulk Actions */}
      {selectedLogs.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-blue-50 border border-blue-200 rounded-lg p-4"
        >
          <div className="flex items-center justify-between">
            <span className="text-sm text-blue-700">
              {selectedLogs.length} log{selectedLogs.length > 1 ? 's' : ''} selected
            </span>
            <div className="flex space-x-2">
              <button className="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700">
                Export Selected
              </button>
              <button className="px-3 py-1 text-sm bg-red-600 text-white rounded hover:bg-red-700">
                Archive
              </button>
            </div>
          </div>
        </motion.div>
      )}

      {/* Logs Table */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <DataTable
          columns={columns}
          data={mockLogs}
          onRowSelect={setSelectedLogs}
          selectedRows={selectedLogs}
        />
      </div>
    </div>
  )
}
