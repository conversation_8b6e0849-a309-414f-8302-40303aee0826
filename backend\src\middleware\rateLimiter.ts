import rateLimit from 'express-rate-limit';
import { Request, Response } from 'express';

const createRateLimiter = (windowMs: number, max: number, message?: string) => {
  return rateLimit({
    windowMs,
    max,
    message: {
      success: false,
      message: message || 'Too many requests, please try again later',
      error: 'RATE_LIMIT_EXCEEDED'
    },
    standardHeaders: true,
    legacyHeaders: false,
    keyGenerator: (req: Request) => {
      return req.ip || 'anonymous';
    },
    handler: (req: Request, res: Response) => {
      res.status(429).json({
        success: false,
        message: message || 'Too many requests, please try again later',
        error: 'RATE_LIMIT_EXCEEDED'
      });
    }
  });
};

export const generalLimiter = createRateLimiter(
  parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000'), // 15 minutes
  parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100'), // 100 requests per window
  'Too many requests from this IP, please try again later'
);

export const authLimiter = createRateLimiter(
  15 * 60 * 1000, // 15 minutes
  5, // 5 attempts per window
  'Too many authentication attempts, please try again after 15 minutes'
);

export const forgotPasswordLimiter = createRateLimiter(
  60 * 60 * 1000, // 1 hour
  3, // 3 attempts per hour
  'Too many password reset requests, please try again after 1 hour'
);

export const strictLimiter = createRateLimiter(
  5 * 60 * 1000, // 5 minutes
  10, // 10 requests per window
  'Too many requests, please slow down'
);

export const uploadLimiter = createRateLimiter(
  60 * 60 * 1000, // 1 hour
  20, // 20 uploads per hour
  'Too many file uploads, please try again later'
);
