import { Types } from 'mongoose';
import LiveClassPoll, { ILiveClassPoll } from '../models/LiveClassPoll';
import LiveClassParticipant from '../models/LiveClassParticipant';
import LiveClass from '../models/LiveClass';

export class LiveClassPollService {
  async createPoll(data: {
    liveClassId: string;
    createdBy: string;
    question: string;
    type: 'multiple-choice' | 'single-choice' | 'text' | 'rating' | 'yes-no';
    options: Array<{ text: string }>;
    settings?: {
      allowMultipleAnswers?: boolean;
      showResultsImmediately?: boolean;
      allowAnonymous?: boolean;
      timeLimit?: number;
      correctAnswer?: number;
      points?: number;
    };
  }): Promise<ILiveClassPoll> {
    const { liveClassId, createdBy, question, type, options, settings = {} } = data;

    // Check if user can create polls
    const participant = await LiveClassParticipant.findOne({
      liveClass: liveClassId,
      user: createdBy,
      status: 'joined'
    });

    if (!participant || !participant.permissions.canCreatePolls) {
      throw new Error('User not authorized to create polls');
    }

    // Check if polls are enabled
    const liveClass = await LiveClass.findById(liveClassId);
    if (!liveClass || !liveClass.pollSettings.enabled) {
      throw new Error('Polls are disabled for this live class');
    }

    const poll = new LiveClassPoll({
      liveClass: liveClassId,
      createdBy,
      question,
      type,
      options: options.map(opt => ({
        text: opt.text,
        votes: 0,
        voters: []
      })),
      settings: {
        allowMultipleAnswers: type === 'multiple-choice' ? settings.allowMultipleAnswers : false,
        showResultsImmediately: settings.showResultsImmediately ?? true,
        allowAnonymous: settings.allowAnonymous ?? liveClass.pollSettings.allowAnonymous,
        timeLimit: settings.timeLimit,
        correctAnswer: settings.correctAnswer,
        points: settings.points || 0
      }
    });

    await poll.save();
    await poll.populate('createdBy', 'name avatar role');

    return poll;
  }

  async startPoll(pollId: string, userId: string): Promise<ILiveClassPoll | null> {
    const poll = await LiveClassPoll.findById(pollId);
    if (!poll) return null;

    // Check if user can start polls
    const participant = await LiveClassParticipant.findOne({
      liveClass: poll.liveClass,
      user: userId,
      status: 'joined'
    });

    if (!participant || !participant.permissions.canCreatePolls) {
      throw new Error('User not authorized to start polls');
    }

    poll.status = 'active';
    poll.startTime = new Date();
    
    if (poll.settings.timeLimit) {
      poll.endTime = new Date(Date.now() + poll.settings.timeLimit * 1000);
    }

    await poll.save();
    return poll;
  }

  async closePoll(pollId: string, userId: string): Promise<ILiveClassPoll | null> {
    const poll = await LiveClassPoll.findById(pollId);
    if (!poll) return null;

    // Check if user can close polls
    const participant = await LiveClassParticipant.findOne({
      liveClass: poll.liveClass,
      user: userId,
      status: 'joined'
    });

    if (!participant || !participant.permissions.canCreatePolls) {
      throw new Error('User not authorized to close polls');
    }

    poll.status = 'closed';
    poll.endTime = new Date();
    await poll.save();

    // Calculate analytics
    await this.calculatePollAnalytics(poll);

    return poll;
  }

  async submitResponse(data: {
    pollId: string;
    userId: string;
    answer: string | number | number[];
  }): Promise<boolean> {
    const { pollId, userId, answer } = data;

    const poll = await LiveClassPoll.findById(pollId);
    if (!poll) {
      throw new Error('Poll not found');
    }

    if (poll.status !== 'active') {
      throw new Error('Poll is not active');
    }

    // Check if poll has expired
    if (poll.endTime && poll.endTime < new Date()) {
      throw new Error('Poll has expired');
    }

    // Check if user is participant
    const participant = await LiveClassParticipant.findOne({
      liveClass: poll.liveClass,
      user: userId,
      status: 'joined'
    });

    if (!participant || !participant.permissions.canViewPolls) {
      throw new Error('User not authorized to participate in polls');
    }

    // Check if user has already responded
    const existingResponse = poll.responses.find(r => r.user.toString() === userId);
    if (existingResponse && poll.type !== 'multiple-choice') {
      throw new Error('User has already responded to this poll');
    }

    // Validate answer based on poll type
    const isValidAnswer = this.validateAnswer(poll, answer);
    if (!isValidAnswer) {
      throw new Error('Invalid answer format');
    }

    let isCorrect = false;
    let pointsEarned = 0;

    // Check if answer is correct (for quiz-type polls)
    if (poll.settings.correctAnswer !== undefined) {
      if (poll.type === 'single-choice' || poll.type === 'yes-no') {
        isCorrect = answer === poll.settings.correctAnswer;
      } else if (poll.type === 'multiple-choice' && Array.isArray(answer)) {
        isCorrect = answer.includes(poll.settings.correctAnswer);
      }

      if (isCorrect) {
        pointsEarned = poll.settings.points || 0;
      }
    }

    // Add or update response
    if (existingResponse) {
      existingResponse.answer = answer;
      existingResponse.submittedAt = new Date();
      existingResponse.isCorrect = isCorrect;
      existingResponse.pointsEarned = pointsEarned;
    } else {
      poll.responses.push({
        user: new Types.ObjectId(userId),
        answer,
        submittedAt: new Date(),
        isCorrect,
        pointsEarned
      });
    }

    // Update vote counts for option-based polls
    if (poll.type === 'single-choice' || poll.type === 'yes-no') {
      const optionIndex = answer as number;
      if (poll.options[optionIndex]) {
        // Remove previous vote if updating
        if (existingResponse && typeof existingResponse.answer === 'number') {
          const prevIndex = existingResponse.answer as number;
          if (poll.options[prevIndex]) {
            poll.options[prevIndex].votes = Math.max(0, poll.options[prevIndex].votes - 1);
            const voterIndex = poll.options[prevIndex].voters.findIndex(v => v.toString() === userId);
            if (voterIndex > -1) {
              poll.options[prevIndex].voters.splice(voterIndex, 1);
            }
          }
        }

        poll.options[optionIndex].votes++;
        if (!poll.options[optionIndex].voters.includes(new Types.ObjectId(userId))) {
          poll.options[optionIndex].voters.push(new Types.ObjectId(userId));
        }
      }
    } else if (poll.type === 'multiple-choice' && Array.isArray(answer)) {
      // Handle multiple choice updates
      if (existingResponse && Array.isArray(existingResponse.answer)) {
        // Remove previous votes
        (existingResponse.answer as number[]).forEach(prevIndex => {
          if (poll.options[prevIndex]) {
            poll.options[prevIndex].votes = Math.max(0, poll.options[prevIndex].votes - 1);
            const voterIndex = poll.options[prevIndex].voters.findIndex(v => v.toString() === userId);
            if (voterIndex > -1) {
              poll.options[prevIndex].voters.splice(voterIndex, 1);
            }
          }
        });
      }

      // Add new votes
      answer.forEach(optionIndex => {
        if (poll.options[optionIndex]) {
          poll.options[optionIndex].votes++;
          if (!poll.options[optionIndex].voters.includes(new Types.ObjectId(userId))) {
            poll.options[optionIndex].voters.push(new Types.ObjectId(userId));
          }
        }
      });
    }

    await poll.save();
    return true;
  }

  async getPollResults(pollId: string, userId: string): Promise<{
    poll: ILiveClassPoll;
    results: any;
    userResponse?: any;
  } | null> {
    const poll = await LiveClassPoll.findById(pollId)
      .populate('createdBy', 'name avatar role');

    if (!poll) return null;

    // Check if user can view results
    const participant = await LiveClassParticipant.findOne({
      liveClass: poll.liveClass,
      user: userId,
      status: 'joined'
    });

    if (!participant || !participant.permissions.canViewPolls) {
      throw new Error('User not authorized to view poll results');
    }

    // Check if results should be shown
    if (!poll.settings.showResultsImmediately && poll.status === 'active') {
      const isCreator = poll.createdBy._id.toString() === userId;
      const isModerator = participant.permissions.canCreatePolls;
      
      if (!isCreator && !isModerator) {
        throw new Error('Poll results not available yet');
      }
    }

    const userResponse = poll.responses.find(r => r.user.toString() === userId);
    const results = this.formatPollResults(poll);

    return {
      poll,
      results,
      userResponse: userResponse || null
    };
  }

  async getLiveClassPolls(liveClassId: string, options: {
    status?: 'draft' | 'active' | 'closed' | 'archived';
    limit?: number;
    skip?: number;
  } = {}): Promise<ILiveClassPoll[]> {
    const { status, limit = 50, skip = 0 } = options;

    const query: any = { liveClass: liveClassId };
    if (status) {
      query.status = status;
    }

    return LiveClassPoll.find(query)
      .populate('createdBy', 'name avatar role')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);
  }

  async deletePoll(pollId: string, userId: string): Promise<boolean> {
    const poll = await LiveClassPoll.findById(pollId);
    if (!poll) return false;

    // Check if user can delete polls
    const isCreator = poll.createdBy.toString() === userId;
    
    if (!isCreator) {
      const participant = await LiveClassParticipant.findOne({
        liveClass: poll.liveClass,
        user: userId,
        status: 'joined'
      });

      if (!participant || !participant.permissions.canCreatePolls) {
        throw new Error('User not authorized to delete this poll');
      }
    }

    await LiveClassPoll.findByIdAndDelete(pollId);
    return true;
  }

  async getPollAnalytics(liveClassId: string): Promise<{
    totalPolls: number;
    activePolls: number;
    averageResponseRate: number;
    totalResponses: number;
    popularPollTypes: Array<{ type: string; count: number }>;
  }> {
    const polls = await LiveClassPoll.find({ liveClass: liveClassId });
    
    const totalPolls = polls.length;
    const activePolls = polls.filter(p => p.status === 'active').length;
    const totalResponses = polls.reduce((sum, poll) => sum + poll.responses.length, 0);
    
    const participantCount = await LiveClassParticipant.countDocuments({
      liveClass: liveClassId,
      status: 'joined'
    });

    const averageResponseRate = participantCount > 0 
      ? (totalResponses / (totalPolls * participantCount)) * 100 
      : 0;

    const pollTypeCounts: { [key: string]: number } = {};
    polls.forEach(poll => {
      pollTypeCounts[poll.type] = (pollTypeCounts[poll.type] || 0) + 1;
    });

    const popularPollTypes = Object.entries(pollTypeCounts)
      .map(([type, count]) => ({ type, count }))
      .sort((a, b) => b.count - a.count);

    return {
      totalPolls,
      activePolls,
      averageResponseRate: Math.round(averageResponseRate * 100) / 100,
      totalResponses,
      popularPollTypes
    };
  }

  private validateAnswer(poll: ILiveClassPoll, answer: string | number | number[]): boolean {
    switch (poll.type) {
      case 'single-choice':
      case 'yes-no':
        return typeof answer === 'number' && answer >= 0 && answer < poll.options.length;
      
      case 'multiple-choice':
        return Array.isArray(answer) && 
               answer.every(a => typeof a === 'number' && a >= 0 && a < poll.options.length) &&
               answer.length > 0;
      
      case 'text':
        return typeof answer === 'string' && answer.trim().length > 0;
      
      case 'rating':
        return typeof answer === 'number' && answer >= 1 && answer <= 5;
      
      default:
        return false;
    }
  }

  private formatPollResults(poll: ILiveClassPoll): any {
    const totalResponses = poll.responses.length;
    
    if (poll.type === 'text') {
      return {
        totalResponses,
        responses: poll.settings.allowAnonymous 
          ? poll.responses.map(r => ({ answer: r.answer, submittedAt: r.submittedAt }))
          : poll.responses
      };
    }

    if (poll.type === 'rating') {
      const ratings = poll.responses.map(r => r.answer as number);
      const averageRating = ratings.length > 0 
        ? ratings.reduce((sum, rating) => sum + rating, 0) / ratings.length 
        : 0;

      return {
        totalResponses,
        averageRating: Math.round(averageRating * 100) / 100,
        distribution: this.getRatingDistribution(ratings)
      };
    }

    // For choice-based polls
    const results = poll.options.map((option, index) => {
      const percentage = totalResponses > 0 ? (option.votes / totalResponses) * 100 : 0;
      return {
        index,
        text: option.text,
        votes: option.votes,
        percentage: Math.round(percentage * 100) / 100
      };
    });

    return {
      totalResponses,
      options: results,
      correctAnswer: poll.settings.correctAnswer,
      correctResponses: poll.responses.filter(r => r.isCorrect).length
    };
  }

  private getRatingDistribution(ratings: number[]): { [rating: number]: number } {
    const distribution: { [rating: number]: number } = { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 };
    ratings.forEach(rating => {
      distribution[rating] = (distribution[rating] || 0) + 1;
    });
    return distribution;
  }

  private async calculatePollAnalytics(poll: ILiveClassPoll): Promise<void> {
    const totalParticipants = await LiveClassParticipant.countDocuments({
      liveClass: poll.liveClass,
      status: { $in: ['joined', 'left'] }
    });

    const totalResponses = poll.responses.length;
    const responseRate = totalParticipants > 0 ? (totalResponses / totalParticipants) * 100 : 0;
    
    const responseTimes = poll.responses
      .filter(r => poll.startTime)
      .map(r => (r.submittedAt.getTime() - poll.startTime!.getTime()) / 1000);
    
    const averageResponseTime = responseTimes.length > 0
      ? responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length
      : 0;

    const correctAnswers = poll.responses.filter(r => r.isCorrect).length;

    poll.analytics = {
      totalResponses,
      responseRate: Math.round(responseRate * 100) / 100,
      averageResponseTime: Math.round(averageResponseTime * 100) / 100,
      correctAnswers
    };

    await poll.save();
  }
}

export default new LiveClassPollService();
