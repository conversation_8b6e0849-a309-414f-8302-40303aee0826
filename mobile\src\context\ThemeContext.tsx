import React, { createContext, useContext, ReactNode } from 'react';
import { useAppSelector } from '@/hooks/redux';
import { THEME_COLORS } from '@/constants';

interface ThemeContextType {
  theme: 'light' | 'dark';
  colors: typeof THEME_COLORS.light;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

interface ThemeProviderProps {
  children: ReactNode;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  const theme = useAppSelector(state => state.settings.theme);
  const colors = THEME_COLORS[theme];

  const value = {
    theme,
    colors,
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
};

export const useTheme = (): ThemeContextType => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};
