import { DownloadItem } from '@/types';
import { StorageService } from './storage';
import { SecurityService } from './security';
import RNFS from 'react-native-fs';

export class DownloadService {
  private static downloads = new Map<string, DownloadItem>();
  private static isInitialized = false;

  static async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      // Load existing downloads from storage
      const savedDownloads = StorageService.getObject<DownloadItem[]>('downloads') || [];
      savedDownloads.forEach(download => {
        this.downloads.set(download.id, download);
      });

      this.isInitialized = true;
      console.log('Download service initialized');
    } catch (error) {
      console.error('Download service initialization failed:', error);
    }
  }

  static async startDownload(item: Omit<DownloadItem, 'id' | 'progress' | 'status' | 'createdAt'>): Promise<string> {
    const downloadId = Date.now().toString();
    const downloadItem: DownloadItem = {
      ...item,
      id: downloadId,
      progress: 0,
      status: 'pending',
      downloadedSize: 0,
      createdAt: new Date(),
    };

    this.downloads.set(downloadId, downloadItem);
    await this.saveDownloads();

    // Start actual download
    this.performDownload(downloadId);

    return downloadId;
  }

  private static async performDownload(downloadId: string): Promise<void> {
    const download = this.downloads.get(downloadId);
    if (!download) return;

    try {
      download.status = 'downloading';
      await this.saveDownloads();

      const fileName = `${downloadId}_${download.title.replace(/[^a-zA-Z0-9]/g, '_')}`;
      const filePath = `${RNFS.DocumentDirectoryPath}/downloads/${fileName}`;

      // Ensure download directory exists
      await RNFS.mkdir(`${RNFS.DocumentDirectoryPath}/downloads`);

      // Download with progress tracking
      const downloadResult = await RNFS.downloadFile({
        fromUrl: download.url,
        toFile: filePath,
        progress: (res) => {
          const progress = (res.bytesWritten / res.contentLength) * 100;
          download.progress = progress;
          download.downloadedSize = res.bytesWritten;
          // Emit progress update event
        },
        progressDivider: 10,
      }).promise;

      if (downloadResult.statusCode === 200) {
        // Encrypt downloaded file
        const encryptedPath = await this.encryptDownloadedFile(filePath);
        
        download.status = 'completed';
        download.progress = 100;
        download.filePath = encryptedPath;
        
        // Remove original unencrypted file
        await RNFS.unlink(filePath);
      } else {
        throw new Error(`Download failed with status: ${downloadResult.statusCode}`);
      }
    } catch (error) {
      download.status = 'error';
      console.error('Download failed:', error);
    }

    await this.saveDownloads();
  }

  private static async encryptDownloadedFile(filePath: string): Promise<string> {
    try {
      // Read file content
      const fileContent = await RNFS.readFile(filePath, 'base64');
      
      // Encrypt content
      const encryptedContent = await SecurityService.encryptData(fileContent);
      
      // Write encrypted file
      const encryptedPath = `${filePath}.encrypted`;
      await RNFS.writeFile(encryptedPath, encryptedContent, 'utf8');
      
      return encryptedPath;
    } catch (error) {
      console.error('File encryption failed:', error);
      return filePath; // Return original path if encryption fails
    }
  }

  static async pauseDownload(downloadId: string): Promise<void> {
    const download = this.downloads.get(downloadId);
    if (download) {
      download.status = 'paused';
      await this.saveDownloads();
    }
  }

  static async resumeDownload(downloadId: string): Promise<void> {
    const download = this.downloads.get(downloadId);
    if (download) {
      download.status = 'downloading';
      await this.saveDownloads();
      // Resume download logic
      this.performDownload(downloadId);
    }
  }

  static async cancelDownload(downloadId: string): Promise<void> {
    const download = this.downloads.get(downloadId);
    if (download) {
      // Delete partial file if exists
      if (download.filePath) {
        try {
          await RNFS.unlink(download.filePath);
        } catch (error) {
          console.error('Failed to delete file:', error);
        }
      }

      this.downloads.delete(downloadId);
      await this.saveDownloads();
    }
  }

  static async deleteDownload(downloadId: string): Promise<void> {
    const download = this.downloads.get(downloadId);
    if (download) {
      // Delete downloaded file
      if (download.filePath) {
        try {
          await RNFS.unlink(download.filePath);
        } catch (error) {
          console.error('Failed to delete file:', error);
        }
      }

      this.downloads.delete(downloadId);
      await this.saveDownloads();
    }
  }

  static async retryDownload(downloadId: string): Promise<void> {
    const download = this.downloads.get(downloadId);
    if (download) {
      download.status = 'pending';
      download.progress = 0;
      download.downloadedSize = 0;
      await this.saveDownloads();
      this.performDownload(downloadId);
    }
  }

  static async getDownloads(): Promise<DownloadItem[]> {
    return Array.from(this.downloads.values());
  }

  static async clearCompletedDownloads(): Promise<void> {
    const completedDownloads = Array.from(this.downloads.values())
      .filter(download => download.status === 'completed');

    for (const download of completedDownloads) {
      this.downloads.delete(download.id);
    }

    await this.saveDownloads();
  }

  static async resumeAllDownloads(): Promise<void> {
    const pausedDownloads = Array.from(this.downloads.values())
      .filter(download => download.status === 'paused' || download.status === 'pending');

    for (const download of pausedDownloads) {
      this.performDownload(download.id);
    }
  }

  static async pauseAllDownloads(): Promise<void> {
    const activeDownloads = Array.from(this.downloads.values())
      .filter(download => download.status === 'downloading');

    for (const download of activeDownloads) {
      download.status = 'paused';
    }

    await this.saveDownloads();
  }

  private static async saveDownloads(): Promise<void> {
    try {
      const downloadsArray = Array.from(this.downloads.values());
      StorageService.setObject('downloads', downloadsArray);
    } catch (error) {
      console.error('Failed to save downloads:', error);
    }
  }
}
