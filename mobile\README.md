# RJWU EduTech Mobile App

A comprehensive React Native mobile application for the RJWU EduTech platform with advanced security features, offline capabilities, and native performance.

## Features

### 🔐 Security Features
- Biometric authentication (fingerprint/face recognition)
- Root/jailbreak detection
- Screenshot and screen recording prevention
- Certificate pinning for API calls
- Video watermarking
- Session management and device binding
- Secure offline storage with encryption

### 📱 Core Functionality
- User authentication with multiple methods
- Course browsing and enrollment
- Video streaming with security controls
- Offline content download and management
- Progress tracking and analytics
- Push notifications
- Multi-language support (English/Hindi)
- Dark/light theme support

### 🎥 Video Features
- Secure video player with DRM protection
- Offline video playback
- Video quality selection
- Subtitles support
- Playback speed control
- Watermarking overlay
- Screenshot/recording prevention

### 📥 Download Management
- Background downloads
- Resume/pause functionality
- WiFi-only download option
- Download quality selection
- Storage management
- Encrypted offline storage

## Architecture

### Technology Stack
- **Framework**: React Native 0.72
- **Language**: TypeScript
- **State Management**: Redux Toolkit
- **Navigation**: React Navigation v6
- **Storage**: MMKV (encrypted)
- **Security**: Custom native modules
- **Networking**: Fetch with certificate pinning
- **Offline**: Redux Persist + MMKV

### Project Structure
```
src/
├── components/          # Reusable UI components
├── screens/            # Screen components
├── navigation/         # Navigation configuration
├── services/           # API and utility services
├── store/             # Redux store and slices
├── hooks/             # Custom React hooks
├── context/           # React context providers
├── types/             # TypeScript type definitions
├── constants/         # App constants and configuration
├── utils/             # Utility functions
├── native/            # Native module interfaces
└── locales/           # Internationalization files
```

### Security Implementation
- Device fingerprinting
- Runtime application self-protection (RASP)
- Anti-tampering mechanisms
- Secure communication with SSL pinning
- Encrypted local storage
- Biometric authentication integration

## Installation

### Prerequisites
- Node.js >= 16
- React Native CLI
- Android Studio (for Android)
- Xcode (for iOS)

### Setup
```bash
# Install dependencies
npm install

# iOS setup
cd ios && pod install && cd ..

# Android setup
# Ensure Android SDK is properly configured

# Start Metro bundler
npm start

# Run on Android
npm run android

# Run on iOS
npm run ios
```

### Environment Configuration
Create environment-specific configuration files:
- `.env.development`
- `.env.staging`
- `.env.production`

## Security Configuration

### Certificate Pinning
Update certificate pins in `src/constants/index.ts`:
```typescript
CERTIFICATE_PINS: [
  'sha256/YOUR_CERTIFICATE_PIN_HERE',
  'sha256/BACKUP_CERTIFICATE_PIN_HERE',
]
```

### Native Modules
Implement platform-specific security modules:
- `SecurityModule.android.js`
- `SecurityModule.ios.js`

## Build & Deployment

### Android
```bash
# Debug build
npm run android

# Release build
npm run build:android
```

### iOS
```bash
# Debug build
npm run ios

# Release build
npm run build:ios
```

## Testing

```bash
# Run tests
npm test

# Run tests with coverage
npm run test:coverage

# Run linting
npm run lint

# Run type checking
npm run type-check
```

## Performance Optimization

### Implemented Optimizations
- Code splitting and lazy loading
- Image optimization and caching
- Memory management for large video files
- Network request optimization
- Bundle size optimization
- Native performance optimization

### Monitoring
- Crash reporting
- Performance monitoring
- User analytics (privacy-compliant)
- Security violation tracking

## Native Modules

### Security Module
Provides native security features:
- Root/jailbreak detection
- Screenshot prevention
- Screen recording detection
- Certificate validation
- Device fingerprinting

### Video Security Module
Handles video-specific security:
- Watermark overlay
- Playback protection
- DRM integration
- Secure video rendering

## Offline Capabilities

### Supported Offline Features
- Downloaded course content
- Offline video playback
- Progress synchronization
- Cached user data
- Offline authentication (biometric)

### Sync Strategy
- Background sync when online
- Conflict resolution
- Delta updates
- Bandwidth optimization

## Accessibility

### WCAG Compliance
- Screen reader support
- High contrast mode
- Font size adjustment
- Voice control support
- Keyboard navigation

### Localization
- Right-to-left (RTL) support
- Number formatting
- Date/time formatting
- Currency formatting

## Contributing

### Development Guidelines
1. Follow TypeScript best practices
2. Use functional components with hooks
3. Implement proper error handling
4. Write unit tests for new features
5. Follow security best practices
6. Document complex functionality

### Code Style
- ESLint + Prettier configuration
- TypeScript strict mode
- Consistent naming conventions
- Comprehensive type definitions

## License

Private - RJWU EduTech Platform

## Support

For technical support and documentation:
- Email: <EMAIL>
- Documentation: Internal wiki
- Issue tracking: Internal system
