import { google } from 'googleapis';

export interface YouTubeLiveBroadcast {
  id: string;
  title: string;
  description: string;
  scheduledStartTime: Date;
  scheduledEndTime: Date;
  streamKey: string;
  streamUrl: string;
  broadcastId: string;
}

export interface YouTubeRecordingData {
  url: string;
  duration: number;
  fileSize: number;
  format: string;
}

export class YouTubeService {
  private youtube: any;

  constructor() {
    this.youtube = google.youtube({
      version: 'v3',
      auth: process.env.YOUTUBE_API_KEY
    });
  }

  async createLiveBroadcast(data: {
    title: string;
    description: string;
    scheduledStartTime: Date;
    scheduledEndTime: Date;
  }): Promise<YouTubeLiveBroadcast> {
    try {
      // Create broadcast
      const broadcastResponse = await this.youtube.liveBroadcasts.insert({
        part: ['snippet', 'status'],
        requestBody: {
          snippet: {
            title: data.title,
            description: data.description,
            scheduledStartTime: data.scheduledStartTime.toISOString(),
            scheduledEndTime: data.scheduledEndTime.toISOString()
          },
          status: {
            privacyStatus: 'unlisted',
            selfDeclaredMadeForKids: false
          }
        }
      });

      const broadcast = broadcastResponse.data;

      // Create live stream
      const streamResponse = await this.youtube.liveStreams.insert({
        part: ['snippet', 'cdn'],
        requestBody: {
          snippet: {
            title: `${data.title} - Stream`
          },
          cdn: {
            format: '1080p',
            ingestionType: 'rtmp'
          }
        }
      });

      const stream = streamResponse.data;

      // Bind stream to broadcast
      await this.youtube.liveBroadcasts.bind({
        part: ['id'],
        id: broadcast.id,
        streamId: stream.id
      });

      return {
        id: stream.id,
        title: data.title,
        description: data.description,
        scheduledStartTime: data.scheduledStartTime,
        scheduledEndTime: data.scheduledEndTime,
        streamKey: stream.cdn.ingestionInfo.streamName,
        streamUrl: stream.cdn.ingestionInfo.ingestionAddress,
        broadcastId: broadcast.id
      };
    } catch (error) {
      console.error('Error creating YouTube live broadcast:', error);
      throw new Error('Failed to create YouTube live broadcast');
    }
  }

  async startRecording(broadcastId: string): Promise<void> {
    try {
      await this.youtube.liveBroadcasts.transition({
        part: ['status'],
        id: broadcastId,
        broadcastStatus: 'live'
      });
    } catch (error) {
      console.error('Error starting YouTube recording:', error);
      throw new Error('Failed to start YouTube recording');
    }
  }

  async stopRecording(broadcastId: string): Promise<YouTubeRecordingData> {
    try {
      // End the broadcast
      await this.youtube.liveBroadcasts.transition({
        part: ['status'],
        id: broadcastId,
        broadcastStatus: 'complete'
      });

      // Get the video details
      const videoResponse = await this.youtube.videos.list({
        part: ['snippet', 'contentDetails', 'fileDetails'],
        id: broadcastId
      });

      const video = videoResponse.data.items[0];
      
      // Parse duration from ISO 8601 format (PT1H2M3S)
      const duration = this.parseDuration(video.contentDetails.duration);
      
      return {
        url: `https://www.youtube.com/watch?v=${broadcastId}`,
        duration,
        fileSize: video.fileDetails?.fileSize || 0,
        format: 'mp4'
      };
    } catch (error) {
      console.error('Error stopping YouTube recording:', error);
      throw new Error('Failed to stop YouTube recording');
    }
  }

  async getBroadcastStatus(broadcastId: string): Promise<string> {
    try {
      const response = await this.youtube.liveBroadcasts.list({
        part: ['status'],
        id: broadcastId
      });

      return response.data.items[0]?.status?.lifeCycleStatus || 'unknown';
    } catch (error) {
      console.error('Error getting broadcast status:', error);
      return 'error';
    }
  }

  async updateBroadcast(broadcastId: string, data: {
    title?: string;
    description?: string;
    scheduledStartTime?: Date;
    scheduledEndTime?: Date;
  }): Promise<void> {
    try {
      const updateData: any = {};
      
      if (data.title || data.description || data.scheduledStartTime || data.scheduledEndTime) {
        updateData.snippet = {};
        if (data.title) updateData.snippet.title = data.title;
        if (data.description) updateData.snippet.description = data.description;
        if (data.scheduledStartTime) updateData.snippet.scheduledStartTime = data.scheduledStartTime.toISOString();
        if (data.scheduledEndTime) updateData.snippet.scheduledEndTime = data.scheduledEndTime.toISOString();
      }

      await this.youtube.liveBroadcasts.update({
        part: ['snippet'],
        requestBody: {
          id: broadcastId,
          ...updateData
        }
      });
    } catch (error) {
      console.error('Error updating YouTube broadcast:', error);
      throw new Error('Failed to update YouTube broadcast');
    }
  }

  async deleteBroadcast(broadcastId: string): Promise<void> {
    try {
      await this.youtube.liveBroadcasts.delete({
        id: broadcastId
      });
    } catch (error) {
      console.error('Error deleting YouTube broadcast:', error);
      throw new Error('Failed to delete YouTube broadcast');
    }
  }

  private parseDuration(duration: string): number {
    // Parse ISO 8601 duration format (PT1H2M3S) to seconds
    const match = duration.match(/PT(?:(\d+)H)?(?:(\d+)M)?(?:(\d+)S)?/);
    if (!match) return 0;

    const hours = parseInt(match[1] || '0', 10);
    const minutes = parseInt(match[2] || '0', 10);
    const seconds = parseInt(match[3] || '0', 10);

    return hours * 3600 + minutes * 60 + seconds;
  }
}

export default YouTubeService;
