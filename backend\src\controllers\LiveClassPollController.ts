import { Response } from 'express';
import { AuthenticatedRequest } from '../types';
import LiveClassPollService from '../services/LiveClassPollService';
import { validationResult } from 'express-validator';

export class LiveClassPollController {
  async createPoll(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
        return;
      }

      const { id } = req.params;
      const { question, type, options, settings } = req.body;

      const poll = await LiveClassPollService.createPoll({
        liveClassId: id,
        createdBy: req.user!.id,
        question,
        type,
        options,
        settings
      });

      res.status(201).json({
        success: true,
        message: 'Poll created successfully',
        data: poll
      });
    } catch (error: any) {
      res.status(400).json({
        success: false,
        message: error.message
      });
    }
  }

  async startPoll(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { pollId } = req.params;
      const poll = await LiveClassPollService.startPoll(pollId, req.user!.id);

      if (!poll) {
        res.status(404).json({
          success: false,
          message: 'Poll not found'
        });
        return;
      }

      res.json({
        success: true,
        message: 'Poll started successfully',
        data: poll
      });
    } catch (error: any) {
      res.status(400).json({
        success: false,
        message: error.message
      });
    }
  }

  async closePoll(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { pollId } = req.params;
      const poll = await LiveClassPollService.closePoll(pollId, req.user!.id);

      if (!poll) {
        res.status(404).json({
          success: false,
          message: 'Poll not found'
        });
        return;
      }

      res.json({
        success: true,
        message: 'Poll closed successfully',
        data: poll
      });
    } catch (error: any) {
      res.status(400).json({
        success: false,
        message: error.message
      });
    }
  }

  async submitResponse(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { pollId } = req.params;
      const { answer } = req.body;

      await LiveClassPollService.submitResponse({
        pollId,
        userId: req.user!.id,
        answer
      });

      res.json({
        success: true,
        message: 'Response submitted successfully'
      });
    } catch (error: any) {
      res.status(400).json({
        success: false,
        message: error.message
      });
    }
  }

  async getPollResults(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { pollId } = req.params;
      const results = await LiveClassPollService.getPollResults(pollId, req.user!.id);

      if (!results) {
        res.status(404).json({
          success: false,
          message: 'Poll not found'
        });
        return;
      }

      res.json({
        success: true,
        message: 'Poll results retrieved successfully',
        data: results
      });
    } catch (error: any) {
      res.status(400).json({
        success: false,
        message: error.message
      });
    }
  }

  async getLiveClassPolls(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const { status, limit, skip } = req.query;

      const options = {
        status: status as any,
        limit: limit ? parseInt(limit as string) : undefined,
        skip: skip ? parseInt(skip as string) : undefined
      };

      const polls = await LiveClassPollService.getLiveClassPolls(id, options);

      res.json({
        success: true,
        message: 'Polls retrieved successfully',
        data: polls
      });
    } catch (error: any) {
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve polls',
        error: error.message
      });
    }
  }

  async deletePoll(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { pollId } = req.params;
      const deleted = await LiveClassPollService.deletePoll(pollId, req.user!.id);

      if (!deleted) {
        res.status(404).json({
          success: false,
          message: 'Poll not found'
        });
        return;
      }

      res.json({
        success: true,
        message: 'Poll deleted successfully'
      });
    } catch (error: any) {
      res.status(400).json({
        success: false,
        message: error.message
      });
    }
  }
}

export default new LiveClassPollController();
