import { Types } from 'mongoose';
import LiveClass, { ILiveClass } from '../models/LiveClass';
import LiveClassParticipant, { ILiveClassParticipant } from '../models/LiveClassParticipant';
import LiveClassRecording from '../models/LiveClassRecording';
import YouTubeService from './YouTubeService';
import WebRTCService from './WebRTCService';
import NotificationService from './NotificationService';
import { v4 as uuidv4 } from 'uuid';

export class LiveClassService {
  private youtubeService: YouTubeService;
  private webrtcService: WebRTCService;
  private notificationService: NotificationService;

  constructor() {
    this.youtubeService = new YouTubeService();
    this.webrtcService = new WebRTCService();
    this.notificationService = new NotificationService();
  }

  async createLiveClass(data: Partial<ILiveClass>): Promise<ILiveClass> {
    // Generate streaming configurations
    const streamingOptions: any = {};

    if (data.streamingOptions?.youtube) {
      const youtubeConfig = await this.youtubeService.createLiveBroadcast({
        title: data.title!,
        description: data.description!,
        scheduledStartTime: data.scheduledStart!,
        scheduledEndTime: data.scheduledEnd!
      });
      streamingOptions.youtube = youtubeConfig;
    }

    if (data.streamingOptions?.webrtc) {
      streamingOptions.webrtc = {
        roomId: uuidv4(),
        signalServer: process.env.WEBRTC_SIGNAL_SERVER || 'ws://localhost:3001'
      };
    }

    if (data.streamingOptions?.jitsi) {
      streamingOptions.jitsi = {
        roomName: `rjwu-${uuidv4()}`,
        domain: process.env.JITSI_DOMAIN || 'meet.jit.si'
      };
    }

    const liveClass = new LiveClass({
      ...data,
      streamingOptions,
      status: 'scheduled'
    });

    await liveClass.save();

    // Schedule notifications
    await this.scheduleNotifications(liveClass);

    return liveClass;
  }

  async updateLiveClass(id: string, data: Partial<ILiveClass>): Promise<ILiveClass | null> {
    const liveClass = await LiveClass.findByIdAndUpdate(
      id,
      { ...data, updatedAt: new Date() },
      { new: true, runValidators: true }
    );

    if (liveClass && data.scheduledStart) {
      // Reschedule notifications if start time changed
      await this.scheduleNotifications(liveClass);
    }

    return liveClass;
  }

  async startLiveClass(id: string, userId: string): Promise<ILiveClass | null> {
    const liveClass = await LiveClass.findById(id);
    if (!liveClass) return null;

    // Check if user is instructor
    if (liveClass.instructor.toString() !== userId) {
      throw new Error('Only the instructor can start the live class');
    }

    // Update status and actual start time
    liveClass.status = 'live';
    liveClass.actualStart = new Date();
    await liveClass.save();

    // Start recording if enabled
    if (liveClass.isRecorded) {
      await this.startRecording(liveClass);
    }

    // Notify enrolled participants
    await this.notificationService.notifyLiveClassStarted(liveClass);

    return liveClass;
  }

  async endLiveClass(id: string, userId: string): Promise<ILiveClass | null> {
    const liveClass = await LiveClass.findById(id);
    if (!liveClass) return null;

    // Check if user is instructor
    if (liveClass.instructor.toString() !== userId) {
      throw new Error('Only the instructor can end the live class');
    }

    // Update status and actual end time
    liveClass.status = 'ended';
    liveClass.actualEnd = new Date();
    await liveClass.save();

    // Stop recording if enabled
    if (liveClass.isRecorded) {
      await this.stopRecording(liveClass);
    }

    // Mark attendance for all active participants
    await this.markAttendanceForActiveParticipants(liveClass);

    // Calculate analytics
    await this.calculateAnalytics(liveClass);

    return liveClass;
  }

  async joinLiveClass(liveClassId: string, userId: string, connectionInfo: any): Promise<ILiveClassParticipant> {
    const liveClass = await LiveClass.findById(liveClassId);
    if (!liveClass) {
      throw new Error('Live class not found');
    }

    if (liveClass.status !== 'live') {
      throw new Error('Live class is not currently active');
    }

    // Check if user is already in the class
    let participant = await LiveClassParticipant.findOne({
      liveClass: liveClassId,
      user: userId,
      status: 'joined'
    });

    if (participant) {
      return participant;
    }

    // Create new participant
    participant = new LiveClassParticipant({
      liveClass: liveClassId,
      user: userId,
      joinedAt: new Date(),
      status: 'joined',
      connectionInfo
    });

    await participant.save();

    // Update live class analytics
    await this.updateParticipantCount(liveClass);

    return participant;
  }

  async leaveLiveClass(liveClassId: string, userId: string): Promise<void> {
    const participant = await LiveClassParticipant.findOne({
      liveClass: liveClassId,
      user: userId,
      status: 'joined'
    });

    if (participant) {
      participant.status = 'left';
      participant.leftAt = new Date();
      participant.attendance.duration = Math.floor(
        (Date.now() - participant.joinedAt.getTime()) / (1000 * 60)
      );
      await participant.save();
    }
  }

  async getParticipants(liveClassId: string): Promise<ILiveClassParticipant[]> {
    return LiveClassParticipant.find({
      liveClass: liveClassId,
      status: 'joined'
    }).populate('user', 'name email avatar');
  }

  async updateParticipantPermissions(
    liveClassId: string,
    userId: string,
    permissions: any,
    moderatorId: string
  ): Promise<void> {
    const participant = await LiveClassParticipant.findOne({
      liveClass: liveClassId,
      user: userId
    });

    if (participant) {
      participant.permissions = { ...participant.permissions, ...permissions };
      await participant.save();
    }
  }

  async removeParticipant(liveClassId: string, userId: string, moderatorId: string, reason?: string): Promise<void> {
    const participant = await LiveClassParticipant.findOne({
      liveClass: liveClassId,
      user: userId
    });

    if (participant) {
      participant.status = 'removed';
      participant.leftAt = new Date();
      participant.warnings.push({
        type: 'removal',
        reason: reason || 'Removed by moderator',
        issuedBy: new Types.ObjectId(moderatorId),
        issuedAt: new Date()
      });
      await participant.save();
    }
  }

  async getLiveClasses(filters: any = {}, pagination: any = {}): Promise<{
    classes: ILiveClass[];
    total: number;
    page: number;
    limit: number;
  }> {
    const { page = 1, limit = 10 } = pagination;
    const skip = (page - 1) * limit;

    const query = this.buildQuery(filters);
    
    const [classes, total] = await Promise.all([
      LiveClass.find(query)
        .populate('instructor', 'name email avatar')
        .populate('course', 'title')
        .sort({ scheduledStart: -1 })
        .skip(skip)
        .limit(limit),
      LiveClass.countDocuments(query)
    ]);

    return {
      classes,
      total,
      page,
      limit
    };
  }

  async getLiveClassById(id: string, userId?: string): Promise<ILiveClass | null> {
    const liveClass = await LiveClass.findById(id)
      .populate('instructor', 'name email avatar')
      .populate('course', 'title description')
      .populate('participantCount');

    if (!liveClass) return null;

    // Check access permissions
    if (userId && !await this.checkAccess(liveClass, userId)) {
      throw new Error('Access denied');
    }

    return liveClass;
  }

  private async scheduleNotifications(liveClass: ILiveClass): Promise<void> {
    // Schedule notification 24 hours before
    const oneDayBefore = new Date(liveClass.scheduledStart.getTime() - 24 * 60 * 60 * 1000);
    if (oneDayBefore > new Date()) {
      await this.notificationService.scheduleNotification({
        type: 'live_class_reminder',
        recipients: [], // Will be populated with enrolled students
        data: { liveClassId: liveClass._id, timeUntil: '24 hours' },
        scheduledFor: oneDayBefore
      });
    }

    // Schedule notification 1 hour before
    const oneHourBefore = new Date(liveClass.scheduledStart.getTime() - 60 * 60 * 1000);
    if (oneHourBefore > new Date()) {
      await this.notificationService.scheduleNotification({
        type: 'live_class_reminder',
        recipients: [],
        data: { liveClassId: liveClass._id, timeUntil: '1 hour' },
        scheduledFor: oneHourBefore
      });
    }

    // Schedule notification 15 minutes before
    const fifteenMinBefore = new Date(liveClass.scheduledStart.getTime() - 15 * 60 * 1000);
    if (fifteenMinBefore > new Date()) {
      await this.notificationService.scheduleNotification({
        type: 'live_class_starting',
        recipients: [],
        data: { liveClassId: liveClass._id, timeUntil: '15 minutes' },
        scheduledFor: fifteenMinBefore
      });
    }
  }

  private async startRecording(liveClass: ILiveClass): Promise<void> {
    // Implementation depends on streaming provider
    if (liveClass.streamingOptions.youtube) {
      await this.youtubeService.startRecording(liveClass.streamingOptions.youtube.broadcastId!);
    }
    // Add other recording implementations
  }

  private async stopRecording(liveClass: ILiveClass): Promise<void> {
    if (liveClass.streamingOptions.youtube) {
      const recordingData = await this.youtubeService.stopRecording(
        liveClass.streamingOptions.youtube.broadcastId!
      );
      
      // Create recording entry
      const recording = new LiveClassRecording({
        liveClass: liveClass._id,
        title: `${liveClass.title} - Recording`,
        recordingUrl: recordingData.url,
        duration: recordingData.duration,
        fileSize: recordingData.fileSize,
        format: recordingData.format,
        status: 'processing',
        watermark: {
          enabled: liveClass.recordingSettings.watermark,
          text: 'RJWU EduTech',
          position: 'bottom-right',
          opacity: 0.7
        },
        security: {
          encrypted: true,
          drm: true,
          accessControl: 'enrolled'
        },
        metadata: {
          chatIncluded: liveClass.recordingSettings.includeChat,
          pollsIncluded: liveClass.recordingSettings.includePolls,
          screenshareIncluded: true
        }
      });

      await recording.save();
      
      // Update live class with recording URL
      liveClass.recordingUrl = recording.recordingUrl;
      await liveClass.save();
    }
  }

  private async markAttendanceForActiveParticipants(liveClass: ILiveClass): Promise<void> {
    const activeParticipants = await LiveClassParticipant.find({
      liveClass: liveClass._id,
      status: 'joined'
    });

    const minimumDuration = liveClass.attendanceSettings.minimumDuration;

    for (const participant of activeParticipants) {
      const duration = Math.floor((Date.now() - participant.joinedAt.getTime()) / (1000 * 60));
      
      if (duration >= minimumDuration) {
        participant.attendance.marked = true;
        participant.attendance.markedAt = new Date();
        participant.attendance.duration = duration;
        await participant.save();
      }
    }
  }

  private async calculateAnalytics(liveClass: ILiveClass): Promise<void> {
    const participants = await LiveClassParticipant.find({ liveClass: liveClass._id });
    
    const totalJoined = participants.length;
    const peakConcurrent = await this.calculatePeakConcurrent(liveClass._id);
    const averageDuration = participants.reduce((sum, p) => sum + p.attendance.duration, 0) / totalJoined;
    const engagementScore = await this.calculateEngagementScore(liveClass._id);

    liveClass.analytics = {
      totalJoined,
      peakConcurrent,
      averageDuration,
      engagementScore
    };

    await liveClass.save();
  }

  private async calculatePeakConcurrent(liveClassId: string): Promise<number> {
    // This would require tracking concurrent participants over time
    // For now, return a placeholder
    return 0;
  }

  private async calculateEngagementScore(liveClassId: string): Promise<number> {
    // Calculate based on chat messages, poll participation, etc.
    // For now, return a placeholder
    return 0;
  }

  private async updateParticipantCount(liveClass: ILiveClass): Promise<void> {
    const currentCount = await LiveClassParticipant.countDocuments({
      liveClass: liveClass._id,
      status: 'joined'
    });

    if (currentCount > liveClass.analytics.peakConcurrent) {
      liveClass.analytics.peakConcurrent = currentCount;
      await liveClass.save();
    }
  }

  private buildQuery(filters: any): any {
    const query: any = {};

    if (filters.instructor) {
      query.instructor = filters.instructor;
    }

    if (filters.course) {
      query.course = filters.course;
    }

    if (filters.status) {
      query.status = filters.status;
    }

    if (filters.startDate && filters.endDate) {
      query.scheduledStart = {
        $gte: new Date(filters.startDate),
        $lte: new Date(filters.endDate)
      };
    }

    if (filters.tags && filters.tags.length > 0) {
      query['metadata.tags'] = { $in: filters.tags };
    }

    return query;
  }

  private async checkAccess(liveClass: ILiveClass, userId: string): Promise<boolean> {
    // Check if user is instructor
    if (liveClass.instructor.toString() === userId) {
      return true;
    }

    // Check if user is enrolled in the course
    // This would typically check enrollment status
    return true; // Placeholder
  }
}

export default new LiveClassService();
