export const APP_CONFIG = {
  API_BASE_URL: __DEV__ ? 'http://localhost:5000/api' : 'https://api.rjwu.edu/api',
  WS_BASE_URL: __DEV__ ? 'ws://localhost:5000' : 'wss://api.rjwu.edu',
  VERSION: '1.0.0',
  BUILD_NUMBER: 1,
};

export const STORAGE_KEYS = {
  USER_TOKEN: 'user_token',
  USER_DATA: 'user_data',
  THEME: 'theme',
  LANGUAGE: 'language',
  BIOMETRIC_ENABLED: 'biometric_enabled',
  ONBOARDING_COMPLETED: 'onboarding_completed',
  DOWNLOADED_COURSES: 'downloaded_courses',
  APP_SETTINGS: 'app_settings',
};

export const SECURITY_CONFIG = {
  MAX_LOGIN_ATTEMPTS: 5,
  SESSION_TIMEOUT: 30 * 60 * 1000, // 30 minutes
  CERTIFICATE_PINS: [
    'sha256/AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=',
    'sha256/BBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBB=',
  ],
  ROOT_DETECTION_ENABLED: true,
  SCREENSHOT_BLOCKING_ENABLED: true,
  SCREEN_RECORDING_BLOCKING_ENABLED: true,
  WATERMARK_ENABLED: true,
};

export const DOWNLOAD_CONFIG = {
  MAX_CONCURRENT_DOWNLOADS: 3,
  CHUNK_SIZE: 1024 * 1024, // 1MB
  RETRY_ATTEMPTS: 3,
  TIMEOUT: 30000,
  STORAGE_PATH: 'downloads',
};

export const VIDEO_CONFIG = {
  DEFAULT_QUALITY: 'medium',
  QUALITIES: {
    low: { width: 480, height: 270, bitrate: 500000 },
    medium: { width: 720, height: 405, bitrate: 1000000 },
    high: { width: 1080, height: 607, bitrate: 2000000 },
  },
  WATERMARK_CONFIG: {
    text: 'RJWU EduTech',
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.7)',
    position: 'bottom-right',
    margin: 20,
  },
};

export const THEME_COLORS = {
  light: {
    primary: '#2E86C1',
    secondary: '#F39C12',
    background: '#FFFFFF',
    surface: '#F8F9FA',
    text: '#2C3E50',
    textSecondary: '#7F8C8D',
    border: '#E5E5E5',
    error: '#E74C3C',
    success: '#27AE60',
    warning: '#F39C12',
    info: '#3498DB',
  },
  dark: {
    primary: '#3498DB',
    secondary: '#F39C12',
    background: '#1A1A1A',
    surface: '#2C2C2C',
    text: '#FFFFFF',
    textSecondary: '#BDC3C7',
    border: '#404040',
    error: '#E74C3C',
    success: '#27AE60',
    warning: '#F39C12',
    info: '#3498DB',
  },
};

export const ANIMATION_DURATION = {
  short: 200,
  medium: 300,
  long: 500,
};

export const SCREEN_SIZES = {
  small: 320,
  medium: 768,
  large: 1024,
};

export const NOTIFICATION_TYPES = {
  INFO: 'info',
  SUCCESS: 'success',
  WARNING: 'warning',
  ERROR: 'error',
} as const;

export const COURSE_CATEGORIES = [
  'Programming',
  'Mathematics',
  'Science',
  'Language',
  'Business',
  'Design',
  'Technology',
  'Engineering',
];

export const SUPPORTED_LANGUAGES = [
  { code: 'en', name: 'English', nativeName: 'English' },
  { code: 'hi', name: 'Hindi', nativeName: 'हिंदी' },
];

export const PERMISSIONS = {
  CAMERA: 'camera',
  MICROPHONE: 'microphone',
  STORAGE: 'storage',
  NOTIFICATIONS: 'notifications',
  LOCATION: 'location',
} as const;
