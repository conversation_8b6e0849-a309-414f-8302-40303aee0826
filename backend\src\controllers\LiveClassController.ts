import { Response } from 'express';
import { AuthenticatedRequest } from '../types';
import LiveClassService from '../services/LiveClassService';
import LiveClassChatService from '../services/LiveClassChatService';
import LiveClassPollService from '../services/LiveClassPollService';
import { validationResult } from 'express-validator';

export class LiveClassController {
  async createLiveClass(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
        return;
      }

      const liveClass = await LiveClassService.createLiveClass({
        ...req.body,
        instructor: req.user?.id
      });

      res.status(201).json({
        success: true,
        message: 'Live class created successfully',
        data: liveClass
      });
    } catch (error: any) {
      res.status(500).json({
        success: false,
        message: 'Failed to create live class',
        error: error.message
      });
    }
  }

  async updateLiveClass(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const liveClass = await LiveClassService.updateLiveClass(id, req.body);

      if (!liveClass) {
        res.status(404).json({
          success: false,
          message: 'Live class not found'
        });
        return;
      }

      res.json({
        success: true,
        message: 'Live class updated successfully',
        data: liveClass
      });
    } catch (error: any) {
      res.status(500).json({
        success: false,
        message: 'Failed to update live class',
        error: error.message
      });
    }
  }

  async getLiveClasses(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const {
        page = 1,
        limit = 10,
        instructor,
        course,
        status,
        startDate,
        endDate,
        tags
      } = req.query;

      const filters = {
        instructor,
        course,
        status,
        startDate,
        endDate,
        tags: tags ? (typeof tags === 'string' ? tags.split(',') : tags) : undefined
      };

      const pagination = {
        page: parseInt(page as string),
        limit: parseInt(limit as string)
      };

      const result = await LiveClassService.getLiveClasses(filters, pagination);

      res.json({
        success: true,
        message: 'Live classes retrieved successfully',
        data: result.classes,
        pagination: {
          page: result.page,
          limit: result.limit,
          total: result.total,
          totalPages: Math.ceil(result.total / result.limit)
        }
      });
    } catch (error: any) {
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve live classes',
        error: error.message
      });
    }
  }

  async getLiveClassById(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const liveClass = await LiveClassService.getLiveClassById(id, req.user?.id);

      if (!liveClass) {
        res.status(404).json({
          success: false,
          message: 'Live class not found'
        });
        return;
      }

      res.json({
        success: true,
        message: 'Live class retrieved successfully',
        data: liveClass
      });
    } catch (error: any) {
      if (error.message === 'Access denied') {
        res.status(403).json({
          success: false,
          message: 'Access denied'
        });
        return;
      }

      res.status(500).json({
        success: false,
        message: 'Failed to retrieve live class',
        error: error.message
      });
    }
  }

  async startLiveClass(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const liveClass = await LiveClassService.startLiveClass(id, req.user!.id);

      if (!liveClass) {
        res.status(404).json({
          success: false,
          message: 'Live class not found'
        });
        return;
      }

      res.json({
        success: true,
        message: 'Live class started successfully',
        data: liveClass
      });
    } catch (error: any) {
      res.status(403).json({
        success: false,
        message: error.message
      });
    }
  }

  async endLiveClass(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const liveClass = await LiveClassService.endLiveClass(id, req.user!.id);

      if (!liveClass) {
        res.status(404).json({
          success: false,
          message: 'Live class not found'
        });
        return;
      }

      res.json({
        success: true,
        message: 'Live class ended successfully',
        data: liveClass
      });
    } catch (error: any) {
      res.status(403).json({
        success: false,
        message: error.message
      });
    }
  }

  async joinLiveClass(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const connectionInfo = {
        ip: req.ip,
        userAgent: req.headers['user-agent'],
        platform: req.body.platform || 'web'
      };

      const participant = await LiveClassService.joinLiveClass(id, req.user!.id, connectionInfo);

      res.json({
        success: true,
        message: 'Joined live class successfully',
        data: participant
      });
    } catch (error: any) {
      res.status(400).json({
        success: false,
        message: error.message
      });
    }
  }

  async leaveLiveClass(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      await LiveClassService.leaveLiveClass(id, req.user!.id);

      res.json({
        success: true,
        message: 'Left live class successfully'
      });
    } catch (error: any) {
      res.status(500).json({
        success: false,
        message: 'Failed to leave live class',
        error: error.message
      });
    }
  }

  async getParticipants(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const participants = await LiveClassService.getParticipants(id);

      res.json({
        success: true,
        message: 'Participants retrieved successfully',
        data: participants
      });
    } catch (error: any) {
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve participants',
        error: error.message
      });
    }
  }

  async updateParticipantPermissions(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { id, participantId } = req.params;
      const { permissions } = req.body;

      await LiveClassService.updateParticipantPermissions(
        id,
        participantId,
        permissions,
        req.user!.id
      );

      res.json({
        success: true,
        message: 'Participant permissions updated successfully'
      });
    } catch (error: any) {
      res.status(400).json({
        success: false,
        message: error.message
      });
    }
  }

  async removeParticipant(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { id, participantId } = req.params;
      const { reason } = req.body;

      await LiveClassService.removeParticipant(id, participantId, req.user!.id, reason);

      res.json({
        success: true,
        message: 'Participant removed successfully'
      });
    } catch (error: any) {
      res.status(400).json({
        success: false,
        message: error.message
      });
    }
  }

  // Chat endpoints
  async sendChatMessage(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const { message, type, metadata } = req.body;

      const chatMessage = await LiveClassChatService.sendMessage({
        liveClassId: id,
        userId: req.user!.id,
        message,
        type,
        metadata
      });

      res.status(201).json({
        success: true,
        message: 'Message sent successfully',
        data: chatMessage
      });
    } catch (error: any) {
      res.status(400).json({
        success: false,
        message: error.message
      });
    }
  }

  async getChatMessages(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const { limit, skip, since } = req.query;

      const options = {
        limit: limit ? parseInt(limit as string) : undefined,
        skip: skip ? parseInt(skip as string) : undefined,
        since: since ? new Date(since as string) : undefined
      };

      const messages = await LiveClassChatService.getMessages(id, options);

      res.json({
        success: true,
        message: 'Messages retrieved successfully',
        data: messages
      });
    } catch (error: any) {
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve messages',
        error: error.message
      });
    }
  }

  async pinChatMessage(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { messageId } = req.params;
      await LiveClassChatService.pinMessage(messageId, req.user!.id);

      res.json({
        success: true,
        message: 'Message pinned successfully'
      });
    } catch (error: any) {
      res.status(400).json({
        success: false,
        message: error.message
      });
    }
  }

  async hideChatMessage(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { messageId } = req.params;
      const { reason } = req.body;

      await LiveClassChatService.hideMessage(messageId, req.user!.id, reason);

      res.json({
        success: true,
        message: 'Message hidden successfully'
      });
    } catch (error: any) {
      res.status(400).json({
        success: false,
        message: error.message
      });
    }
  }

  async sendAnnouncement(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const { message } = req.body;

      const announcement = await LiveClassChatService.sendAnnouncement(id, req.user!.id, message);

      res.status(201).json({
        success: true,
        message: 'Announcement sent successfully',
        data: announcement
      });
    } catch (error: any) {
      res.status(400).json({
        success: false,
        message: error.message
      });
    }
  }

  async deleteLiveClass(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      // Implementation depends on your deletion strategy
      // You might want to soft delete or archive instead

      res.json({
        success: true,
        message: 'Live class deleted successfully'
      });
    } catch (error: any) {
      res.status(500).json({
        success: false,
        message: 'Failed to delete live class',
        error: error.message
      });
    }
  }
}

export default new LiveClassController();
