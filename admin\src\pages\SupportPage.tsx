import React, { useState } from 'react'
import { motion } from 'framer-motion'
import {
  ChatBubbleLeftRightIcon,
  ClockIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  UserIcon,
  TagIcon,
} from '@heroicons/react/24/outline'
import { DataTable } from '../components/DataTable'
import { TicketModal } from '../components/modals/TicketModal'

export const SupportPage: React.FC = () => {
  const [showTicketModal, setShowTicketModal] = useState(false)
  const [selectedTicket, setSelectedTicket] = useState<any>(null)

  const columns = [
    { key: 'ticketId', header: 'Ticket ID', sortable: true },
    { key: 'subject', header: 'Subject' },
    { key: 'user', header: 'User' },
    { key: 'category', header: 'Category' },
    { key: 'priority', header: 'Priority' },
    { key: 'status', header: 'Status' },
    { key: 'assignedTo', header: 'Assigned To' },
    { key: 'createdAt', header: 'Created', sortable: true },
    { key: 'actions', header: 'Actions' },
  ]

  const mockTickets = [
    {
      id: '1',
      ticketId: 'TK-2024-001',
      subject: 'Cannot access video lectures',
      user: 'Rahul Kumar',
      category: 'Technical',
      priority: 'High',
      status: 'Open',
      assignedTo: 'Support Team',
      createdAt: '2024-01-15',
    },
    {
      id: '2',
      ticketId: 'TK-2024-002',
      subject: 'Payment not reflecting',
      user: 'Priya Sharma',
      category: 'Billing',
      priority: 'Medium',
      status: 'In Progress',
      assignedTo: 'Finance Team',
      createdAt: '2024-01-14',
    },
    {
      id: '3',
      ticketId: 'TK-2024-003',
      subject: 'Request for course refund',
      user: 'Amit Singh',
      category: 'Billing',
      priority: 'Low',
      status: 'Resolved',
      assignedTo: 'Admin Team',
      createdAt: '2024-01-13',
    },
  ]

  const stats = [
    { name: 'Total Tickets', value: '2,847', icon: ChatBubbleLeftRightIcon, color: 'bg-blue-500' },
    { name: 'Open Tickets', value: '156', icon: ClockIcon, color: 'bg-yellow-500' },
    { name: 'Resolved Today', value: '89', icon: CheckCircleIcon, color: 'bg-green-500' },
    { name: 'Urgent Tickets', value: '12', icon: ExclamationTriangleIcon, color: 'bg-red-500' },
  ]

  const ticketsByCategory = [
    { name: 'Technical Issues', count: 45, percentage: 35 },
    { name: 'Billing & Payments', count: 32, percentage: 25 },
    { name: 'Course Content', count: 28, percentage: 22 },
    { name: 'Account Issues', count: 18, percentage: 14 },
    { name: 'Others', count: 5, percentage: 4 },
  ]

  const recentActivity = [
    {
      action: 'Ticket resolved',
      ticket: 'TK-2024-003',
      user: 'Admin Team',
      time: '5 minutes ago',
    },
    {
      action: 'New ticket assigned',
      ticket: 'TK-2024-004',
      user: 'Support Team',
      time: '12 minutes ago',
    },
    {
      action: 'Priority updated',
      ticket: 'TK-2024-001',
      user: 'Manager',
      time: '25 minutes ago',
    },
    {
      action: 'Response sent',
      ticket: 'TK-2024-002',
      user: 'Finance Team',
      time: '1 hour ago',
    },
  ]

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="md:flex md:items-center md:justify-between">
        <div className="flex-1 min-w-0">
          <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
            Support Center
          </h2>
          <p className="mt-1 text-sm text-gray-500">
            Manage customer support tickets and help requests
          </p>
        </div>
        <div className="mt-4 flex md:mt-0 md:ml-4 space-x-2">
          <button className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
            <TagIcon className="-ml-1 mr-2 h-5 w-5" />
            Manage Categories
          </button>
          <button
            onClick={() => setShowTicketModal(true)}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700"
          >
            <ChatBubbleLeftRightIcon className="-ml-1 mr-2 h-5 w-5" />
            Create Ticket
          </button>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <motion.div
            key={stat.name}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            className="bg-white rounded-lg p-6 shadow-sm border border-gray-200"
          >
            <div className="flex items-center">
              <div className={`flex-shrink-0 p-3 rounded-md ${stat.color}`}>
                <stat.icon className="h-6 w-6 text-white" />
              </div>
              <div className="ml-5">
                <p className="text-sm font-medium text-gray-500">{stat.name}</p>
                <p className="text-2xl font-semibold text-gray-900">{stat.value}</p>
              </div>
            </div>
          </motion.div>
        ))}
      </div>

      {/* Dashboard Overview */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Tickets by Category */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
        >
          <h3 className="text-lg font-medium text-gray-900 mb-6">Tickets by Category</h3>
          <div className="space-y-4">
            {ticketsByCategory.map((category, index) => (
              <div key={category.name} className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <span className="text-sm font-medium text-gray-900">{category.name}</span>
                  <span className="text-sm text-gray-500">({category.count})</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-24 bg-gray-200 rounded-full h-2">
                    <motion.div
                      initial={{ width: 0 }}
                      animate={{ width: `${category.percentage}%` }}
                      transition={{ delay: index * 0.1 + 0.5, duration: 0.8 }}
                      className="bg-primary-600 h-2 rounded-full"
                    />
                  </div>
                  <span className="text-sm font-medium text-gray-900 w-10">{category.percentage}%</span>
                </div>
              </div>
            ))}
          </div>
        </motion.div>

        {/* Recent Activity */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
        >
          <h3 className="text-lg font-medium text-gray-900 mb-6">Recent Activity</h3>
          <div className="space-y-4">
            {recentActivity.map((activity, index) => (
              <div key={index} className="flex items-start space-x-3">
                <div className="flex-shrink-0">
                  <div className="w-2 h-2 bg-primary-600 rounded-full mt-2"></div>
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm text-gray-900">
                    <span className="font-medium">{activity.action}</span>
                    {' '}
                    <span className="text-primary-600">{activity.ticket}</span>
                    {' by '}
                    <span className="font-medium">{activity.user}</span>
                  </p>
                  <p className="text-xs text-gray-500 mt-1">{activity.time}</p>
                </div>
              </div>
            ))}
          </div>
        </motion.div>
      </div>

      {/* Team Performance */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
        className="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
      >
        <h3 className="text-lg font-medium text-gray-900 mb-6">Team Performance (This Week)</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center">
            <div className="text-3xl font-bold text-green-600">4.8</div>
            <div className="text-sm text-gray-500 mt-1">Avg Response Time (hours)</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-blue-600">94%</div>
            <div className="text-sm text-gray-500 mt-1">Resolution Rate</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-purple-600">4.6</div>
            <div className="text-sm text-gray-500 mt-1">Customer Satisfaction</div>
          </div>
        </div>
      </motion.div>

      {/* Filters */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Filter Tickets</h3>
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
          <input
            type="text"
            placeholder="Search tickets..."
            className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          />
          <select className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-transparent">
            <option value="">All Categories</option>
            <option value="technical">Technical Issues</option>
            <option value="billing">Billing & Payments</option>
            <option value="content">Course Content</option>
            <option value="account">Account Issues</option>
          </select>
          <select className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-transparent">
            <option value="">All Status</option>
            <option value="open">Open</option>
            <option value="in-progress">In Progress</option>
            <option value="resolved">Resolved</option>
            <option value="closed">Closed</option>
          </select>
          <select className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-transparent">
            <option value="">All Priority</option>
            <option value="low">Low</option>
            <option value="medium">Medium</option>
            <option value="high">High</option>
            <option value="urgent">Urgent</option>
          </select>
          <select className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-transparent">
            <option value="">All Assignees</option>
            <option value="support-team">Support Team</option>
            <option value="finance-team">Finance Team</option>
            <option value="admin-team">Admin Team</option>
            <option value="technical-team">Technical Team</option>
          </select>
        </div>
      </div>

      {/* Tickets Table */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <DataTable
          columns={columns}
          data={mockTickets}
          onRowEdit={(ticket) => {
            setSelectedTicket(ticket)
            setShowTicketModal(true)
          }}
        />
      </div>

      {/* Ticket Modal */}
      <TicketModal
        open={showTicketModal}
        onClose={() => {
          setShowTicketModal(false)
          setSelectedTicket(null)
        }}
        ticket={selectedTicket}
      />
    </div>
  )
}
