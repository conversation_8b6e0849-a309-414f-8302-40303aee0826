import { apiService } from './api';
import { User, ApiResponse } from '@/types';

interface LoginResponse {
  user: User;
  token: string;
}

interface RegisterData {
  name: string;
  email: string;
  password: string;
  phone?: string;
  role?: string;
}

interface LoginData {
  email: string;
  password: string;
}

interface ResetPasswordData {
  token: string;
  password: string;
}

class AuthService {
  async login(email: string, password: string): Promise<ApiResponse<LoginResponse>> {
    const response = await apiService.post<LoginResponse>('/auth/login', {
      email,
      password,
    });
    
    if (response.success && response.data?.token) {
      localStorage.setItem('auth-token', response.data.token);
    }
    
    return response;
  }

  async register(userData: RegisterData): Promise<ApiResponse<LoginResponse>> {
    const response = await apiService.post<LoginResponse>('/auth/register', userData);
    
    if (response.success && response.data?.token) {
      localStorage.setItem('auth-token', response.data.token);
    }
    
    return response;
  }

  async logout(): Promise<void> {
    try {
      await apiService.post('/auth/logout');
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      localStorage.removeItem('auth-token');
    }
  }

  async getCurrentUser(): Promise<ApiResponse<User>> {
    return apiService.get<User>('/auth/me');
  }

  async updateProfile(userData: Partial<User>): Promise<ApiResponse<User>> {
    return apiService.put<User>('/auth/profile', userData);
  }

  async changePassword(currentPassword: string, newPassword: string): Promise<ApiResponse<void>> {
    return apiService.put<void>('/auth/change-password', {
      currentPassword,
      newPassword,
    });
  }

  async forgotPassword(email: string): Promise<ApiResponse<void>> {
    return apiService.post<void>('/auth/forgot-password', { email });
  }

  async resetPassword(token: string, password: string): Promise<ApiResponse<void>> {
    return apiService.post<void>('/auth/reset-password', { token, password });
  }

  async verifyEmail(token: string): Promise<ApiResponse<void>> {
    return apiService.post<void>('/auth/verify-email', { token });
  }

  async resendVerificationEmail(): Promise<ApiResponse<void>> {
    return apiService.post<void>('/auth/resend-verification');
  }

  async refreshToken(): Promise<ApiResponse<{ token: string }>> {
    return apiService.post<{ token: string }>('/auth/refresh');
  }

  // Social authentication
  async googleAuth(token: string): Promise<ApiResponse<LoginResponse>> {
    const response = await apiService.post<LoginResponse>('/auth/google', { token });
    
    if (response.success && response.data?.token) {
      localStorage.setItem('auth-token', response.data.token);
    }
    
    return response;
  }

  async facebookAuth(token: string): Promise<ApiResponse<LoginResponse>> {
    const response = await apiService.post<LoginResponse>('/auth/facebook', { token });
    
    if (response.success && response.data?.token) {
      localStorage.setItem('auth-token', response.data.token);
    }
    
    return response;
  }

  // Utility methods
  isAuthenticated(): boolean {
    return !!localStorage.getItem('auth-token');
  }

  getToken(): string | null {
    return localStorage.getItem('auth-token');
  }

  // Two-factor authentication
  async enableTwoFactor(): Promise<ApiResponse<{ qrCode: string; secret: string }>> {
    return apiService.post<{ qrCode: string; secret: string }>('/auth/2fa/enable');
  }

  async verifyTwoFactor(token: string): Promise<ApiResponse<void>> {
    return apiService.post<void>('/auth/2fa/verify', { token });
  }

  async disableTwoFactor(token: string): Promise<ApiResponse<void>> {
    return apiService.post<void>('/auth/2fa/disable', { token });
  }
}

export const authService = new AuthService();
export default authService;
