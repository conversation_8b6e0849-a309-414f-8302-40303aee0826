import React from 'react'
import { Routes, Route, Navigate } from 'react-router-dom'
import { useAuthStore } from './stores/authStore'
import { Layout } from './components/Layout'
import { LoginPage } from './pages/LoginPage'
import { DashboardPage } from './pages/DashboardPage'
import { UsersPage } from './pages/UsersPage'
import { BatchesPage } from './pages/BatchesPage'
import { CoursesPage } from './pages/CoursesPage'
import { ContentPage } from './pages/ContentPage'
import { PaymentsPage } from './pages/PaymentsPage'
import { AnalyticsPage } from './pages/AnalyticsPage'
import { SettingsPage } from './pages/SettingsPage'
import { NotificationsPage } from './pages/NotificationsPage'
import { AuditLogsPage } from './pages/AuditLogsPage'
import { SupportPage } from './pages/SupportPage'
import { ProtectedRoute } from './components/ProtectedRoute'

function App() {
  const { isAuthenticated } = useAuthStore()

  return (
    <div className="min-h-screen bg-gray-50">
      <Routes>
        <Route path="/login" element={<LoginPage />} />
        <Route path="/" element={
          <ProtectedRoute>
            <Layout />
          </ProtectedRoute>
        }>
          <Route index element={<Navigate to="/dashboard" replace />} />
          <Route path="dashboard" element={<DashboardPage />} />
          <Route path="users/*" element={<UsersPage />} />
          <Route path="batches/*" element={<BatchesPage />} />
          <Route path="courses/*" element={<CoursesPage />} />
          <Route path="content/*" element={<ContentPage />} />
          <Route path="payments/*" element={<PaymentsPage />} />
          <Route path="analytics/*" element={<AnalyticsPage />} />
          <Route path="notifications/*" element={<NotificationsPage />} />
          <Route path="audit-logs" element={<AuditLogsPage />} />
          <Route path="support" element={<SupportPage />} />
          <Route path="settings/*" element={<SettingsPage />} />
        </Route>
        <Route path="*" element={<Navigate to="/" replace />} />
      </Routes>
    </div>
  )
}

export default App
