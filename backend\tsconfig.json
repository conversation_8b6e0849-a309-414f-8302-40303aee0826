{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["ES2020"], "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "moduleResolution": "node", "allowSyntheticDefaultImports": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "sourceMap": true, "baseUrl": "./src", "paths": {"@/*": ["*"], "@/controllers/*": ["controllers/*"], "@/models/*": ["models/*"], "@/routes/*": ["routes/*"], "@/middleware/*": ["middleware/*"], "@/services/*": ["services/*"], "@/utils/*": ["utils/*"], "@/types/*": ["types/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "tests"]}