import mongoose, { Schema } from 'mongoose';
import { IDoubt } from '@/types';

const responseSchema = new Schema({
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  message: {
    type: String,
    required: [true, 'Response message is required']
  },
  attachments: [{
    type: String
  }],
  createdAt: {
    type: Date,
    default: Date.now
  }
});

const doubtSchema = new Schema<IDoubt>({
  title: {
    type: String,
    required: [true, 'Doubt title is required'],
    trim: true,
    maxlength: [200, 'Doubt title cannot exceed 200 characters']
  },
  description: {
    type: String,
    required: [true, 'Doubt description is required'],
    maxlength: [2000, 'Description cannot exceed 2000 characters']
  },
  studentId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Student ID is required']
  },
  subjectId: {
    type: Schema.Types.ObjectId,
    ref: 'Subject',
    required: [true, 'Subject ID is required']
  },
  chapterId: {
    type: Schema.Types.ObjectId,
    ref: 'Chapter'
  },
  status: {
    type: String,
    enum: ['pending', 'in_progress', 'resolved', 'closed'],
    default: 'pending'
  },
  priority: {
    type: String,
    enum: ['low', 'medium', 'high'],
    default: 'medium'
  },
  assignedTo: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  responses: [responseSchema],
  attachments: [{
    type: String
  }],
  tags: [{
    type: String,
    trim: true,
    lowercase: true
  }]
}, {
  timestamps: true
});

doubtSchema.index({ studentId: 1 });
doubtSchema.index({ subjectId: 1 });
doubtSchema.index({ status: 1 });
doubtSchema.index({ priority: 1 });
doubtSchema.index({ assignedTo: 1 });
doubtSchema.index({ createdAt: -1 });

export default mongoose.model<IDoubt>('Doubt', doubtSchema);
