import React from 'react'
import { Dialog, Transition } from '@headlessui/react'
import { useForm } from 'react-hook-form'
import { XMarkIcon } from '@heroicons/react/24/outline'
import { LoadingSpinner } from '../LoadingSpinner'

interface BatchModalProps {
  open: boolean
  onClose: () => void
  batch?: any
}

interface BatchForm {
  name: string
  course: string
  teacher: string
  maxStudents: number
  startDate: string
  endDate: string
  schedule: string
  fee: number
  description: string
  status: string
}

export const BatchModal: React.FC<BatchModalProps> = ({ open, onClose, batch }) => {
  const isEditing = !!batch
  const [loading, setLoading] = React.useState(false)

  const { register, handleSubmit, formState: { errors }, reset } = useForm<BatchForm>({
    defaultValues: batch ? {
      name: batch.name,
      course: batch.course,
      teacher: batch.teacher,
      maxStudents: 50,
      startDate: batch.startDate,
      endDate: batch.endDate,
      schedule: batch.schedule,
      fee: parseInt(batch.fee?.replace(/[₹,]/g, '') || '0'),
      description: batch.description,
      status: batch.status,
    } : {
      maxStudents: 50,
      status: 'upcoming',
      schedule: 'weekdays',
    }
  })

  React.useEffect(() => {
    if (batch) {
      reset({
        name: batch.name,
        course: batch.course,
        teacher: batch.teacher,
        maxStudents: 50,
        startDate: batch.startDate,
        endDate: batch.endDate,
        schedule: batch.schedule,
        fee: parseInt(batch.fee?.replace(/[₹,]/g, '') || '0'),
        description: batch.description,
        status: batch.status,
      })
    } else {
      reset({
        maxStudents: 50,
        status: 'upcoming',
        schedule: 'weekdays',
      })
    }
  }, [batch, reset])

  const onSubmit = async (data: BatchForm) => {
    setLoading(true)
    try {
      // API call to create/update batch
      console.log('Batch data:', data)
      await new Promise(resolve => setTimeout(resolve, 1000)) // Mock API call
      onClose()
    } catch (error) {
      console.error('Error saving batch:', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <Transition.Root show={open} as={React.Fragment}>
      <Dialog as="div" className="relative z-50" onClose={onClose}>
        <Transition.Child
          as={React.Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
        </Transition.Child>

        <div className="fixed inset-0 z-10 overflow-y-auto">
          <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
            <Transition.Child
              as={React.Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
              enterTo="opacity-100 translate-y-0 sm:scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 translate-y-0 sm:scale-100"
              leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            >
              <Dialog.Panel className="relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-2xl sm:p-6">
                <div className="absolute right-0 top-0 hidden pr-4 pt-4 sm:block">
                  <button
                    type="button"
                    className="rounded-md bg-white text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
                    onClick={onClose}
                  >
                    <XMarkIcon className="h-6 w-6" />
                  </button>
                </div>

                <div className="sm:flex sm:items-start">
                  <div className="mt-3 text-center sm:ml-4 sm:mt-0 sm:text-left w-full">
                    <Dialog.Title as="h3" className="text-base font-semibold leading-6 text-gray-900">
                      {isEditing ? 'Edit Batch' : 'Create New Batch'}
                    </Dialog.Title>

                    <form onSubmit={handleSubmit(onSubmit)} className="mt-6 space-y-4">
                      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                        <div className="lg:col-span-2">
                          <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                            Batch Name
                          </label>
                          <input
                            {...register('name', { required: 'Batch name is required' })}
                            type="text"
                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                            placeholder="e.g., JEE Mains 2024 - Batch A"
                          />
                          {errors.name && (
                            <p className="mt-1 text-sm text-red-600">{errors.name.message}</p>
                          )}
                        </div>

                        <div>
                          <label htmlFor="course" className="block text-sm font-medium text-gray-700">
                            Course
                          </label>
                          <select
                            {...register('course', { required: 'Course is required' })}
                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                          >
                            <option value="">Select Course</option>
                            <option value="JEE Preparation">JEE Preparation</option>
                            <option value="NEET Preparation">NEET Preparation</option>
                            <option value="CBSE Board">CBSE Board</option>
                            <option value="Foundation">Foundation Course</option>
                          </select>
                          {errors.course && (
                            <p className="mt-1 text-sm text-red-600">{errors.course.message}</p>
                          )}
                        </div>

                        <div>
                          <label htmlFor="teacher" className="block text-sm font-medium text-gray-700">
                            Assigned Teacher
                          </label>
                          <select
                            {...register('teacher', { required: 'Teacher is required' })}
                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                          >
                            <option value="">Select Teacher</option>
                            <option value="Dr. Rajesh Kumar">Dr. Rajesh Kumar</option>
                            <option value="Dr. Priya Sharma">Dr. Priya Sharma</option>
                            <option value="Prof. Amit Singh">Prof. Amit Singh</option>
                            <option value="Mrs. Sunita Gupta">Mrs. Sunita Gupta</option>
                          </select>
                          {errors.teacher && (
                            <p className="mt-1 text-sm text-red-600">{errors.teacher.message}</p>
                          )}
                        </div>

                        <div>
                          <label htmlFor="maxStudents" className="block text-sm font-medium text-gray-700">
                            Maximum Students
                          </label>
                          <input
                            {...register('maxStudents', { 
                              required: 'Max students is required',
                              min: { value: 1, message: 'Must be at least 1' },
                              max: { value: 100, message: 'Cannot exceed 100' }
                            })}
                            type="number"
                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                          />
                          {errors.maxStudents && (
                            <p className="mt-1 text-sm text-red-600">{errors.maxStudents.message}</p>
                          )}
                        </div>

                        <div>
                          <label htmlFor="fee" className="block text-sm font-medium text-gray-700">
                            Course Fee (₹)
                          </label>
                          <input
                            {...register('fee', { 
                              required: 'Fee is required',
                              min: { value: 0, message: 'Fee cannot be negative' }
                            })}
                            type="number"
                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                          />
                          {errors.fee && (
                            <p className="mt-1 text-sm text-red-600">{errors.fee.message}</p>
                          )}
                        </div>

                        <div>
                          <label htmlFor="startDate" className="block text-sm font-medium text-gray-700">
                            Start Date
                          </label>
                          <input
                            {...register('startDate', { required: 'Start date is required' })}
                            type="date"
                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                          />
                          {errors.startDate && (
                            <p className="mt-1 text-sm text-red-600">{errors.startDate.message}</p>
                          )}
                        </div>

                        <div>
                          <label htmlFor="endDate" className="block text-sm font-medium text-gray-700">
                            End Date
                          </label>
                          <input
                            {...register('endDate', { required: 'End date is required' })}
                            type="date"
                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                          />
                          {errors.endDate && (
                            <p className="mt-1 text-sm text-red-600">{errors.endDate.message}</p>
                          )}
                        </div>

                        <div>
                          <label htmlFor="schedule" className="block text-sm font-medium text-gray-700">
                            Schedule
                          </label>
                          <select
                            {...register('schedule', { required: 'Schedule is required' })}
                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                          >
                            <option value="weekdays">Weekdays (Mon-Fri)</option>
                            <option value="weekends">Weekends (Sat-Sun)</option>
                            <option value="alternate">Alternate Days</option>
                            <option value="custom">Custom Schedule</option>
                          </select>
                        </div>

                        <div>
                          <label htmlFor="status" className="block text-sm font-medium text-gray-700">
                            Status
                          </label>
                          <select
                            {...register('status', { required: 'Status is required' })}
                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                          >
                            <option value="upcoming">Upcoming</option>
                            <option value="active">Active</option>
                            <option value="completed">Completed</option>
                            <option value="cancelled">Cancelled</option>
                          </select>
                        </div>

                        <div className="lg:col-span-2">
                          <label htmlFor="description" className="block text-sm font-medium text-gray-700">
                            Description
                          </label>
                          <textarea
                            {...register('description')}
                            rows={3}
                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                            placeholder="Batch description, syllabus coverage, special features..."
                          />
                        </div>
                      </div>

                      <div className="mt-5 sm:mt-4 sm:flex sm:flex-row-reverse">
                        <button
                          type="submit"
                          disabled={loading}
                          className="inline-flex w-full justify-center rounded-md bg-primary-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-primary-500 sm:ml-3 sm:w-auto disabled:opacity-50"
                        >
                          {loading ? (
                            <LoadingSpinner size="sm" />
                          ) : (
                            isEditing ? 'Update Batch' : 'Create Batch'
                          )}
                        </button>
                        <button
                          type="button"
                          className="mt-3 inline-flex w-full justify-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 sm:mt-0 sm:w-auto"
                          onClick={onClose}
                        >
                          Cancel
                        </button>
                      </div>
                    </form>
                  </div>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition.Root>
  )
}
