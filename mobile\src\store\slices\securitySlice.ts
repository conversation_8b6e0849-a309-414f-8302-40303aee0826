import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { SecurityConfig } from '@/types';
import { SecurityService } from '@/services/security';

interface SecurityState {
  isRooted: boolean;
  isDebuggingEnabled: boolean;
  isEmulator: boolean;
  deviceId: string | null;
  sessionId: string | null;
  lastSecurityCheck: Date | null;
  securityLevel: 'low' | 'medium' | 'high';
  violations: SecurityViolation[];
  isScreenshotBlocked: boolean;
  isScreenRecordingDetected: boolean;
  watermarkConfig: {
    enabled: boolean;
    text: string;
    opacity: number;
    position: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right' | 'center';
  };
  certificatePinning: {
    enabled: boolean;
    pins: string[];
  };
  error: string | null;
}

interface SecurityViolation {
  id: string;
  type: 'root_detected' | 'debug_detected' | 'screenshot_attempt' | 'screen_recording' | 'tampering';
  timestamp: Date;
  details: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
}

const initialState: SecurityState = {
  isRooted: false,
  isDebuggingEnabled: false,
  isEmulator: false,
  deviceId: null,
  sessionId: null,
  lastSecurityCheck: null,
  securityLevel: 'medium',
  violations: [],
  isScreenshotBlocked: true,
  isScreenRecordingDetected: false,
  watermarkConfig: {
    enabled: true,
    text: 'RJWU EduTech',
    opacity: 0.7,
    position: 'bottom-right',
  },
  certificatePinning: {
    enabled: true,
    pins: [],
  },
  error: null,
};

export const performSecurityCheck = createAsyncThunk(
  'security/performSecurityCheck',
  async () => {
    const results = await SecurityService.performSecurityCheck();
    return results;
  }
);

export const detectRootAccess = createAsyncThunk(
  'security/detectRootAccess',
  async () => {
    const isRooted = await SecurityService.isDeviceRooted();
    return isRooted;
  }
);

export const detectDebugging = createAsyncThunk(
  'security/detectDebugging',
  async () => {
    const isDebugging = await SecurityService.isDebuggingEnabled();
    return isDebugging;
  }
);

export const detectEmulator = createAsyncThunk(
  'security/detectEmulator',
  async () => {
    const isEmulator = await SecurityService.isEmulator();
    return isEmulator;
  }
);

export const blockScreenshots = createAsyncThunk(
  'security/blockScreenshots',
  async (enabled: boolean) => {
    await SecurityService.blockScreenshots(enabled);
    return enabled;
  }
);

export const detectScreenRecording = createAsyncThunk(
  'security/detectScreenRecording',
  async () => {
    const isRecording = await SecurityService.isScreenRecording();
    return isRecording;
  }
);

export const generateDeviceFingerprint = createAsyncThunk(
  'security/generateDeviceFingerprint',
  async () => {
    const fingerprint = await SecurityService.generateDeviceFingerprint();
    return fingerprint;
  }
);

export const validateCertificates = createAsyncThunk(
  'security/validateCertificates',
  async (url: string) => {
    const isValid = await SecurityService.validateCertificatePinning(url);
    return { url, isValid };
  }
);

export const reportSecurityViolation = createAsyncThunk(
  'security/reportViolation',
  async (violation: Omit<SecurityViolation, 'id' | 'timestamp'>) => {
    const id = await SecurityService.reportViolation(violation);
    return { ...violation, id, timestamp: new Date() };
  }
);

const securitySlice = createSlice({
  name: 'security',
  initialState,
  reducers: {
    setSecurityLevel: (state, action: PayloadAction<'low' | 'medium' | 'high'>) => {
      state.securityLevel = action.payload;
    },
    updateWatermarkConfig: (
      state,
      action: PayloadAction<Partial<SecurityState['watermarkConfig']>>
    ) => {
      state.watermarkConfig = { ...state.watermarkConfig, ...action.payload };
    },
    updateCertificatePinning: (
      state,
      action: PayloadAction<Partial<SecurityState['certificatePinning']>>
    ) => {
      state.certificatePinning = { ...state.certificatePinning, ...action.payload };
    },
    addSecurityViolation: (state, action: PayloadAction<SecurityViolation>) => {
      state.violations.unshift(action.payload);
      // Keep only last 100 violations
      if (state.violations.length > 100) {
        state.violations = state.violations.slice(0, 100);
      }
    },
    clearSecurityViolations: (state) => {
      state.violations = [];
    },
    setSessionId: (state, action: PayloadAction<string>) => {
      state.sessionId = action.payload;
    },
    clearSession: (state) => {
      state.sessionId = null;
    },
    clearError: (state) => {
      state.error = null;
    },
    setScreenRecordingDetected: (state, action: PayloadAction<boolean>) => {
      state.isScreenRecordingDetected = action.payload;
      if (action.payload) {
        const violation: SecurityViolation = {
          id: Date.now().toString(),
          type: 'screen_recording',
          timestamp: new Date(),
          details: 'Screen recording activity detected',
          severity: 'high',
        };
        state.violations.unshift(violation);
      }
    },
  },
  extraReducers: (builder) => {
    builder
      // Perform Security Check
      .addCase(performSecurityCheck.pending, (state) => {
        state.error = null;
      })
      .addCase(performSecurityCheck.fulfilled, (state, action) => {
        const { isRooted, isDebugging, isEmulator, deviceId } = action.payload;
        state.isRooted = isRooted;
        state.isDebuggingEnabled = isDebugging;
        state.isEmulator = isEmulator;
        state.deviceId = deviceId;
        state.lastSecurityCheck = new Date();
        
        // Determine security level based on checks
        if (isRooted || isDebugging || isEmulator) {
          state.securityLevel = 'low';
        } else {
          state.securityLevel = 'high';
        }
      })
      .addCase(performSecurityCheck.rejected, (state, action) => {
        state.error = action.error.message || 'Security check failed';
      })
      // Detect Root Access
      .addCase(detectRootAccess.fulfilled, (state, action) => {
        state.isRooted = action.payload;
        if (action.payload) {
          const violation: SecurityViolation = {
            id: Date.now().toString(),
            type: 'root_detected',
            timestamp: new Date(),
            details: 'Root access detected on device',
            severity: 'critical',
          };
          state.violations.unshift(violation);
        }
      })
      // Detect Debugging
      .addCase(detectDebugging.fulfilled, (state, action) => {
        state.isDebuggingEnabled = action.payload;
        if (action.payload) {
          const violation: SecurityViolation = {
            id: Date.now().toString(),
            type: 'debug_detected',
            timestamp: new Date(),
            details: 'Debug mode detected',
            severity: 'high',
          };
          state.violations.unshift(violation);
        }
      })
      // Detect Emulator
      .addCase(detectEmulator.fulfilled, (state, action) => {
        state.isEmulator = action.payload;
      })
      // Block Screenshots
      .addCase(blockScreenshots.fulfilled, (state, action) => {
        state.isScreenshotBlocked = action.payload;
      })
      // Detect Screen Recording
      .addCase(detectScreenRecording.fulfilled, (state, action) => {
        state.isScreenRecordingDetected = action.payload;
        if (action.payload) {
          const violation: SecurityViolation = {
            id: Date.now().toString(),
            type: 'screen_recording',
            timestamp: new Date(),
            details: 'Screen recording detected',
            severity: 'high',
          };
          state.violations.unshift(violation);
        }
      })
      // Generate Device Fingerprint
      .addCase(generateDeviceFingerprint.fulfilled, (state, action) => {
        state.deviceId = action.payload;
      })
      // Report Security Violation
      .addCase(reportSecurityViolation.fulfilled, (state, action) => {
        state.violations.unshift(action.payload);
      });
  },
});

export const {
  setSecurityLevel,
  updateWatermarkConfig,
  updateCertificatePinning,
  addSecurityViolation,
  clearSecurityViolations,
  setSessionId,
  clearSession,
  clearError,
  setScreenRecordingDetected,
} = securitySlice.actions;

export default securitySlice.reducer;
