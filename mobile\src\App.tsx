import React, { useEffect, useState } from 'react';
import { StatusBar, Platform, AppState, AppStateStatus } from 'react-native';
import { Provider } from 'react-redux';
import { PersistGate } from 'redux-persist/integration/react';
import { NavigationContainer } from '@react-navigation/native';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import SplashScreen from 'react-native-splash-screen';
import NetInfo from '@react-native-community/netinfo';

import { store, persistor } from '@/store';
import { AppNavigation } from '@/navigation/AppNavigation';
import { ThemeProvider } from '@/context/ThemeContext';
import { I18nProvider } from '@/context/I18nContext';
import { SecurityProvider } from '@/context/SecurityContext';
import { NetworkProvider } from '@/context/NetworkContext';
import { LoadingScreen } from '@/components/LoadingScreen';
import { SecurityService } from '@/services/security';
import { NotificationService } from '@/services/notification';
import { DownloadService } from '@/services/download';
import { useAppDispatch, useAppSelector } from '@/hooks/redux';
import { performSecurityCheck } from '@/store/slices/securitySlice';

const AppContent: React.FC = () => {
  const dispatch = useAppDispatch();
  const { theme } = useAppSelector(state => state.settings);
  const { isAuthenticated } = useAppSelector(state => state.auth);
  const [isInitialized, setIsInitialized] = useState(false);
  const [appState, setAppState] = useState(AppState.currentState);

  useEffect(() => {
    initializeApp();
    
    const handleAppStateChange = (nextAppState: AppStateStatus) => {
      if (appState.match(/inactive|background/) && nextAppState === 'active') {
        // App has come to the foreground
        handleAppForeground();
      } else if (nextAppState.match(/inactive|background/)) {
        // App has gone to background
        handleAppBackground();
      }
      setAppState(nextAppState);
    };

    const subscription = AppState.addEventListener('change', handleAppStateChange);
    
    return () => {
      subscription?.remove();
    };
  }, [appState]);

  const initializeApp = async () => {
    try {
      // Initialize security
      await SecurityService.initializeSecurity();
      
      // Initialize notifications
      await NotificationService.initialize();
      
      // Initialize download service
      await DownloadService.initialize();
      
      // Perform security check
      dispatch(performSecurityCheck());
      
      // Setup network monitoring
      const unsubscribe = NetInfo.addEventListener(state => {
        // Handle network state changes
        console.log('Network state changed:', state);
      });

      // Hide splash screen
      if (Platform.OS === 'android') {
        SplashScreen.hide();
      }
      
      setIsInitialized(true);
    } catch (error) {
      console.error('App initialization failed:', error);
      setIsInitialized(true); // Still show app even if some services fail
    }
  };

  const handleAppForeground = async () => {
    try {
      // Perform security check when app comes to foreground
      dispatch(performSecurityCheck());
      
      // Resume downloads
      await DownloadService.resumeAllDownloads();
      
      // Refresh notifications
      await NotificationService.refreshNotifications();
    } catch (error) {
      console.error('Foreground handler failed:', error);
    }
  };

  const handleAppBackground = async () => {
    try {
      // Pause downloads on background (optional)
      // await DownloadService.pauseAllDownloads();
      
      // Clear sensitive data from memory if needed
      // await SecurityService.clearSensitiveMemoryData();
    } catch (error) {
      console.error('Background handler failed:', error);
    }
  };

  if (!isInitialized) {
    return <LoadingScreen />;
  }

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <SafeAreaProvider>
        <ThemeProvider>
          <I18nProvider>
            <SecurityProvider>
              <NetworkProvider>
                <StatusBar
                  barStyle={theme === 'dark' ? 'light-content' : 'dark-content'}
                  backgroundColor={theme === 'dark' ? '#1A1A1A' : '#FFFFFF'}
                />
                <NavigationContainer>
                  <AppNavigation />
                </NavigationContainer>
              </NetworkProvider>
            </SecurityProvider>
          </I18nProvider>
        </ThemeProvider>
      </SafeAreaProvider>
    </GestureHandlerRootView>
  );
};

const App: React.FC = () => {
  return (
    <Provider store={store}>
      <PersistGate loading={<LoadingScreen />} persistor={persistor}>
        <AppContent />
      </PersistGate>
    </Provider>
  );
};

export default App;
