# RJWU EduTech Admin Panel

A comprehensive, modern admin panel for the RJWU EduTech platform built with React.js, TypeScript, and Tailwind CSS. This admin panel provides complete control over the educational platform with advanced features for user management, content moderation, analytics, and system administration.

## 🚀 Features

### 📊 Dashboard & Analytics
- **Real-time Statistics**: Live metrics for users, revenue, batches, and system performance
- **Interactive Charts**: Revenue trends, user growth, engagement metrics with Recharts
- **Performance Metrics**: Course completion rates, user retention, assignment submissions
- **Activity Feed**: Real-time platform activity and user actions
- **Quick Actions**: Fast access to common administrative tasks

### 👥 User Management
- **Comprehensive User Control**: Manage students, teachers, and admin users
- **Advanced Search & Filters**: Find users by role, status, batch, and custom criteria
- **Bulk Operations**: Mass user updates, activations, deactivations, and exports
- **User Profiles**: Detailed user information with enrollment history
- **Role-based Permissions**: Granular access control for different user types

### 🎓 Batch & Course Management
- **Batch Creation**: Create and manage student batches with access controls
- **Course Administration**: Full course lifecycle management
- **Content Organization**: Structured content management with categories
- **Enrollment Management**: Handle student enrollments and transfers
- **Performance Tracking**: Monitor batch and course performance metrics

### 📚 Content Management & Moderation
- **Content Approval Workflows**: Multi-stage content review and approval process
- **Bulk Content Operations**: Mass approve, reject, or archive content
- **Content Analytics**: Track video views, engagement, and completion rates
- **Watermarking & DRM**: Content protection and security features
- **Version Control**: Content versioning and revision history

### 💳 Payment & Financial Management
- **Multi-Gateway Support**: Integrate with Razorpay, PayU, PhonePe, Stripe
- **Transaction Monitoring**: Real-time payment tracking and status updates
- **Refund Management**: Process full and partial refunds with audit trails
- **Revenue Analytics**: Detailed financial reporting and insights
- **Payment Gateway Configuration**: Easy setup and management of payment providers

### 🔔 Notification System
- **Multi-Channel Notifications**: Email, SMS, push notifications, and in-app messages
- **Targeted Messaging**: Send notifications to specific user groups or individuals
- **Scheduled Campaigns**: Plan and schedule notification campaigns
- **Template Management**: Create and manage notification templates
- **Delivery Analytics**: Track notification delivery and engagement rates

### 🔒 Security & Audit
- **Comprehensive Audit Logs**: Track all administrative actions and system events
- **Security Monitoring**: Monitor failed login attempts and suspicious activities
- **Role-based Access Control**: Granular permissions for different admin roles
- **Session Management**: Control user sessions and concurrent logins
- **Data Protection**: Encryption, secure backups, and privacy controls

### 🎫 Support & Help Desk
- **Ticket Management**: Complete support ticket lifecycle management
- **Category-based Organization**: Organize tickets by type and priority
- **Team Assignment**: Assign tickets to appropriate support teams
- **Response Templates**: Quick responses with customizable templates
- **Performance Metrics**: Track resolution times and customer satisfaction

### ⚙️ System Settings & Configuration
- **Platform Configuration**: Customize branding, colors, and general settings
- **Payment Gateway Setup**: Configure multiple payment providers
- **Security Policies**: Set password policies, session timeouts, and security rules
- **Notification Settings**: Configure email, SMS, and push notification providers
- **System Maintenance**: Backup management, cache control, and performance optimization

## 🛠️ Technical Stack

### Frontend
- **React 18** with TypeScript for type safety
- **Tailwind CSS** for responsive, modern UI design
- **Framer Motion** for smooth animations and transitions
- **React Router** for client-side routing
- **React Hook Form** for efficient form management
- **Tanstack React Query** for server state management
- **Zustand** for local state management
- **Recharts** for data visualization and charts

### UI Components
- **Headless UI** for accessible, unstyled components
- **Heroicons** for consistent iconography
- **React Hot Toast** for user notifications
- **Custom Components** built for specific admin needs

### Development Tools
- **Vite** for fast development and building
- **TypeScript** for type safety and better DX
- **ESLint & Prettier** for code quality and formatting
- **PostCSS & Autoprefixer** for CSS processing

## 📦 Installation & Setup

### Prerequisites
- Node.js 18+ and npm/yarn
- Modern web browser with ES2020+ support

### Installation Steps

1. **Clone and Navigate**
   ```bash
   cd admin
   ```

2. **Install Dependencies**
   ```bash
   npm install
   # or
   yarn install
   ```

3. **Environment Configuration**
   ```bash
   cp .env.example .env.local
   ```
   
   Configure your environment variables:
   ```env
   VITE_API_URL=http://localhost:5000/api
   VITE_APP_NAME=RJWU Admin Panel
   VITE_APP_VERSION=1.0.0
   ```

4. **Start Development Server**
   ```bash
   npm run dev
   # or
   yarn dev
   ```

5. **Build for Production**
   ```bash
   npm run build
   # or
   yarn build
   ```

## 🏗️ Project Structure

```
admin/
├── public/                 # Static assets
├── src/
│   ├── components/         # Reusable UI components
│   │   ├── charts/        # Chart components (Recharts)
│   │   ├── modals/        # Modal dialogs
│   │   ├── settings/      # Settings page components
│   │   └── analytics/     # Analytics components
│   ├── pages/             # Main page components
│   │   ├── DashboardPage.tsx
│   │   ├── UsersPage.tsx
│   │   ├── BatchesPage.tsx
│   │   ├── CoursesPage.tsx
│   │   ├── ContentPage.tsx
│   │   ├── PaymentsPage.tsx
│   │   ├── AnalyticsPage.tsx
│   │   ├── NotificationsPage.tsx
│   │   ├── AuditLogsPage.tsx
│   │   ├── SupportPage.tsx
│   │   └── SettingsPage.tsx
│   ├── stores/            # State management (Zustand)
│   ├── services/          # API services and utilities
│   ├── types/             # TypeScript type definitions
│   ├── utils/             # Helper functions and utilities
│   └── styles/            # Global styles and Tailwind config
├── package.json
├── vite.config.ts
├── tailwind.config.js
└── tsconfig.json
```

## 🔧 Configuration

### API Integration
Configure the backend API URL in your environment file:
```env
VITE_API_URL=https://your-api-domain.com/api
```

### Theming & Branding
Customize the admin panel appearance in:
- `tailwind.config.js` - Color scheme and design tokens
- `src/components/settings/GeneralSettings.tsx` - Platform branding

### Authentication
The admin panel includes:
- JWT-based authentication
- Role-based access control
- Session management
- Secure logout functionality

## 📱 Responsive Design

The admin panel is fully responsive and optimized for:
- **Desktop**: Full-featured dashboard experience
- **Tablet**: Optimized layouts with collapsible sidebars
- **Mobile**: Touch-friendly interface with drawer navigation

## 🚀 Key Features Breakdown

### Dashboard
- Real-time metrics and KPIs
- Interactive charts and graphs
- Quick action buttons
- Recent activity feed
- System health monitoring

### User Management
- Advanced user search and filtering
- Bulk user operations
- User profile management
- Role and permission assignments
- Activity tracking

### Content Management
- Content approval workflows
- Bulk content operations
- Content analytics
- Version control
- Content security features

### Payment Management
- Multi-gateway integration
- Transaction monitoring
- Refund processing
- Revenue analytics
- Payment configuration

### Analytics & Reporting
- User engagement metrics
- Revenue analytics
- Performance dashboards
- Custom reports
- Data export capabilities

### Settings & Configuration
- Platform customization
- Security configuration
- Payment gateway setup
- Notification management
- System maintenance

## 🔒 Security Features

- **Authentication**: Secure JWT-based authentication
- **Authorization**: Role-based access control (RBAC)
- **Audit Logging**: Comprehensive activity tracking
- **Session Management**: Secure session handling
- **Data Protection**: Encryption and secure data handling

## 📊 Performance Optimizations

- **Code Splitting**: Automatic code splitting with Vite
- **Lazy Loading**: Lazy load routes and components
- **Caching**: Intelligent API response caching
- **Virtualization**: Virtual scrolling for large datasets
- **Optimization**: Bundle optimization and tree shaking

## 🧪 Testing

```bash
# Run unit tests
npm run test

# Run integration tests
npm run test:integration

# Run e2e tests
npm run test:e2e
```

## 📈 Monitoring & Analytics

The admin panel includes built-in monitoring for:
- User engagement metrics
- System performance
- Error tracking
- API response times
- User behavior analytics

## 🌐 Internationalization (i18n)

- Multi-language support (English, Hindi)
- RTL (Right-to-Left) language support
- Localized date and number formatting
- Cultural adaptations

## 🚀 Deployment

### Development
```bash
npm run dev
```

### Production Build
```bash
npm run build
npm run preview
```

### Docker Deployment
```bash
docker build -t rjwu-admin .
docker run -p 3002:3002 rjwu-admin
```

## 📝 API Integration

The admin panel integrates with the RJWU backend API for:
- User management operations
- Content management workflows
- Payment processing
- Analytics data
- System configuration

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Email: <EMAIL>
- Documentation: [Admin Panel Docs](https://docs.rjwu.com/admin)
- Issues: [GitHub Issues](https://github.com/rjwu/admin-panel/issues)

## 🗺️ Roadmap

- [ ] Advanced reporting and dashboards
- [ ] AI-powered analytics and insights
- [ ] Mobile app for admin functions
- [ ] Advanced workflow automation
- [ ] Integration with external tools
- [ ] Multi-tenant architecture support

---

**Built with ❤️ for RJWU EduTech Platform**
