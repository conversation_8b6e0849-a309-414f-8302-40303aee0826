import React, { useState } from 'react'
import { useForm } from 'react-hook-form'
import { LoadingSpinner } from '../LoadingSpinner'
import { CreditCardIcon, CheckCircleIcon, XCircleIcon } from '@heroicons/react/24/outline'

interface PaymentGateway {
  id: string
  name: string
  enabled: boolean
  testMode: boolean
  credentials: {
    [key: string]: string
  }
  fees: {
    percentage: number
    fixed: number
  }
  supportedMethods: string[]
  status: 'active' | 'inactive' | 'error'
}

interface PaymentSettingsForm {
  defaultCurrency: string
  defaultGateway: string
  enableTestMode: boolean
  paymentTimeout: number
  autoRefundTimeout: number
  minimumAmount: number
  maximumAmount: number
  enableWallets: boolean
  enableEMI: boolean
  enableInternational: boolean
  taxRate: number
  processingFee: number
  enableCoupons: boolean
  enableReferralCredits: boolean
}

export const PaymentSettings: React.FC = () => {
  const [loading, setLoading] = useState(false)
  const [activeGateway, setActiveGateway] = useState<string>('razorpay')

  const { register, handleSubmit, formState: { errors } } = useForm<PaymentSettingsForm>({
    defaultValues: {
      defaultCurrency: 'INR',
      defaultGateway: 'razorpay',
      enableTestMode: false,
      paymentTimeout: 15,
      autoRefundTimeout: 7,
      minimumAmount: 100,
      maximumAmount: 100000,
      enableWallets: true,
      enableEMI: true,
      enableInternational: false,
      taxRate: 18,
      processingFee: 2.5,
      enableCoupons: true,
      enableReferralCredits: true,
    }
  })

  const [gateways, setGateways] = useState<PaymentGateway[]>([
    {
      id: 'razorpay',
      name: 'Razorpay',
      enabled: true,
      testMode: false,
      credentials: {
        keyId: 'rzp_test_****',
        keySecret: '****',
        webhookSecret: '****'
      },
      fees: {
        percentage: 2.0,
        fixed: 0
      },
      supportedMethods: ['card', 'netbanking', 'wallet', 'upi'],
      status: 'active'
    },
    {
      id: 'payu',
      name: 'PayU',
      enabled: true,
      testMode: false,
      credentials: {
        merchantKey: 'gtKFFx',
        merchantSalt: '****'
      },
      fees: {
        percentage: 2.9,
        fixed: 0
      },
      supportedMethods: ['card', 'netbanking', 'wallet'],
      status: 'active'
    },
    {
      id: 'phonepe',
      name: 'PhonePe',
      enabled: false,
      testMode: true,
      credentials: {
        merchantId: '****',
        saltKey: '****',
        saltIndex: '1'
      },
      fees: {
        percentage: 1.8,
        fixed: 0
      },
      supportedMethods: ['upi', 'wallet'],
      status: 'inactive'
    },
    {
      id: 'stripe',
      name: 'Stripe',
      enabled: false,
      testMode: true,
      credentials: {
        publishableKey: 'pk_test_****',
        secretKey: 'sk_test_****',
        webhookSecret: 'whsec_****'
      },
      fees: {
        percentage: 2.9,
        fixed: 30
      },
      supportedMethods: ['card'],
      status: 'inactive'
    }
  ])

  const onSubmit = async (data: PaymentSettingsForm) => {
    setLoading(true)
    try {
      // API call to save payment settings
      console.log('Payment settings:', data)
      await new Promise(resolve => setTimeout(resolve, 1000)) // Mock API call
    } catch (error) {
      console.error('Error saving payment settings:', error)
    } finally {
      setLoading(false)
    }
  }

  const toggleGateway = (gatewayId: string) => {
    setGateways(prev => 
      prev.map(gateway => 
        gateway.id === gatewayId 
          ? { ...gateway, enabled: !gateway.enabled }
          : gateway
      )
    )
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />
      case 'error':
        return <XCircleIcon className="h-5 w-5 text-red-500" />
      default:
        return <div className="h-2 w-2 bg-gray-400 rounded-full" />
    }
  }

  return (
    <div className="space-y-6">
      {/* Payment Gateways */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center space-x-2 mb-6">
          <CreditCardIcon className="h-5 w-5 text-primary-600" />
          <h3 className="text-lg font-medium text-gray-900">Payment Gateways</h3>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {gateways.map((gateway) => (
            <div
              key={gateway.id}
              className={`border-2 rounded-lg p-4 cursor-pointer transition-colors ${
                gateway.enabled
                  ? 'border-primary-200 bg-primary-50'
                  : 'border-gray-200 bg-gray-50'
              }`}
              onClick={() => setActiveGateway(gateway.id)}
            >
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center space-x-3">
                  {getStatusIcon(gateway.status)}
                  <h4 className="font-medium text-gray-900">{gateway.name}</h4>
                </div>
                <button
                  onClick={(e) => {
                    e.stopPropagation()
                    toggleGateway(gateway.id)
                  }}
                  className={`px-3 py-1 rounded-full text-xs font-medium ${
                    gateway.enabled
                      ? 'bg-green-100 text-green-800'
                      : 'bg-gray-100 text-gray-800'
                  }`}
                >
                  {gateway.enabled ? 'Enabled' : 'Disabled'}
                </button>
              </div>

              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-500">Fee:</span>
                  <span className="text-gray-900">
                    {gateway.fees.percentage}%
                    {gateway.fees.fixed > 0 && ` + ₹${gateway.fees.fixed}`}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">Methods:</span>
                  <span className="text-gray-900">{gateway.supportedMethods.length}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">Mode:</span>
                  <span className={`${gateway.testMode ? 'text-yellow-600' : 'text-green-600'}`}>
                    {gateway.testMode ? 'Test' : 'Live'}
                  </span>
                </div>
              </div>

              <div className="mt-3 flex flex-wrap gap-1">
                {gateway.supportedMethods.map((method) => (
                  <span
                    key={method}
                    className="px-2 py-1 bg-white rounded text-xs text-gray-600 border"
                  >
                    {method.toUpperCase()}
                  </span>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Gateway Configuration */}
      {activeGateway && (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">
            {gateways.find(g => g.id === activeGateway)?.name} Configuration
          </h3>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            {Object.entries(gateways.find(g => g.id === activeGateway)?.credentials || {}).map(([key, value]) => (
              <div key={key}>
                <label className="block text-sm font-medium text-gray-700 capitalize">
                  {key.replace(/([A-Z])/g, ' $1').trim()}
                </label>
                <input
                  type="password"
                  defaultValue={value}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                />
              </div>
            ))}
          </div>

          <div className="mt-4 flex items-center space-x-4">
            <button className="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700">
              Test Connection
            </button>
            <button className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50">
              Save Configuration
            </button>
          </div>
        </div>
      )}

      {/* General Payment Settings */}
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">General Settings</h3>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <div>
              <label htmlFor="defaultCurrency" className="block text-sm font-medium text-gray-700">
                Default Currency
              </label>
              <select
                {...register('defaultCurrency')}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
              >
                <option value="INR">INR - Indian Rupee</option>
                <option value="USD">USD - US Dollar</option>
                <option value="EUR">EUR - Euro</option>
                <option value="GBP">GBP - British Pound</option>
              </select>
            </div>

            <div>
              <label htmlFor="defaultGateway" className="block text-sm font-medium text-gray-700">
                Default Gateway
              </label>
              <select
                {...register('defaultGateway')}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
              >
                {gateways.filter(g => g.enabled).map(gateway => (
                  <option key={gateway.id} value={gateway.id}>
                    {gateway.name}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label htmlFor="paymentTimeout" className="block text-sm font-medium text-gray-700">
                Payment Timeout (minutes)
              </label>
              <input
                {...register('paymentTimeout', {
                  required: 'Payment timeout is required',
                  min: { value: 5, message: 'Minimum 5 minutes' },
                  max: { value: 60, message: 'Maximum 60 minutes' }
                })}
                type="number"
                min="5"
                max="60"
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
              />
              {errors.paymentTimeout && (
                <p className="mt-1 text-sm text-red-600">{errors.paymentTimeout.message}</p>
              )}
            </div>

            <div>
              <label htmlFor="autoRefundTimeout" className="block text-sm font-medium text-gray-700">
                Auto Refund Timeout (days)
              </label>
              <input
                {...register('autoRefundTimeout', {
                  required: 'Auto refund timeout is required',
                  min: { value: 1, message: 'Minimum 1 day' },
                  max: { value: 30, message: 'Maximum 30 days' }
                })}
                type="number"
                min="1"
                max="30"
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
              />
              {errors.autoRefundTimeout && (
                <p className="mt-1 text-sm text-red-600">{errors.autoRefundTimeout.message}</p>
              )}
            </div>

            <div>
              <label htmlFor="minimumAmount" className="block text-sm font-medium text-gray-700">
                Minimum Amount (₹)
              </label>
              <input
                {...register('minimumAmount', {
                  required: 'Minimum amount is required',
                  min: { value: 1, message: 'Must be greater than 0' }
                })}
                type="number"
                min="1"
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
              />
              {errors.minimumAmount && (
                <p className="mt-1 text-sm text-red-600">{errors.minimumAmount.message}</p>
              )}
            </div>

            <div>
              <label htmlFor="maximumAmount" className="block text-sm font-medium text-gray-700">
                Maximum Amount (₹)
              </label>
              <input
                {...register('maximumAmount', {
                  required: 'Maximum amount is required',
                  min: { value: 100, message: 'Must be at least ₹100' }
                })}
                type="number"
                min="100"
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
              />
              {errors.maximumAmount && (
                <p className="mt-1 text-sm text-red-600">{errors.maximumAmount.message}</p>
              )}
            </div>
          </div>
        </div>

        {/* Payment Features */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Payment Features</h3>
          
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <label htmlFor="enableWallets" className="text-sm font-medium text-gray-900">
                  Enable Digital Wallets
                </label>
                <p className="text-sm text-gray-500">
                  Allow payments through Paytm, PhonePe, Google Pay, etc.
                </p>
              </div>
              <input
                {...register('enableWallets')}
                type="checkbox"
                className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <label htmlFor="enableEMI" className="text-sm font-medium text-gray-900">
                  Enable EMI Options
                </label>
                <p className="text-sm text-gray-500">
                  Allow customers to pay in installments
                </p>
              </div>
              <input
                {...register('enableEMI')}
                type="checkbox"
                className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <label htmlFor="enableInternational" className="text-sm font-medium text-gray-900">
                  Enable International Payments
                </label>
                <p className="text-sm text-gray-500">
                  Accept payments from international cards
                </p>
              </div>
              <input
                {...register('enableInternational')}
                type="checkbox"
                className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <label htmlFor="enableCoupons" className="text-sm font-medium text-gray-900">
                  Enable Coupon System
                </label>
                <p className="text-sm text-gray-500">
                  Allow discount coupons and promo codes
                </p>
              </div>
              <input
                {...register('enableCoupons')}
                type="checkbox"
                className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <label htmlFor="enableReferralCredits" className="text-sm font-medium text-gray-900">
                  Enable Referral Credits
                </label>
                <p className="text-sm text-gray-500">
                  Allow users to earn and spend referral credits
                </p>
              </div>
              <input
                {...register('enableReferralCredits')}
                type="checkbox"
                className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
              />
            </div>
          </div>
        </div>

        {/* Fees & Taxes */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Fees & Taxes</h3>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <div>
              <label htmlFor="taxRate" className="block text-sm font-medium text-gray-700">
                Tax Rate (%)
              </label>
              <input
                {...register('taxRate', {
                  required: 'Tax rate is required',
                  min: { value: 0, message: 'Cannot be negative' },
                  max: { value: 50, message: 'Cannot exceed 50%' }
                })}
                type="number"
                step="0.01"
                min="0"
                max="50"
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
              />
              {errors.taxRate && (
                <p className="mt-1 text-sm text-red-600">{errors.taxRate.message}</p>
              )}
            </div>

            <div>
              <label htmlFor="processingFee" className="block text-sm font-medium text-gray-700">
                Processing Fee (%)
              </label>
              <input
                {...register('processingFee', {
                  required: 'Processing fee is required',
                  min: { value: 0, message: 'Cannot be negative' },
                  max: { value: 10, message: 'Cannot exceed 10%' }
                })}
                type="number"
                step="0.01"
                min="0"
                max="10"
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
              />
              {errors.processingFee && (
                <p className="mt-1 text-sm text-red-600">{errors.processingFee.message}</p>
              )}
            </div>
          </div>
        </div>

        {/* Test Mode Warning */}
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <ExclamationTriangleIcon className="h-5 w-5 text-yellow-400" />
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-yellow-800">
                Test Mode Active
              </h3>
              <div className="mt-2 text-sm text-yellow-700">
                <p>
                  Some payment gateways are in test mode. No real transactions will be processed.
                  Remember to switch to live mode before going to production.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Save Button */}
        <div className="flex justify-end">
          <button
            type="submit"
            disabled={loading}
            className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"
          >
            {loading ? (
              <LoadingSpinner size="sm" />
            ) : (
              'Save Payment Settings'
            )}
          </button>
        </div>
      </form>
    </div>
  )
}
