{"name": "rjwu-mobile", "version": "1.0.0", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "start": "react-native start", "test": "jest", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "build:android": "cd android && ./gradlew assembleRelease", "build:ios": "cd ios && xcodebuild -workspace RJWUMobile.xcworkspace -scheme RJWUMobile -configuration Release -archivePath build/RJWUMobile.xcarchive archive", "clean": "react-native clean-project-auto"}, "dependencies": {"@react-native-async-storage/async-storage": "^1.19.5", "@react-native-community/netinfo": "^10.0.0", "@react-native-firebase/app": "^18.6.1", "@react-native-firebase/messaging": "^18.6.1", "@react-navigation/bottom-tabs": "^6.5.11", "@react-navigation/native": "^6.1.9", "@react-navigation/native-stack": "^6.9.17", "@reduxjs/toolkit": "^1.9.7", "react": "18.2.0", "react-native": "0.72.7", "react-native-biometrics": "^3.0.1", "react-native-crypto-js": "^1.0.0", "react-native-device-info": "^10.11.0", "react-native-fs": "^2.20.0", "react-native-gesture-handler": "^2.13.4", "react-native-i18n": "^2.0.15", "react-native-image-cache-wrapper": "^0.4.0", "react-native-jailmonkey": "^2.8.0", "react-native-keychain": "^8.1.3", "react-native-linear-gradient": "^2.8.3", "react-native-mmkv": "^2.10.2", "react-native-permissions": "^3.10.1", "react-native-reanimated": "^3.5.4", "react-native-safe-area-context": "^4.7.4", "react-native-screens": "^3.27.0", "react-native-splash-screen": "^3.3.0", "react-native-ssl-pinning": "^1.5.1", "react-native-svg": "^13.14.0", "react-native-vector-icons": "^10.0.2", "react-native-video": "^5.2.1", "react-redux": "^8.1.3", "redux-persist": "^6.0.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/eslint-config": "^0.72.2", "@react-native/metro-config": "^0.72.11", "@tsconfig/react-native": "^3.0.0", "@types/react": "^18.0.24", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.2.1", "eslint": "^8.19.0", "jest": "^29.2.1", "metro-react-native-babel-preset": "0.76.8", "prettier": "^2.4.1", "react-test-renderer": "18.2.0", "typescript": "4.8.4"}, "jest": {"preset": "react-native"}}