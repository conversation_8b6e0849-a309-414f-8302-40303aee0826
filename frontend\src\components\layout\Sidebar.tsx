import { Link, useLocation } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { 
  Home, 
  BookOpen, 
  FileText, 
  Award, 
  Users, 
  Settings, 
  BarChart3,
  Video,
  Calendar,
  Bell,
  X
} from 'lucide-react';
import { useAuth } from '@/hooks/useAuth';
import { cn } from '@/utils/cn';

interface SidebarProps {
  open: boolean;
  onClose: () => void;
}

const Sidebar = ({ open, onClose }: SidebarProps) => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const location = useLocation();

  const getNavigationItems = () => {
    const baseItems = [
      {
        name: t('navigation.dashboard'),
        href: '/dashboard',
        icon: Home,
      },
      {
        name: t('navigation.courses'),
        href: '/my-courses',
        icon: BookOpen,
      },
      {
        name: t('navigation.tests'),
        href: '/tests',
        icon: FileText,
      },
      {
        name: t('navigation.certificates'),
        href: '/certificates',
        icon: Award,
      },
    ];

    if (user?.role === 'student') {
      return [
        ...baseItems,
        {
          name: t('navigation.liveClasses'),
          href: '/live-classes',
          icon: Video,
        },
      ];
    }

    if (user?.role === 'teacher') {
      return [
        ...baseItems,
        {
          name: 'Content Management',
          href: '/content',
          icon: FileText,
        },
        {
          name: t('navigation.liveClasses'),
          href: '/live-classes',
          icon: Video,
        },
        {
          name: 'Analytics',
          href: '/analytics',
          icon: BarChart3,
        },
      ];
    }

    if (user?.role === 'admin') {
      return [
        {
          name: t('navigation.dashboard'),
          href: '/dashboard/admin',
          icon: Home,
        },
        {
          name: 'User Management',
          href: '/admin/users',
          icon: Users,
        },
        {
          name: 'Course Management',
          href: '/admin/courses',
          icon: BookOpen,
        },
        {
          name: 'Test Management',
          href: '/admin/tests',
          icon: FileText,
        },
        {
          name: 'Analytics',
          href: '/admin/analytics',
          icon: BarChart3,
        },
        {
          name: 'System Settings',
          href: '/admin/settings',
          icon: Settings,
        },
      ];
    }

    return baseItems;
  };

  const navigationItems = getNavigationItems();

  const isActive = (href: string) => {
    if (href === '/dashboard') {
      return location.pathname === '/dashboard' || 
             location.pathname.startsWith('/dashboard/');
    }
    return location.pathname.startsWith(href);
  };

  return (
    <>
      {/* Mobile overlay */}
      {open && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={onClose}
        />
      )}

      {/* Sidebar */}
      <div className={cn(
        "fixed top-0 left-0 h-full w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out z-50 lg:translate-x-0 lg:static lg:inset-0",
        open ? "translate-x-0" : "-translate-x-full"
      )}>
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b border-gray-200 lg:hidden">
            <span className="text-lg font-semibold text-gray-900">Menu</span>
            <button
              onClick={onClose}
              className="p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100"
            >
              <X size={20} />
            </button>
          </div>

          {/* Navigation */}
          <nav className="flex-1 px-4 py-6 space-y-2 overflow-y-auto">
            {navigationItems.map((item) => {
              const Icon = item.icon;
              const active = isActive(item.href);
              
              return (
                <Link
                  key={item.name}
                  to={item.href}
                  onClick={onClose}
                  className={cn(
                    "flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors duration-200",
                    active
                      ? "bg-primary-50 text-primary-700 border-r-2 border-primary-600"
                      : "text-gray-700 hover:bg-gray-50 hover:text-gray-900"
                  )}
                >
                  <Icon 
                    size={20} 
                    className={cn(
                      "mr-3",
                      active ? "text-primary-600" : "text-gray-400"
                    )} 
                  />
                  {item.name}
                </Link>
              );
            })}
          </nav>

          {/* Footer */}
          <div className="p-4 border-t border-gray-200">
            <div className="flex items-center">
              {user?.avatar ? (
                <img
                  src={user.avatar}
                  alt={user.name}
                  className="w-10 h-10 rounded-full"
                />
              ) : (
                <div className="w-10 h-10 bg-primary-600 rounded-full flex items-center justify-center">
                  <span className="text-white text-sm font-medium">
                    {user?.name?.charAt(0).toUpperCase()}
                  </span>
                </div>
              )}
              <div className="ml-3 flex-1">
                <p className="text-sm font-medium text-gray-900 truncate">
                  {user?.name}
                </p>
                <p className="text-xs text-gray-500 capitalize">
                  {user?.role}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default Sidebar;
