import React from 'react'
import { Dialog, Transition } from '@headlessui/react'
import { useForm } from 'react-hook-form'
import { XMarkIcon, CurrencyRupeeIcon } from '@heroicons/react/24/outline'
import { LoadingSpinner } from '../LoadingSpinner'

interface RefundModalProps {
  open: boolean
  onClose: () => void
  payment?: any
}

interface RefundForm {
  amount: number
  reason: string
  type: 'full' | 'partial'
  notes: string
}

export const RefundModal: React.FC<RefundModalProps> = ({ open, onClose, payment }) => {
  const [loading, setLoading] = React.useState(false)
  
  const originalAmount = payment ? parseInt(payment.amount.replace(/[₹,]/g, '')) : 0
  
  const { register, handleSubmit, formState: { errors }, reset, watch } = useForm<RefundForm>({
    defaultValues: {
      amount: originalAmount,
      type: 'full',
      reason: '',
      notes: '',
    }
  })

  const watchedType = watch('type')

  React.useEffect(() => {
    if (payment) {
      reset({
        amount: originalAmount,
        type: 'full',
        reason: '',
        notes: '',
      })
    }
  }, [payment, originalAmount, reset])

  const onSubmit = async (data: RefundForm) => {
    setLoading(true)
    try {
      // API call to process refund
      console.log('Refund data:', data)
      await new Promise(resolve => setTimeout(resolve, 1500)) // Mock API call
      onClose()
    } catch (error) {
      console.error('Error processing refund:', error)
    } finally {
      setLoading(false)
    }
  }

  if (!payment) return null

  return (
    <Transition.Root show={open} as={React.Fragment}>
      <Dialog as="div" className="relative z-50" onClose={onClose}>
        <Transition.Child
          as={React.Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
        </Transition.Child>

        <div className="fixed inset-0 z-10 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <Transition.Child
              as={React.Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
              enterTo="opacity-100 translate-y-0 sm:scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 translate-y-0 sm:scale-100"
              leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            >
              <Dialog.Panel className="relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6">
                <div className="absolute right-0 top-0 hidden pr-4 pt-4 sm:block">
                  <button
                    type="button"
                    className="rounded-md bg-white text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
                    onClick={onClose}
                  >
                    <XMarkIcon className="h-6 w-6" />
                  </button>
                </div>

                <div className="sm:flex sm:items-start">
                  <div className="mx-auto flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                    <CurrencyRupeeIcon className="h-6 w-6 text-red-600" />
                  </div>
                  <div className="mt-3 text-center sm:ml-4 sm:mt-0 sm:text-left w-full">
                    <Dialog.Title as="h3" className="text-base font-semibold leading-6 text-gray-900">
                      Process Refund
                    </Dialog.Title>

                    {/* Payment Details */}
                    <div className="mt-4 bg-gray-50 rounded-lg p-4">
                      <h4 className="text-sm font-medium text-gray-900 mb-2">Payment Details</h4>
                      <dl className="grid grid-cols-1 gap-2 text-sm">
                        <div className="flex justify-between">
                          <dt className="text-gray-500">Transaction ID:</dt>
                          <dd className="font-medium text-gray-900">{payment.transactionId}</dd>
                        </div>
                        <div className="flex justify-between">
                          <dt className="text-gray-500">Student:</dt>
                          <dd className="font-medium text-gray-900">{payment.student}</dd>
                        </div>
                        <div className="flex justify-between">
                          <dt className="text-gray-500">Course:</dt>
                          <dd className="font-medium text-gray-900">{payment.course}</dd>
                        </div>
                        <div className="flex justify-between">
                          <dt className="text-gray-500">Original Amount:</dt>
                          <dd className="font-medium text-gray-900">{payment.amount}</dd>
                        </div>
                        <div className="flex justify-between">
                          <dt className="text-gray-500">Gateway:</dt>
                          <dd className="font-medium text-gray-900">{payment.gateway}</dd>
                        </div>
                      </dl>
                    </div>

                    <form onSubmit={handleSubmit(onSubmit)} className="mt-6 space-y-4">
                      {/* Refund Type */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Refund Type
                        </label>
                        <div className="grid grid-cols-2 gap-3">
                          <label className="relative flex cursor-pointer rounded-lg border p-4 focus:outline-none">
                            <input
                              {...register('type', { required: 'Refund type is required' })}
                              type="radio"
                              value="full"
                              className="sr-only"
                            />
                            <span className="flex flex-1">
                              <span className="flex flex-col">
                                <span className="block text-sm font-medium text-gray-900">
                                  Full Refund
                                </span>
                                <span className="mt-1 flex items-center text-sm text-gray-500">
                                  {payment.amount}
                                </span>
                              </span>
                            </span>
                            <svg
                              className={`h-5 w-5 text-primary-600 ${
                                watchedType === 'full' ? 'block' : 'hidden'
                              }`}
                              viewBox="0 0 20 20"
                              fill="currentColor"
                            >
                              <path
                                fillRule="evenodd"
                                d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                                clipRule="evenodd"
                              />
                            </svg>
                          </label>

                          <label className="relative flex cursor-pointer rounded-lg border p-4 focus:outline-none">
                            <input
                              {...register('type')}
                              type="radio"
                              value="partial"
                              className="sr-only"
                            />
                            <span className="flex flex-1">
                              <span className="flex flex-col">
                                <span className="block text-sm font-medium text-gray-900">
                                  Partial Refund
                                </span>
                                <span className="mt-1 flex items-center text-sm text-gray-500">
                                  Custom amount
                                </span>
                              </span>
                            </span>
                            <svg
                              className={`h-5 w-5 text-primary-600 ${
                                watchedType === 'partial' ? 'block' : 'hidden'
                              }`}
                              viewBox="0 0 20 20"
                              fill="currentColor"
                            >
                              <path
                                fillRule="evenodd"
                                d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                                clipRule="evenodd"
                              />
                            </svg>
                          </label>
                        </div>
                      </div>

                      {/* Refund Amount */}
                      {watchedType === 'partial' && (
                        <div>
                          <label htmlFor="amount" className="block text-sm font-medium text-gray-700">
                            Refund Amount (₹)
                          </label>
                          <input
                            {...register('amount', {
                              required: 'Amount is required',
                              min: { value: 1, message: 'Amount must be greater than 0' },
                              max: { value: originalAmount, message: `Amount cannot exceed ${originalAmount}` }
                            })}
                            type="number"
                            max={originalAmount}
                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                          />
                          {errors.amount && (
                            <p className="mt-1 text-sm text-red-600">{errors.amount.message}</p>
                          )}
                        </div>
                      )}

                      {/* Reason */}
                      <div>
                        <label htmlFor="reason" className="block text-sm font-medium text-gray-700">
                          Reason for Refund
                        </label>
                        <select
                          {...register('reason', { required: 'Reason is required' })}
                          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                        >
                          <option value="">Select a reason</option>
                          <option value="student_request">Student Request</option>
                          <option value="technical_issue">Technical Issue</option>
                          <option value="course_cancelled">Course Cancelled</option>
                          <option value="duplicate_payment">Duplicate Payment</option>
                          <option value="quality_issue">Quality Issue</option>
                          <option value="other">Other</option>
                        </select>
                        {errors.reason && (
                          <p className="mt-1 text-sm text-red-600">{errors.reason.message}</p>
                        )}
                      </div>

                      {/* Additional Notes */}
                      <div>
                        <label htmlFor="notes" className="block text-sm font-medium text-gray-700">
                          Additional Notes
                        </label>
                        <textarea
                          {...register('notes')}
                          rows={3}
                          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                          placeholder="Any additional information about this refund..."
                        />
                      </div>

                      {/* Warning */}
                      <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
                        <div className="flex">
                          <div className="flex-shrink-0">
                            <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                              <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                            </svg>
                          </div>
                          <div className="ml-3">
                            <h3 className="text-sm font-medium text-yellow-800">
                              Refund Processing
                            </h3>
                            <div className="mt-2 text-sm text-yellow-700">
                              <p>
                                Refunds typically take 5-7 business days to reflect in the student's account.
                                The student will be notified via email once the refund is processed.
                              </p>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div className="mt-5 sm:mt-4 sm:flex sm:flex-row-reverse">
                        <button
                          type="submit"
                          disabled={loading}
                          className="inline-flex w-full justify-center rounded-md bg-red-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-red-500 sm:ml-3 sm:w-auto disabled:opacity-50"
                        >
                          {loading ? (
                            <LoadingSpinner size="sm" />
                          ) : (
                            'Process Refund'
                          )}
                        </button>
                        <button
                          type="button"
                          className="mt-3 inline-flex w-full justify-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 sm:mt-0 sm:w-auto"
                          onClick={onClose}
                        >
                          Cancel
                        </button>
                      </div>
                    </form>
                  </div>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition.Root>
  )
}
