import { Server as SocketIOServer, Socket } from 'socket.io';
import jwt from 'jsonwebtoken';
import WebRTCService from '../services/WebRTCService';
import LiveClassChatService from '../services/LiveClassChatService';
import LiveClassService from '../services/LiveClassService';

interface AuthenticatedSocket extends Socket {
  userId?: string;
  user?: any;
}

export function setupLiveClassSocket(io: SocketIOServer): void {
  // Initialize WebRTC service
  WebRTCService.initialize(io);

  // Authentication middleware for socket connections
  io.use(async (socket: AuthenticatedSocket, next) => {
    try {
      const token = socket.handshake.auth.token || socket.handshake.headers.authorization?.replace('Bearer ', '');
      
      if (!token) {
        return next(new Error('Authentication token required'));
      }

      const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any;
      socket.userId = decoded.id;
      socket.user = decoded;
      
      next();
    } catch (error) {
      next(new Error('Invalid authentication token'));
    }
  });

  io.on('connection', (socket: AuthenticatedSocket) => {
    console.log(`User ${socket.userId} connected to live class socket`);

    // Live Class Events
    socket.on('join-live-class', async (data) => {
      try {
        const { liveClassId, connectionInfo } = data;
        
        // Join the live class room
        socket.join(`live-class-${liveClassId}`);
        
        // Get live class info
        const liveClass = await LiveClassService.getLiveClassById(liveClassId);
        if (!liveClass) {
          socket.emit('error', { message: 'Live class not found' });
          return;
        }

        // Join as participant
        const participant = await LiveClassService.joinLiveClass(
          liveClassId,
          socket.userId!,
          { ...connectionInfo, socketId: socket.id }
        );

        socket.emit('joined-live-class', {
          liveClass,
          participant,
          roomId: `live-class-${liveClassId}`
        });

        // Notify others about new participant
        socket.to(`live-class-${liveClassId}`).emit('participant-joined', {
          participant: {
            id: participant.id,
            userId: participant.user,
            role: participant.role,
            permissions: participant.permissions,
            joinedAt: participant.joinedAt
          }
        });

      } catch (error: any) {
        socket.emit('error', { message: error.message });
      }
    });

    socket.on('leave-live-class', async (data) => {
      try {
        const { liveClassId } = data;
        
        await LiveClassService.leaveLiveClass(liveClassId, socket.userId!);
        socket.leave(`live-class-${liveClassId}`);
        
        socket.to(`live-class-${liveClassId}`).emit('participant-left', {
          userId: socket.userId
        });

        socket.emit('left-live-class', { liveClassId });
      } catch (error: any) {
        socket.emit('error', { message: error.message });
      }
    });

    // Chat Events
    socket.on('send-chat-message', async (data) => {
      try {
        const { liveClassId, message, type = 'text', metadata } = data;
        
        const chatMessage = await LiveClassChatService.sendMessage({
          liveClassId,
          userId: socket.userId!,
          message,
          type,
          metadata
        });

        // Broadcast to all participants in the live class
        io.to(`live-class-${liveClassId}`).emit('new-chat-message', chatMessage);
      } catch (error: any) {
        socket.emit('chat-error', { message: error.message });
      }
    });

    socket.on('react-to-message', async (data) => {
      try {
        const { messageId, emoji, liveClassId } = data;
        
        await LiveClassChatService.addReaction(messageId, socket.userId!, emoji);
        
        io.to(`live-class-${liveClassId}`).emit('message-reaction', {
          messageId,
          emoji,
          userId: socket.userId
        });
      } catch (error: any) {
        socket.emit('chat-error', { message: error.message });
      }
    });

    // Poll Events
    socket.on('poll-created', (data) => {
      const { liveClassId, poll } = data;
      socket.to(`live-class-${liveClassId}`).emit('new-poll', poll);
    });

    socket.on('poll-started', (data) => {
      const { liveClassId, poll } = data;
      io.to(`live-class-${liveClassId}`).emit('poll-started', poll);
    });

    socket.on('poll-closed', (data) => {
      const { liveClassId, poll, results } = data;
      io.to(`live-class-${liveClassId}`).emit('poll-closed', { poll, results });
    });

    socket.on('poll-response', (data) => {
      const { liveClassId, pollId, userId } = data;
      // Only notify poll creator/moderators about new responses
      socket.to(`live-class-${liveClassId}`).emit('poll-response-received', {
        pollId,
        userId,
        timestamp: new Date()
      });
    });

    // Screen Sharing Events
    socket.on('start-screen-share', async (data) => {
      try {
        const { liveClassId } = data;
        
        // Check permissions
        const participant = await LiveClassService.getParticipants(liveClassId);
        const currentParticipant = participant.find(p => p.user.toString() === socket.userId);
        
        if (!currentParticipant?.permissions.canScreenShare) {
          socket.emit('error', { message: 'Not authorized to share screen' });
          return;
        }

        socket.to(`live-class-${liveClassId}`).emit('screen-share-started', {
          userId: socket.userId,
          socketId: socket.id
        });
      } catch (error: any) {
        socket.emit('error', { message: error.message });
      }
    });

    socket.on('stop-screen-share', (data) => {
      const { liveClassId } = data;
      socket.to(`live-class-${liveClassId}`).emit('screen-share-stopped', {
        userId: socket.userId
      });
    });

    // WebRTC Signaling Events
    socket.on('webrtc-offer', (data) => {
      const { to, offer, liveClassId } = data;
      socket.to(to).emit('webrtc-offer', {
        from: socket.id,
        offer,
        liveClassId
      });
    });

    socket.on('webrtc-answer', (data) => {
      const { to, answer, liveClassId } = data;
      socket.to(to).emit('webrtc-answer', {
        from: socket.id,
        answer,
        liveClassId
      });
    });

    socket.on('webrtc-ice-candidate', (data) => {
      const { to, candidate, liveClassId } = data;
      socket.to(to).emit('webrtc-ice-candidate', {
        from: socket.id,
        candidate,
        liveClassId
      });
    });

    // Moderation Events
    socket.on('mute-participant', async (data) => {
      try {
        const { liveClassId, participantId } = data;
        
        await LiveClassService.updateParticipantPermissions(
          liveClassId,
          participantId,
          { canSpeak: false },
          socket.userId!
        );

        io.to(`live-class-${liveClassId}`).emit('participant-muted', {
          participantId,
          mutedBy: socket.userId
        });
      } catch (error: any) {
        socket.emit('error', { message: error.message });
      }
    });

    socket.on('unmute-participant', async (data) => {
      try {
        const { liveClassId, participantId } = data;
        
        await LiveClassService.updateParticipantPermissions(
          liveClassId,
          participantId,
          { canSpeak: true },
          socket.userId!
        );

        io.to(`live-class-${liveClassId}`).emit('participant-unmuted', {
          participantId,
          unmutedBy: socket.userId
        });
      } catch (error: any) {
        socket.emit('error', { message: error.message });
      }
    });

    socket.on('remove-participant', async (data) => {
      try {
        const { liveClassId, participantId, reason } = data;
        
        await LiveClassService.removeParticipant(
          liveClassId,
          participantId,
          socket.userId!,
          reason
        );

        // Find the participant's socket and disconnect them
        const participantSockets = await io.in(`live-class-${liveClassId}`).fetchSockets();
        const participantSocket = participantSockets.find(s => 
          (s as any).userId === participantId
        );

        if (participantSocket) {
          participantSocket.emit('removed-from-live-class', {
            reason: reason || 'Removed by moderator'
          });
          participantSocket.leave(`live-class-${liveClassId}`);
        }

        io.to(`live-class-${liveClassId}`).emit('participant-removed', {
          participantId,
          removedBy: socket.userId,
          reason
        });
      } catch (error: any) {
        socket.emit('error', { message: error.message });
      }
    });

    // Connection Quality Events
    socket.on('connection-quality-update', (data) => {
      const { liveClassId, quality } = data;
      socket.to(`live-class-${liveClassId}`).emit('participant-connection-quality', {
        userId: socket.userId,
        quality
      });
    });

    // Raise Hand Events
    socket.on('raise-hand', (data) => {
      const { liveClassId } = data;
      socket.to(`live-class-${liveClassId}`).emit('hand-raised', {
        userId: socket.userId,
        timestamp: new Date()
      });
    });

    socket.on('lower-hand', (data) => {
      const { liveClassId } = data;
      socket.to(`live-class-${liveClassId}`).emit('hand-lowered', {
        userId: socket.userId
      });
    });

    // Handle disconnection
    socket.on('disconnect', async () => {
      console.log(`User ${socket.userId} disconnected from live class socket`);
      
      // Leave all live class rooms
      const rooms = Array.from(socket.rooms);
      const liveClassRooms = rooms.filter(room => room.startsWith('live-class-'));
      
      for (const room of liveClassRooms) {
        const liveClassId = room.replace('live-class-', '');
        try {
          await LiveClassService.leaveLiveClass(liveClassId, socket.userId!);
          socket.to(room).emit('participant-left', {
            userId: socket.userId
          });
        } catch (error) {
          console.error('Error handling disconnect:', error);
        }
      }
    });
  });
}
