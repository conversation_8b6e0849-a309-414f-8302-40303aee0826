import mongoose, { Schema } from 'mongoose';
import { ITest } from '@/types';

const questionSchema = new Schema({
  question: {
    type: String,
    required: [true, 'Question text is required']
  },
  type: {
    type: String,
    enum: ['mcq', 'descriptive'],
    required: [true, 'Question type is required']
  },
  options: [{
    type: String
  }],
  correctAnswer: {
    type: Schema.Types.Mixed,
    required: [true, 'Correct answer is required']
  },
  marks: {
    type: Number,
    required: [true, 'Question marks are required'],
    min: [1, 'Marks must be at least 1']
  },
  explanation: {
    type: String
  }
});

const testSchema = new Schema<ITest>({
  title: {
    type: String,
    required: [true, 'Test title is required'],
    trim: true,
    maxlength: [200, 'Test title cannot exceed 200 characters']
  },
  description: {
    type: String,
    required: [true, 'Test description is required'],
    maxlength: [1000, 'Description cannot exceed 1000 characters']
  },
  type: {
    type: String,
    enum: ['mcq', 'descriptive', 'mixed'],
    required: [true, 'Test type is required']
  },
  chapterId: {
    type: Schema.Types.ObjectId,
    ref: 'Chapter'
  },
  subjectId: {
    type: Schema.Types.ObjectId,
    ref: 'Subject',
    required: [true, 'Subject ID is required']
  },
  questions: [questionSchema],
  duration: {
    type: Number,
    required: [true, 'Test duration is required'],
    min: [1, 'Duration must be at least 1 minute']
  },
  totalMarks: {
    type: Number,
    required: [true, 'Total marks are required'],
    min: [1, 'Total marks must be at least 1']
  },
  passingMarks: {
    type: Number,
    required: [true, 'Passing marks are required'],
    min: [0, 'Passing marks cannot be negative']
  },
  attempts: {
    type: Number,
    default: 1,
    min: [1, 'Attempts must be at least 1']
  },
  isActive: {
    type: Boolean,
    default: true
  },
  accessLevel: {
    type: String,
    enum: ['free', 'premium', 'batch_only'],
    default: 'batch_only'
  },
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  }
}, {
  timestamps: true
});

testSchema.pre('save', function(next) {
  this.totalMarks = this.questions.reduce((total, question) => total + question.marks, 0);
  next();
});

testSchema.index({ subjectId: 1 });
testSchema.index({ chapterId: 1 });
testSchema.index({ type: 1 });
testSchema.index({ accessLevel: 1 });
testSchema.index({ isActive: 1 });

export default mongoose.model<ITest>('Test', testSchema);
