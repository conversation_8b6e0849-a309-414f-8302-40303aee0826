import React, { useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Animated,
  Dimensions,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import { useNavigation } from '@react-navigation/native';
import { useTheme } from '@/context/ThemeContext';
import { useI18n } from '@/context/I18nContext';
import { useAppDispatch, useAppSelector } from '@/hooks/redux';
import { StorageService } from '@/services/storage';
import { STORAGE_KEYS } from '@/constants';

const { width, height } = Dimensions.get('window');

export const SplashScreen: React.FC = () => {
  const navigation = useNavigation();
  const { colors } = useTheme();
  const { t } = useI18n();
  const dispatch = useAppDispatch();
  const { isAuthenticated } = useAppSelector(state => state.auth);
  
  const fadeAnim = new Animated.Value(0);
  const scaleAnim = new Animated.Value(0.8);

  useEffect(() => {
    startAnimations();
    checkInitialRoute();
  }, []);

  const startAnimations = () => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        tension: 50,
        friction: 8,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const checkInitialRoute = async () => {
    try {
      // Simulate loading time
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Check if onboarding was completed
      const onboardingCompleted = StorageService.getBoolean(STORAGE_KEYS.ONBOARDING_COMPLETED);
      
      if (!onboardingCompleted) {
        navigation.navigate('Onboarding' as never);
        return;
      }

      // Check authentication status
      if (isAuthenticated) {
        navigation.navigate('Main' as never);
      } else {
        navigation.navigate('Auth' as never);
      }
    } catch (error) {
      console.error('Splash screen error:', error);
      navigation.navigate('Auth' as never);
    }
  };

  return (
    <LinearGradient
      colors={[colors.primary, colors.secondary]}
      style={styles.container}
    >
      <Animated.View
        style={[
          styles.content,
          {
            opacity: fadeAnim,
            transform: [{ scale: scaleAnim }],
          },
        ]}
      >
        <View style={styles.logoContainer}>
          <Text style={[styles.logo, { color: colors.background }]}>
            RJWU
          </Text>
          <Text style={[styles.subtitle, { color: colors.background }]}>
            EduTech
          </Text>
        </View>
        
        <View style={styles.taglineContainer}>
          <Text style={[styles.tagline, { color: colors.background }]}>
            Your Gateway to Quality Education
          </Text>
        </View>
      </Animated.View>
      
      <View style={styles.footer}>
        <Text style={[styles.version, { color: colors.background }]}>
          Version 1.0.0
        </Text>
      </View>
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: 40,
  },
  logo: {
    fontSize: 48,
    fontWeight: 'bold',
    letterSpacing: 2,
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 20,
    fontWeight: '300',
    letterSpacing: 1,
    opacity: 0.9,
  },
  taglineContainer: {
    marginTop: 60,
  },
  tagline: {
    fontSize: 16,
    textAlign: 'center',
    opacity: 0.8,
    fontWeight: '300',
  },
  footer: {
    position: 'absolute',
    bottom: 40,
    alignItems: 'center',
  },
  version: {
    fontSize: 12,
    opacity: 0.7,
  },
});
