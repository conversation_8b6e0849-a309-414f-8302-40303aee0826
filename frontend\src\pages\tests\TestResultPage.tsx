import { useParams } from 'react-router-dom';

const TestResultPage = () => {
  const { testId } = useParams();

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
        <p className="text-gray-500 text-center">
          Test result page for test {testId} is under development.
        </p>
      </div>
    </div>
  );
};

export default TestResultPage;
