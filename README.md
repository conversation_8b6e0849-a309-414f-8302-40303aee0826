# RJWU EduTech Platform

A comprehensive educational technology platform designed for competitive exams, initially targeting Rajasthan state exams with scalability for all educational content.

## 🚀 Features

- **Multi-Platform Support**: Web, Android, iOS, and Desktop applications
- **Multi-Language**: Hindi and English support
- **Role-Based Access**: Students, Teachers, and Admins with dedicated panels
- **Content Management**: Videos, Notes, Tests, Live Classes, and Discussions
- **Security**: Watermarking, session control, and content protection
- **Payment Integration**: Multiple gateways with EMI support
- **Real-time Analytics**: Progress tracking and performance insights

## 🏗️ Architecture

```
RJWU Platform
├── backend/          # Node.js API server
├── frontend/         # React.js web application  
├── admin/           # Admin panel (React.js)
├── mobile/          # React Native app
├── desktop/         # Electron desktop app
└── shared/          # Shared utilities and types
```

## 🛠️ Tech Stack

**Frontend:**
- React.js with TypeScript
- Tailwind CSS for styling
- React Query for state management
- React Router for navigation

**Backend:**
- Node.js with Express.js
- MongoDB with Mongoose
- Socket.io for real-time features
- JWT for authentication

**Mobile:**
- React Native with TypeScript
- Native modules for security features

**Desktop:**
- Electron with React.js

## 🚀 Quick Start

1. **Install all dependencies:**
   ```bash
   npm run install:all
   ```

2. **Start development servers:**
   ```bash
   npm run dev
   ```

3. **Build for production:**
   ```bash
   npm run build
   ```

## 📱 Platform Components

### Student Panel
- Course dashboard with progress tracking
- Video classes with offline support
- Test and assessment system
- Doubt resolution system
- Discussion forums
- Study analytics

### Teacher Panel
- Content upload and management
- Live class hosting
- Student progress monitoring
- Test creation and grading
- Doubt resolution management

### Admin Panel
- Platform configuration
- User management
- Content moderation
- Payment and subscription management
- Analytics and reporting
- Security settings

## 🔧 Configuration

Environment variables are required for each service. See individual README files in each directory for specific setup instructions.

## 📋 Development Guidelines

1. Follow TypeScript strict mode
2. Use ESLint and Prettier for code formatting
3. Write unit tests for critical functionality
4. Follow Git conventional commits
5. Use feature branches for development

## 🔐 Security Features

- Dynamic watermarking on videos
- Session management (one device per user)
- Screenshot and screen recording restrictions
- Content encryption and DRM
- Role-based access control

## 📊 Monitoring

- Real-time analytics dashboard
- Performance monitoring
- Error tracking and logging
- User behavior analytics

## 🤝 Contributing

Please read our contributing guidelines before submitting PRs.

## 📄 License

This project is licensed under the MIT License.
