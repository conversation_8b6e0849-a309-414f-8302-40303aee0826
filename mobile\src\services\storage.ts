import { MMKV } from 'react-native-mmkv';
import Keychain from 'react-native-keychain';

// Regular storage for non-sensitive data
const storage = new MMKV({
  id: 'rjwu-app-storage',
});

// Encrypted storage for sensitive data
const secureStorage = new MMKV({
  id: 'rjwu-secure-storage',
  encryptionKey: 'rjwu-secure-key-2024',
});

export class StorageService {
  // Regular storage methods
  static setItem(key: string, value: string): void {
    try {
      storage.set(key, value);
    } catch (error) {
      console.error('Failed to set item:', error);
      throw error;
    }
  }

  static getItem(key: string): string | undefined {
    try {
      return storage.getString(key);
    } catch (error) {
      console.error('Failed to get item:', error);
      return undefined;
    }
  }

  static removeItem(key: string): void {
    try {
      storage.delete(key);
    } catch (error) {
      console.error('Failed to remove item:', error);
    }
  }

  static setObject(key: string, value: any): void {
    try {
      storage.set(key, JSON.stringify(value));
    } catch (error) {
      console.error('Failed to set object:', error);
      throw error;
    }
  }

  static getObject<T>(key: string): T | null {
    try {
      const value = storage.getString(key);
      return value ? JSON.parse(value) : null;
    } catch (error) {
      console.error('Failed to get object:', error);
      return null;
    }
  }

  static setBoolean(key: string, value: boolean): void {
    try {
      storage.set(key, value);
    } catch (error) {
      console.error('Failed to set boolean:', error);
      throw error;
    }
  }

  static getBoolean(key: string): boolean {
    try {
      return storage.getBoolean(key) ?? false;
    } catch (error) {
      console.error('Failed to get boolean:', error);
      return false;
    }
  }

  static setNumber(key: string, value: number): void {
    try {
      storage.set(key, value);
    } catch (error) {
      console.error('Failed to set number:', error);
      throw error;
    }
  }

  static getNumber(key: string): number | undefined {
    try {
      return storage.getNumber(key);
    } catch (error) {
      console.error('Failed to get number:', error);
      return undefined;
    }
  }

  // Secure storage methods using MMKV encryption
  static setSecureItem(key: string, value: string): void {
    try {
      secureStorage.set(key, value);
    } catch (error) {
      console.error('Failed to set secure item:', error);
      throw error;
    }
  }

  static getSecureItem(key: string): string | undefined {
    try {
      return secureStorage.getString(key);
    } catch (error) {
      console.error('Failed to get secure item:', error);
      return undefined;
    }
  }

  static removeSecureItem(key: string): void {
    try {
      secureStorage.delete(key);
    } catch (error) {
      console.error('Failed to remove secure item:', error);
    }
  }

  static setSecureObject(key: string, value: any): void {
    try {
      secureStorage.set(key, JSON.stringify(value));
    } catch (error) {
      console.error('Failed to set secure object:', error);
      throw error;
    }
  }

  static getSecureObject<T>(key: string): T | null {
    try {
      const value = secureStorage.getString(key);
      return value ? JSON.parse(value) : null;
    } catch (error) {
      console.error('Failed to get secure object:', error);
      return null;
    }
  }

  // Keychain methods for ultra-sensitive data
  static async setKeychainItem(
    key: string,
    value: string,
    options?: Keychain.Options
  ): Promise<void> {
    try {
      await Keychain.setInternetCredentials(
        key,
        key,
        value,
        {
          accessControl: Keychain.ACCESS_CONTROL.BIOMETRY_CURRENT_SET,
          authenticationType: Keychain.AUTHENTICATION_TYPE.BIOMETRICS,
          ...options,
        }
      );
    } catch (error) {
      console.error('Failed to set keychain item:', error);
      throw error;
    }
  }

  static async getKeychainItem(
    key: string,
    options?: Keychain.Options
  ): Promise<string | null> {
    try {
      const credentials = await Keychain.getInternetCredentials(key, {
        authenticationType: Keychain.AUTHENTICATION_TYPE.BIOMETRICS,
        ...options,
      });
      
      if (credentials && credentials.password) {
        return credentials.password;
      }
      
      return null;
    } catch (error) {
      console.error('Failed to get keychain item:', error);
      return null;
    }
  }

  static async removeKeychainItem(key: string): Promise<void> {
    try {
      await Keychain.resetInternetCredentials(key);
    } catch (error) {
      console.error('Failed to remove keychain item:', error);
    }
  }

  // Utility methods
  static getAllKeys(): string[] {
    try {
      return storage.getAllKeys();
    } catch (error) {
      console.error('Failed to get all keys:', error);
      return [];
    }
  }

  static getAllSecureKeys(): string[] {
    try {
      return secureStorage.getAllKeys();
    } catch (error) {
      console.error('Failed to get all secure keys:', error);
      return [];
    }
  }

  static clearAll(): void {
    try {
      storage.clearAll();
    } catch (error) {
      console.error('Failed to clear all storage:', error);
    }
  }

  static clearAllSecure(): void {
    try {
      secureStorage.clearAll();
    } catch (error) {
      console.error('Failed to clear all secure storage:', error);
    }
  }

  static async clearAllKeychain(): Promise<void> {
    try {
      await Keychain.resetGenericPassword();
    } catch (error) {
      console.error('Failed to clear keychain:', error);
    }
  }

  // Storage info
  static getStorageSize(): number {
    try {
      const keys = storage.getAllKeys();
      let totalSize = 0;
      
      keys.forEach(key => {
        const value = storage.getString(key);
        if (value) {
          totalSize += value.length;
        }
      });
      
      return totalSize;
    } catch (error) {
      console.error('Failed to get storage size:', error);
      return 0;
    }
  }

  static getSecureStorageSize(): number {
    try {
      const keys = secureStorage.getAllKeys();
      let totalSize = 0;
      
      keys.forEach(key => {
        const value = secureStorage.getString(key);
        if (value) {
          totalSize += value.length;
        }
      });
      
      return totalSize;
    } catch (error) {
      console.error('Failed to get secure storage size:', error);
      return 0;
    }
  }

  // Migration and backup
  static exportData(): Record<string, any> {
    try {
      const keys = storage.getAllKeys();
      const data: Record<string, any> = {};
      
      keys.forEach(key => {
        try {
          const value = storage.getString(key);
          if (value) {
            data[key] = value;
          }
        } catch (error) {
          console.error(`Failed to export key ${key}:`, error);
        }
      });
      
      return data;
    } catch (error) {
      console.error('Failed to export data:', error);
      return {};
    }
  }

  static importData(data: Record<string, any>): void {
    try {
      Object.entries(data).forEach(([key, value]) => {
        try {
          if (typeof value === 'string') {
            storage.set(key, value);
          }
        } catch (error) {
          console.error(`Failed to import key ${key}:`, error);
        }
      });
    } catch (error) {
      console.error('Failed to import data:', error);
    }
  }

  // Cache management
  static setCacheItem(key: string, value: any, ttl: number = 3600000): void {
    try {
      const cacheItem = {
        value,
        timestamp: Date.now(),
        ttl,
      };
      storage.set(`cache_${key}`, JSON.stringify(cacheItem));
    } catch (error) {
      console.error('Failed to set cache item:', error);
    }
  }

  static getCacheItem<T>(key: string): T | null {
    try {
      const cacheData = storage.getString(`cache_${key}`);
      if (!cacheData) return null;
      
      const cacheItem = JSON.parse(cacheData);
      const now = Date.now();
      
      if (now - cacheItem.timestamp > cacheItem.ttl) {
        // Cache expired
        storage.delete(`cache_${key}`);
        return null;
      }
      
      return cacheItem.value;
    } catch (error) {
      console.error('Failed to get cache item:', error);
      return null;
    }
  }

  static clearExpiredCache(): void {
    try {
      const keys = storage.getAllKeys();
      const cacheKeys = keys.filter(key => key.startsWith('cache_'));
      const now = Date.now();
      
      cacheKeys.forEach(key => {
        try {
          const cacheData = storage.getString(key);
          if (cacheData) {
            const cacheItem = JSON.parse(cacheData);
            if (now - cacheItem.timestamp > cacheItem.ttl) {
              storage.delete(key);
            }
          }
        } catch (error) {
          // Remove corrupted cache item
          storage.delete(key);
        }
      });
    } catch (error) {
      console.error('Failed to clear expired cache:', error);
    }
  }
}
