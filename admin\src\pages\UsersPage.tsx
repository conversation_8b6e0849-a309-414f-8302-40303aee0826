import React, { useState } from 'react'
import { Routes, Route } from 'react-router-dom'
import { useQuery } from '@tanstack/react-query'
import { motion } from 'framer-motion'
import {
  MagnifyingGlassIcon,
  FunnelIcon,
  UserPlusIcon,
  EllipsisVerticalIcon,
} from '@heroicons/react/24/outline'
import { adminAPI } from '../services/api'
import { LoadingSpinner } from '../components/LoadingSpinner'
import { DataTable } from '../components/DataTable'
import { UserModal } from '../components/modals/UserModal'
import { BulkActionModal } from '../components/modals/BulkActionModal'

export const UsersPage: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedUsers, setSelectedUsers] = useState<string[]>([])
  const [showUserModal, setShowUserModal] = useState(false)
  const [showBulkModal, setShowBulkModal] = useState(false)
  const [editingUser, setEditingUser] = useState<any>(null)

  const { data: users, isLoading } = useQuery({
    queryKey: ['users', searchTerm],
    queryFn: () => adminAPI.getUsers({ search: searchTerm }),
    select: (response) => response.data,
  })

  const columns = [
    { key: 'name', header: 'Name', sortable: true },
    { key: 'email', header: 'Email', sortable: true },
    { key: 'role', header: 'Role', sortable: true },
    { key: 'batch', header: 'Batch' },
    { key: 'status', header: 'Status' },
    { key: 'lastActive', header: 'Last Active', sortable: true },
    { key: 'actions', header: 'Actions' },
  ]

  const mockUsers = [
    {
      id: '1',
      name: 'Rahul Kumar',
      email: '<EMAIL>',
      role: 'Student',
      batch: 'Batch-2024-CS',
      status: 'Active',
      lastActive: '2 hours ago',
      avatar: null,
    },
    {
      id: '2',
      name: 'Priya Sharma',
      email: '<EMAIL>',
      role: 'Teacher',
      batch: 'Multiple',
      status: 'Active',
      lastActive: '1 day ago',
      avatar: null,
    },
    {
      id: '3',
      name: 'Amit Singh',
      email: '<EMAIL>',
      role: 'Student',
      batch: 'Batch-2024-PCM',
      status: 'Inactive',
      lastActive: '1 week ago',
      avatar: null,
    },
  ]

  const handleBulkAction = (action: string) => {
    setShowBulkModal(true)
  }

  return (
    <Routes>
      <Route path="/" element={
        <div className="space-y-6">
          {/* Header */}
          <div className="md:flex md:items-center md:justify-between">
            <div className="flex-1 min-w-0">
              <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
                User Management
              </h2>
              <p className="mt-1 text-sm text-gray-500">
                Manage students, teachers, and admin users
              </p>
            </div>
            <div className="mt-4 flex md:mt-0 md:ml-4">
              <button
                onClick={() => setShowUserModal(true)}
                className="ml-3 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                <UserPlusIcon className="-ml-1 mr-2 h-5 w-5" />
                Add User
              </button>
            </div>
          </div>

          {/* Filters */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="relative">
                <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search users..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                />
              </div>
              <select className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                <option value="">All Roles</option>
                <option value="student">Students</option>
                <option value="teacher">Teachers</option>
                <option value="admin">Admins</option>
              </select>
              <select className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                <option value="">All Status</option>
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
                <option value="suspended">Suspended</option>
              </select>
            </div>
          </div>

          {/* Bulk Actions */}
          {selectedUsers.length > 0 && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-primary-50 border border-primary-200 rounded-lg p-4"
            >
              <div className="flex items-center justify-between">
                <span className="text-sm text-primary-700">
                  {selectedUsers.length} user{selectedUsers.length > 1 ? 's' : ''} selected
                </span>
                <div className="flex space-x-2">
                  <button
                    onClick={() => handleBulkAction('activate')}
                    className="px-3 py-1 text-sm bg-green-600 text-white rounded hover:bg-green-700"
                  >
                    Activate
                  </button>
                  <button
                    onClick={() => handleBulkAction('deactivate')}
                    className="px-3 py-1 text-sm bg-red-600 text-white rounded hover:bg-red-700"
                  >
                    Deactivate
                  </button>
                  <button
                    onClick={() => handleBulkAction('export')}
                    className="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700"
                  >
                    Export
                  </button>
                </div>
              </div>
            </motion.div>
          )}

          {/* Users Table */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            {isLoading ? (
              <div className="flex items-center justify-center h-64">
                <LoadingSpinner size="lg" />
              </div>
            ) : (
              <DataTable
                columns={columns}
                data={mockUsers}
                onRowSelect={setSelectedUsers}
                selectedRows={selectedUsers}
                onRowEdit={(user) => {
                  setEditingUser(user)
                  setShowUserModal(true)
                }}
              />
            )}
          </div>

          {/* Modals */}
          <UserModal
            open={showUserModal}
            onClose={() => {
              setShowUserModal(false)
              setEditingUser(null)
            }}
            user={editingUser}
          />

          <BulkActionModal
            open={showBulkModal}
            onClose={() => setShowBulkModal(false)}
            selectedCount={selectedUsers.length}
            entityType="users"
          />
        </div>
      } />
    </Routes>
  )
}
