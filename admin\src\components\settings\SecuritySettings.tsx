import React, { useState } from 'react'
import { useForm } from 'react-hook-form'
import { LoadingSpinner } from '../LoadingSpinner'
import { ShieldCheckIcon, ExclamationTriangleIcon } from '@heroicons/react/24/outline'

interface SecuritySettingsForm {
  passwordMinLength: number
  passwordRequireUppercase: boolean
  passwordRequireLowercase: boolean
  passwordRequireNumbers: boolean
  passwordRequireSymbols: boolean
  sessionTimeout: number
  maxLoginAttempts: number
  lockoutDuration: number
  twoFactorRequired: boolean
  emailVerificationRequired: boolean
  phoneVerificationRequired: boolean
  deviceTrackingEnabled: boolean
  ipWhitelistEnabled: boolean
  ipWhitelist: string
  backupCodes: number
  sessionManagement: string
  dataRetentionPeriod: number
  enableAuditLogs: boolean
  logRetentionPeriod: number
  encryptionEnabled: boolean
  sslRequired: boolean
}

export const SecuritySettings: React.FC = () => {
  const [loading, setLoading] = useState(false)
  const [showAdvanced, setShowAdvanced] = useState(false)

  const { register, handleSubmit, formState: { errors }, watch } = useForm<SecuritySettingsForm>({
    defaultValues: {
      passwordMinLength: 8,
      passwordRequireUppercase: true,
      passwordRequireLowercase: true,
      passwordRequireNumbers: true,
      passwordRequireSymbols: false,
      sessionTimeout: 30,
      maxLoginAttempts: 5,
      lockoutDuration: 15,
      twoFactorRequired: false,
      emailVerificationRequired: true,
      phoneVerificationRequired: false,
      deviceTrackingEnabled: true,
      ipWhitelistEnabled: false,
      ipWhitelist: '',
      backupCodes: 10,
      sessionManagement: 'sliding',
      dataRetentionPeriod: 365,
      enableAuditLogs: true,
      logRetentionPeriod: 90,
      encryptionEnabled: true,
      sslRequired: true,
    }
  })

  const watchedValues = {
    twoFactorRequired: watch('twoFactorRequired'),
    ipWhitelistEnabled: watch('ipWhitelistEnabled'),
    enableAuditLogs: watch('enableAuditLogs'),
  }

  const onSubmit = async (data: SecuritySettingsForm) => {
    setLoading(true)
    try {
      // API call to save security settings
      console.log('Security settings:', data)
      await new Promise(resolve => setTimeout(resolve, 1000)) // Mock API call
    } catch (error) {
      console.error('Error saving security settings:', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      {/* Password Policies */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center space-x-2 mb-4">
          <ShieldCheckIcon className="h-5 w-5 text-primary-600" />
          <h3 className="text-lg font-medium text-gray-900">Password Policies</h3>
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
          <div>
            <label htmlFor="passwordMinLength" className="block text-sm font-medium text-gray-700">
              Minimum Password Length
            </label>
            <input
              {...register('passwordMinLength', {
                required: 'Minimum length is required',
                min: { value: 6, message: 'Minimum length must be at least 6' },
                max: { value: 50, message: 'Maximum length cannot exceed 50' }
              })}
              type="number"
              min="6"
              max="50"
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
            />
            {errors.passwordMinLength && (
              <p className="mt-1 text-sm text-red-600">{errors.passwordMinLength.message}</p>
            )}
          </div>

          <div className="space-y-3">
            <label className="block text-sm font-medium text-gray-700">Password Requirements</label>
            <div className="space-y-2">
              <div className="flex items-center">
                <input
                  {...register('passwordRequireUppercase')}
                  type="checkbox"
                  className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                />
                <label className="ml-2 text-sm text-gray-900">
                  Require uppercase letters (A-Z)
                </label>
              </div>
              <div className="flex items-center">
                <input
                  {...register('passwordRequireLowercase')}
                  type="checkbox"
                  className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                />
                <label className="ml-2 text-sm text-gray-900">
                  Require lowercase letters (a-z)
                </label>
              </div>
              <div className="flex items-center">
                <input
                  {...register('passwordRequireNumbers')}
                  type="checkbox"
                  className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                />
                <label className="ml-2 text-sm text-gray-900">
                  Require numbers (0-9)
                </label>
              </div>
              <div className="flex items-center">
                <input
                  {...register('passwordRequireSymbols')}
                  type="checkbox"
                  className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                />
                <label className="ml-2 text-sm text-gray-900">
                  Require special characters (!@#$%^&*)
                </label>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Authentication Settings */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Authentication Settings</h3>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
          <div>
            <label htmlFor="sessionTimeout" className="block text-sm font-medium text-gray-700">
              Session Timeout (minutes)
            </label>
            <input
              {...register('sessionTimeout', {
                required: 'Session timeout is required',
                min: { value: 5, message: 'Minimum 5 minutes' },
                max: { value: 1440, message: 'Maximum 24 hours' }
              })}
              type="number"
              min="5"
              max="1440"
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
            />
            {errors.sessionTimeout && (
              <p className="mt-1 text-sm text-red-600">{errors.sessionTimeout.message}</p>
            )}
          </div>

          <div>
            <label htmlFor="sessionManagement" className="block text-sm font-medium text-gray-700">
              Session Management
            </label>
            <select
              {...register('sessionManagement')}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
            >
              <option value="sliding">Sliding (extends on activity)</option>
              <option value="fixed">Fixed (expires at set time)</option>
              <option value="remember">Remember me option</option>
            </select>
          </div>

          <div>
            <label htmlFor="maxLoginAttempts" className="block text-sm font-medium text-gray-700">
              Max Login Attempts
            </label>
            <input
              {...register('maxLoginAttempts', {
                required: 'Max attempts is required',
                min: { value: 3, message: 'Minimum 3 attempts' },
                max: { value: 10, message: 'Maximum 10 attempts' }
              })}
              type="number"
              min="3"
              max="10"
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
            />
            {errors.maxLoginAttempts && (
              <p className="mt-1 text-sm text-red-600">{errors.maxLoginAttempts.message}</p>
            )}
          </div>

          <div>
            <label htmlFor="lockoutDuration" className="block text-sm font-medium text-gray-700">
              Lockout Duration (minutes)
            </label>
            <input
              {...register('lockoutDuration', {
                required: 'Lockout duration is required',
                min: { value: 5, message: 'Minimum 5 minutes' },
                max: { value: 1440, message: 'Maximum 24 hours' }
              })}
              type="number"
              min="5"
              max="1440"
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
            />
            {errors.lockoutDuration && (
              <p className="mt-1 text-sm text-red-600">{errors.lockoutDuration.message}</p>
            )}
          </div>
        </div>
      </div>

      {/* Two-Factor Authentication */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Two-Factor Authentication</h3>
        
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <label htmlFor="twoFactorRequired" className="text-sm font-medium text-gray-900">
                Require Two-Factor Authentication
              </label>
              <p className="text-sm text-gray-500">
                Force all users to enable 2FA for enhanced security
              </p>
            </div>
            <input
              {...register('twoFactorRequired')}
              type="checkbox"
              className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
            />
          </div>

          {watchedValues.twoFactorRequired && (
            <div className="pl-4 border-l-2 border-primary-200 bg-primary-50 p-4 rounded-md">
              <div className="flex">
                <ExclamationTriangleIcon className="h-5 w-5 text-primary-600 mr-2" />
                <div>
                  <p className="text-sm font-medium text-primary-800">
                    Two-Factor Authentication is Required
                  </p>
                  <p className="text-sm text-primary-700 mt-1">
                    All users will be required to set up 2FA on their next login.
                  </p>
                </div>
              </div>
              
              <div className="mt-4">
                <label htmlFor="backupCodes" className="block text-sm font-medium text-primary-800">
                  Backup Codes per User
                </label>
                <input
                  {...register('backupCodes')}
                  type="number"
                  min="5"
                  max="20"
                  className="mt-1 block w-32 rounded-md border-primary-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                />
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Verification Requirements */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Verification Requirements</h3>
        
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <label htmlFor="emailVerificationRequired" className="text-sm font-medium text-gray-900">
                Email Verification Required
              </label>
              <p className="text-sm text-gray-500">
                Require users to verify their email address during registration
              </p>
            </div>
            <input
              {...register('emailVerificationRequired')}
              type="checkbox"
              className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
            />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <label htmlFor="phoneVerificationRequired" className="text-sm font-medium text-gray-900">
                Phone Verification Required
              </label>
              <p className="text-sm text-gray-500">
                Require users to verify their phone number during registration
              </p>
            </div>
            <input
              {...register('phoneVerificationRequired')}
              type="checkbox"
              className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
            />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <label htmlFor="deviceTrackingEnabled" className="text-sm font-medium text-gray-900">
                Device Tracking
              </label>
              <p className="text-sm text-gray-500">
                Track and notify users of new device logins
              </p>
            </div>
            <input
              {...register('deviceTrackingEnabled')}
              type="checkbox"
              className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
            />
          </div>
        </div>
      </div>

      {/* Advanced Security */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-900">Advanced Security</h3>
          <button
            type="button"
            onClick={() => setShowAdvanced(!showAdvanced)}
            className="text-sm text-primary-600 hover:text-primary-500"
          >
            {showAdvanced ? 'Hide' : 'Show'} Advanced Options
          </button>
        </div>

        {showAdvanced && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <label htmlFor="ipWhitelistEnabled" className="text-sm font-medium text-gray-900">
                  IP Whitelist
                </label>
                <p className="text-sm text-gray-500">
                  Restrict admin access to specific IP addresses
                </p>
              </div>
              <input
                {...register('ipWhitelistEnabled')}
                type="checkbox"
                className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
              />
            </div>

            {watchedValues.ipWhitelistEnabled && (
              <div>
                <label htmlFor="ipWhitelist" className="block text-sm font-medium text-gray-700">
                  Allowed IP Addresses (one per line)
                </label>
                <textarea
                  {...register('ipWhitelist')}
                  rows={4}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                  placeholder="*************&#10;***********/24&#10;2001:db8::/32"
                />
              </div>
            )}

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
              <div>
                <label htmlFor="dataRetentionPeriod" className="block text-sm font-medium text-gray-700">
                  Data Retention Period (days)
                </label>
                <input
                  {...register('dataRetentionPeriod')}
                  type="number"
                  min="30"
                  max="2555"
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                />
              </div>

              <div>
                <label htmlFor="logRetentionPeriod" className="block text-sm font-medium text-gray-700">
                  Log Retention Period (days)
                </label>
                <input
                  {...register('logRetentionPeriod')}
                  type="number"
                  min="7"
                  max="365"
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                />
              </div>
            </div>

            <div className="flex items-center justify-between">
              <div>
                <label htmlFor="enableAuditLogs" className="text-sm font-medium text-gray-900">
                  Enable Audit Logs
                </label>
                <p className="text-sm text-gray-500">
                  Log all administrative actions and security events
                </p>
              </div>
              <input
                {...register('enableAuditLogs')}
                type="checkbox"
                className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <label htmlFor="encryptionEnabled" className="text-sm font-medium text-gray-900">
                  Database Encryption
                </label>
                <p className="text-sm text-gray-500">
                  Encrypt sensitive data at rest
                </p>
              </div>
              <input
                {...register('encryptionEnabled')}
                type="checkbox"
                className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <label htmlFor="sslRequired" className="text-sm font-medium text-gray-900">
                  Force SSL/HTTPS
                </label>
                <p className="text-sm text-gray-500">
                  Redirect all HTTP traffic to HTTPS
                </p>
              </div>
              <input
                {...register('sslRequired')}
                type="checkbox"
                className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
              />
            </div>
          </div>
        )}
      </div>

      {/* Save Button */}
      <div className="flex justify-end">
        <button
          type="submit"
          disabled={loading}
          className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"
        >
          {loading ? (
            <LoadingSpinner size="sm" />
          ) : (
            'Save Security Settings'
          )}
        </button>
      </div>
    </form>
  )
}
