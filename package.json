{"name": "rjwu-edutech-platform", "version": "1.0.0", "description": "Full-scale EduTech Platform for competitive exams", "main": "index.js", "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd backend && npm run dev", "dev:frontend": "cd frontend && npm run dev", "build": "npm run build:backend && npm run build:frontend", "build:backend": "cd backend && npm run build", "build:frontend": "cd frontend && npm run build", "start": "cd backend && npm start", "install:all": "npm install && cd backend && npm install && cd ../frontend && npm install && cd ../admin && npm install && cd ../mobile && npm install", "test": "npm run test:backend && npm run test:frontend", "test:backend": "cd backend && npm test", "test:frontend": "cd frontend && npm test"}, "keywords": ["edutech", "raja<PERSON><PERSON>", "competitive-exams", "e-learning", "rjwu"], "author": "RJWU Team", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}}