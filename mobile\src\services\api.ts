import { APP_CONFIG, SECURITY_CONFIG } from '@/constants';
import { store } from '@/store';
import { SecurityService } from './security';

interface ApiResponse<T = any> {
  success: boolean;
  data: T;
  message?: string;
  error?: string;
}

interface RequestConfig {
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  headers?: Record<string, string>;
  body?: any;
  timeout?: number;
  retries?: number;
}

class ApiService {
  private baseURL: string;
  private defaultHeaders: Record<string, string>;

  constructor() {
    this.baseURL = APP_CONFIG.API_BASE_URL;
    this.defaultHeaders = {
      'Content-Type': 'application/json',
      'X-App-Version': APP_CONFIG.VERSION,
      'X-Platform': 'mobile',
    };
  }

  private async getAuthHeaders(): Promise<Record<string, string>> {
    const state = store.getState();
    const headers: Record<string, string> = {};

    if (state.auth.token) {
      headers.Authorization = `Bearer ${state.auth.token}`;
    }

    if (state.security.sessionId) {
      headers['X-Session-ID'] = state.security.sessionId;
    }

    if (state.security.deviceId) {
      headers['X-Device-ID'] = state.security.deviceId;
    }

    return headers;
  }

  private async validateCertificate(url: string): Promise<boolean> {
    if (!SECURITY_CONFIG.CERTIFICATE_PINS.length) {
      return true;
    }

    try {
      return await SecurityService.validateCertificatePinning(url);
    } catch (error) {
      console.error('Certificate validation failed:', error);
      return false;
    }
  }

  private async request<T>(
    endpoint: string,
    config: RequestConfig
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseURL}${endpoint}`;
    
    // Validate certificate pinning
    if (!(await this.validateCertificate(url))) {
      throw new Error('Certificate validation failed');
    }

    const authHeaders = await this.getAuthHeaders();
    const headers = {
      ...this.defaultHeaders,
      ...authHeaders,
      ...config.headers,
    };

    const requestConfig: RequestInit = {
      method: config.method,
      headers,
      body: config.body ? JSON.stringify(config.body) : undefined,
    };

    const timeout = config.timeout || 30000;
    const retries = config.retries || 3;

    for (let attempt = 0; attempt <= retries; attempt++) {
      try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), timeout);

        const response = await fetch(url, {
          ...requestConfig,
          signal: controller.signal,
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        return data;
      } catch (error) {
        if (attempt === retries) {
          throw error;
        }
        
        // Wait before retry with exponential backoff
        await new Promise(resolve => 
          setTimeout(resolve, Math.pow(2, attempt) * 1000)
        );
      }
    }

    throw new Error('Max retries exceeded');
  }

  async get<T>(endpoint: string, config?: Partial<RequestConfig>): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'GET', ...config });
  }

  async post<T>(
    endpoint: string,
    body?: any,
    config?: Partial<RequestConfig>
  ): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'POST', body, ...config });
  }

  async put<T>(
    endpoint: string,
    body?: any,
    config?: Partial<RequestConfig>
  ): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'PUT', body, ...config });
  }

  async patch<T>(
    endpoint: string,
    body?: any,
    config?: Partial<RequestConfig>
  ): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'PATCH', body, ...config });
  }

  async delete<T>(endpoint: string, config?: Partial<RequestConfig>): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'DELETE', ...config });
  }

  async uploadFile<T>(
    endpoint: string,
    file: {
      uri: string;
      type: string;
      name: string;
    },
    additionalFields?: Record<string, any>
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseURL}${endpoint}`;
    const authHeaders = await this.getAuthHeaders();

    const formData = new FormData();
    formData.append('file', {
      uri: file.uri,
      type: file.type,
      name: file.name,
    } as any);

    if (additionalFields) {
      Object.entries(additionalFields).forEach(([key, value]) => {
        formData.append(key, value);
      });
    }

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        ...authHeaders,
        'Content-Type': 'multipart/form-data',
      },
      body: formData,
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    return response.json();
  }

  async downloadFile(
    url: string,
    onProgress?: (progress: number) => void
  ): Promise<Blob> {
    const response = await fetch(url);
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const contentLength = response.headers.get('Content-Length');
    const total = contentLength ? parseInt(contentLength, 10) : 0;
    let loaded = 0;

    const reader = response.body?.getReader();
    if (!reader) {
      throw new Error('Failed to get response reader');
    }

    const chunks: Uint8Array[] = [];

    while (true) {
      const { done, value } = await reader.read();
      
      if (done) break;
      
      chunks.push(value);
      loaded += value.length;
      
      if (onProgress && total > 0) {
        onProgress((loaded / total) * 100);
      }
    }

    const blob = new Blob(chunks);
    return blob;
  }
}

export const api = new ApiService();
