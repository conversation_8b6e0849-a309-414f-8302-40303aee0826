import mongoose, { Schema } from 'mongoose';
import { IBatch } from '@/types';

const batchSchema = new Schema<IBatch>({
  name: {
    type: String,
    required: [true, 'Batch name is required'],
    trim: true,
    maxlength: [100, 'Batch name cannot exceed 100 characters']
  },
  description: {
    type: String,
    required: [true, 'Batch description is required'],
    maxlength: [500, 'Description cannot exceed 500 characters']
  },
  institutionId: {
    type: String,
    required: [true, 'Institution ID is required']
  },
  subjects: [{
    type: Schema.Types.ObjectId,
    ref: 'Subject'
  }],
  teachers: [{
    type: Schema.Types.ObjectId,
    ref: 'User'
  }],
  students: [{
    type: Schema.Types.ObjectId,
    ref: 'User'
  }],
  startDate: {
    type: Date,
    required: [true, 'Start date is required']
  },
  endDate: {
    type: Date,
    required: [true, 'End date is required'],
    validate: {
      validator: function(this: IBatch, value: Date) {
        return value > this.startDate;
      },
      message: 'End date must be after start date'
    }
  },
  isActive: {
    type: Boolean,
    default: true
  },
  maxStudents: {
    type: Number,
    required: [true, 'Maximum students limit is required'],
    min: [1, 'Maximum students must be at least 1']
  },
  currentStudents: {
    type: Number,
    default: 0,
    min: [0, 'Current students cannot be negative']
  },
  fee: {
    type: Number,
    required: [true, 'Batch fee is required'],
    min: [0, 'Fee cannot be negative']
  },
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  }
}, {
  timestamps: true
});

batchSchema.pre('save', function(next) {
  this.currentStudents = this.students.length;
  next();
});

batchSchema.index({ institutionId: 1 });
batchSchema.index({ isActive: 1 });
batchSchema.index({ startDate: 1, endDate: 1 });
batchSchema.index({ createdBy: 1 });

export default mongoose.model<IBatch>('Batch', batchSchema);
