import React from 'react'
import { motion } from 'framer-motion'
import {
  ClockIcon,
  EyeIcon,
  PlayIcon,
  DocumentCheckIcon,
  ChatBubbleLeftRightIcon,
} from '@heroicons/react/24/outline'

const metrics = [
  {
    name: 'Average Session Duration',
    value: '24m 32s',
    target: '30m',
    progress: 82,
    icon: ClockIcon,
    color: 'bg-blue-500',
  },
  {
    name: 'Video Completion Rate',
    value: '78%',
    target: '85%',
    progress: 92,
    icon: PlayIcon,
    color: 'bg-green-500',
  },
  {
    name: 'Quiz Success Rate',
    value: '84%',
    target: '90%',
    progress: 93,
    icon: DocumentCheckIcon,
    color: 'bg-purple-500',
  },
  {
    name: 'Forum Participation',
    value: '67%',
    target: '75%',
    progress: 89,
    icon: ChatBubbleLeftRightIcon,
    color: 'bg-orange-500',
  },
  {
    name: 'Daily Active Users',
    value: '68%',
    target: '80%',
    progress: 85,
    icon: EyeIcon,
    color: 'bg-indigo-500',
  },
]

const engagementData = [
  { metric: 'Course Completion Rate', value: 76, benchmark: 85 },
  { metric: 'Student Retention (30d)', value: 89, benchmark: 90 },
  { metric: 'Assignment Submission', value: 92, benchmark: 95 },
  { metric: 'Live Class Attendance', value: 73, benchmark: 80 },
  { metric: 'Discussion Forum Posts', value: 45, benchmark: 60 },
]

export const PerformanceMetrics: React.FC = () => {
  return (
    <div className="space-y-6">
      {/* Key Performance Indicators */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
      >
        <h3 className="text-lg font-medium text-gray-900 mb-6">Key Performance Indicators</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {metrics.map((metric, index) => (
            <motion.div
              key={metric.name}
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: index * 0.1 }}
              className="bg-gray-50 rounded-lg p-4"
            >
              <div className="flex items-center justify-between mb-3">
                <div className={`p-2 rounded-md ${metric.color}`}>
                  <metric.icon className="h-5 w-5 text-white" />
                </div>
                <div className="text-right">
                  <p className="text-sm text-gray-500">Target: {metric.target}</p>
                </div>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">{metric.name}</p>
                <p className="text-2xl font-bold text-gray-900 mt-1">{metric.value}</p>
                <div className="mt-3">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-500">Progress</span>
                    <span className="font-medium text-gray-900">{metric.progress}%</span>
                  </div>
                  <div className="mt-1 w-full bg-gray-200 rounded-full h-2">
                    <motion.div
                      initial={{ width: 0 }}
                      animate={{ width: `${metric.progress}%` }}
                      transition={{ delay: index * 0.1 + 0.5, duration: 0.8 }}
                      className={`h-2 rounded-full ${metric.color}`}
                    />
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </motion.div>

      {/* Engagement Metrics vs Benchmarks */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3 }}
        className="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
      >
        <h3 className="text-lg font-medium text-gray-900 mb-6">Engagement vs Industry Benchmarks</h3>
        <div className="space-y-4">
          {engagementData.map((item, index) => (
            <div key={item.metric} className="flex items-center justify-between">
              <div className="flex-1">
                <div className="flex items-center justify-between mb-1">
                  <span className="text-sm font-medium text-gray-900">{item.metric}</span>
                  <div className="flex items-center space-x-4 text-sm">
                    <span className="text-gray-600">Current: {item.value}%</span>
                    <span className="text-gray-500">Benchmark: {item.benchmark}%</span>
                  </div>
                </div>
                <div className="relative">
                  <div className="w-full bg-gray-200 rounded-full h-3">
                    <motion.div
                      initial={{ width: 0 }}
                      animate={{ width: `${(item.value / item.benchmark) * 100}%` }}
                      transition={{ delay: index * 0.1 + 0.5, duration: 0.8 }}
                      className={`h-3 rounded-full ${
                        item.value >= item.benchmark ? 'bg-green-500' : 'bg-yellow-500'
                      }`}
                      style={{ maxWidth: '100%' }}
                    />
                  </div>
                  <div
                    className="absolute top-0 h-3 w-1 bg-gray-600 rounded"
                    style={{ left: '100%', transform: 'translateX(-2px)' }}
                  />
                </div>
              </div>
            </div>
          ))}
        </div>
      </motion.div>

      {/* Recent Activity Summary */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
        className="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
      >
        <h3 className="text-lg font-medium text-gray-900 mb-6">Weekly Activity Summary</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="text-center">
            <div className="text-3xl font-bold text-blue-600">2,847</div>
            <div className="text-sm text-gray-500 mt-1">Total Logins</div>
            <div className="text-xs text-green-600 mt-1">+12% from last week</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-green-600">1,234</div>
            <div className="text-sm text-gray-500 mt-1">Videos Watched</div>
            <div className="text-xs text-green-600 mt-1">+8% from last week</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-purple-600">567</div>
            <div className="text-sm text-gray-500 mt-1">Assignments Submitted</div>
            <div className="text-xs text-red-600 mt-1">-3% from last week</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-orange-600">890</div>
            <div className="text-sm text-gray-500 mt-1">Quiz Attempts</div>
            <div className="text-xs text-green-600 mt-1">+15% from last week</div>
          </div>
        </div>
      </motion.div>
    </div>
  )
}
