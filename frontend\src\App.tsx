import { Routes, Route, Navigate } from 'react-router-dom';
import { useEffect } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { socketService } from '@/services/socketService';

// Layout components
import Layout from '@/components/layout/Layout';
import AuthLayout from '@/components/layout/AuthLayout';
import ProtectedRoute from '@/components/auth/ProtectedRoute';

// Pages
import HomePage from '@/pages/HomePage';
import LoginPage from '@/pages/auth/LoginPage';
import RegisterPage from '@/pages/auth/RegisterPage';
import ForgotPasswordPage from '@/pages/auth/ForgotPasswordPage';
import ResetPasswordPage from '@/pages/auth/ResetPasswordPage';

// Dashboard pages
import StudentDashboard from '@/pages/dashboard/StudentDashboard';
import TeacherDashboard from '@/pages/dashboard/TeacherDashboard';
import AdminDashboard from '@/pages/dashboard/AdminDashboard';

// Course pages
import CoursesPage from '@/pages/courses/CoursesPage';
import CourseDetailPage from '@/pages/courses/CourseDetailPage';
import LessonPage from '@/pages/courses/LessonPage';

// Test pages
import TestsPage from '@/pages/tests/TestsPage';
import TestPage from '@/pages/tests/TestPage';
import TestResultPage from '@/pages/tests/TestResultPage';

// Profile pages
import ProfilePage from '@/pages/profile/ProfilePage';
import SettingsPage from '@/pages/profile/SettingsPage';

// Error pages
import NotFoundPage from '@/pages/errors/NotFoundPage';
import ErrorBoundary from '@/components/common/ErrorBoundary';

function App() {
  const { isAuthenticated, user } = useAuth();

  useEffect(() => {
    // Initialize socket connection for authenticated users
    if (isAuthenticated && user) {
      socketService.connect();
      
      // Cleanup on unmount
      return () => {
        socketService.disconnect();
      };
    }
  }, [isAuthenticated, user]);

  const getDashboardRoute = () => {
    if (!user) return '/';
    
    switch (user.role) {
      case 'admin':
        return '/dashboard/admin';
      case 'teacher':
        return '/dashboard/teacher';
      case 'student':
      default:
        return '/dashboard/student';
    }
  };

  return (
    <ErrorBoundary>
      <div className="min-h-screen bg-gray-50">
        <Routes>
          {/* Public routes */}
          <Route path="/" element={<Layout />}>
            <Route index element={<HomePage />} />
            <Route path="courses" element={<CoursesPage />} />
            <Route path="courses/:id" element={<CourseDetailPage />} />
          </Route>

          {/* Auth routes */}
          <Route path="/auth" element={<AuthLayout />}>
            <Route path="login" element={<LoginPage />} />
            <Route path="register" element={<RegisterPage />} />
            <Route path="forgot-password" element={<ForgotPasswordPage />} />
            <Route path="reset-password" element={<ResetPasswordPage />} />
          </Route>

          {/* Protected routes */}
          <Route path="/" element={<ProtectedRoute><Layout /></ProtectedRoute>}>
            {/* Dashboard routes */}
            <Route path="dashboard">
              <Route 
                index 
                element={<Navigate to={getDashboardRoute()} replace />} 
              />
              <Route 
                path="student" 
                element={
                  <ProtectedRoute allowedRoles={['student']}>
                    <StudentDashboard />
                  </ProtectedRoute>
                } 
              />
              <Route 
                path="teacher" 
                element={
                  <ProtectedRoute allowedRoles={['teacher']}>
                    <TeacherDashboard />
                  </ProtectedRoute>
                } 
              />
              <Route 
                path="admin" 
                element={
                  <ProtectedRoute allowedRoles={['admin']}>
                    <AdminDashboard />
                  </ProtectedRoute>
                } 
              />
            </Route>

            {/* Course routes */}
            <Route path="my-courses" element={<CoursesPage />} />
            <Route path="course/:courseId/lesson/:lessonId" element={<LessonPage />} />

            {/* Test routes */}
            <Route path="tests" element={<TestsPage />} />
            <Route path="test/:testId" element={<TestPage />} />
            <Route path="test/:testId/result" element={<TestResultPage />} />

            {/* Profile routes */}
            <Route path="profile" element={<ProfilePage />} />
            <Route path="settings" element={<SettingsPage />} />
          </Route>

          {/* Redirects */}
          <Route 
            path="/login" 
            element={
              isAuthenticated ? 
                <Navigate to={getDashboardRoute()} replace /> : 
                <Navigate to="/auth/login" replace />
            } 
          />
          <Route 
            path="/register" 
            element={
              isAuthenticated ? 
                <Navigate to={getDashboardRoute()} replace /> : 
                <Navigate to="/auth/register" replace />
            } 
          />

          {/* 404 */}
          <Route path="*" element={<NotFoundPage />} />
        </Routes>
      </div>
    </ErrorBoundary>
  );
}

export default App;
