import React from 'react'
import { Dialog, Transition } from '@headlessui/react'
import { ExclamationTriangleIcon } from '@heroicons/react/24/outline'
import { LoadingSpinner } from '../LoadingSpinner'

interface BulkActionModalProps {
  open: boolean
  onClose: () => void
  selectedCount: number
  entityType: string
  action?: string
}

export const BulkActionModal: React.FC<BulkActionModalProps> = ({
  open,
  onClose,
  selectedCount,
  entityType,
  action = 'update'
}) => {
  const [loading, setLoading] = React.useState(false)
  const [selectedAction, setSelectedAction] = React.useState('activate')

  const handleBulkAction = async () => {
    setLoading(true)
    try {
      // API call for bulk action
      await new Promise(resolve => setTimeout(resolve, 1500)) // Mock API call
      onClose()
    } catch (error) {
      console.error('Bulk action error:', error)
    } finally {
      setLoading(false)
    }
  }

  const getActionDescription = () => {
    switch (selectedAction) {
      case 'activate':
        return `Activate ${selectedCount} ${entityType}`
      case 'deactivate':
        return `Deactivate ${selectedCount} ${entityType}`
      case 'delete':
        return `Delete ${selectedCount} ${entityType}`
      case 'export':
        return `Export ${selectedCount} ${entityType} to CSV`
      default:
        return `Update ${selectedCount} ${entityType}`
    }
  }

  const isDestructive = selectedAction === 'delete'

  return (
    <Transition.Root show={open} as={React.Fragment}>
      <Dialog as="div" className="relative z-50" onClose={onClose}>
        <Transition.Child
          as={React.Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
        </Transition.Child>

        <div className="fixed inset-0 z-10 overflow-y-auto">
          <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
            <Transition.Child
              as={React.Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
              enterTo="opacity-100 translate-y-0 sm:scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 translate-y-0 sm:scale-100"
              leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            >
              <Dialog.Panel className="relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6">
                <div className="sm:flex sm:items-start">
                  <div className={`mx-auto flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full sm:mx-0 sm:h-10 sm:w-10 ${
                    isDestructive ? 'bg-red-100' : 'bg-blue-100'
                  }`}>
                    <ExclamationTriangleIcon className={`h-6 w-6 ${
                      isDestructive ? 'text-red-600' : 'text-blue-600'
                    }`} />
                  </div>
                  <div className="mt-3 text-center sm:ml-4 sm:mt-0 sm:text-left">
                    <Dialog.Title as="h3" className="text-base font-semibold leading-6 text-gray-900">
                      Bulk Action Confirmation
                    </Dialog.Title>
                    <div className="mt-2">
                      <p className="text-sm text-gray-500">
                        You are about to perform a bulk action on {selectedCount} {entityType}.
                      </p>
                    </div>

                    <div className="mt-4">
                      <label htmlFor="action" className="block text-sm font-medium text-gray-700">
                        Select Action
                      </label>
                      <select
                        id="action"
                        value={selectedAction}
                        onChange={(e) => setSelectedAction(e.target.value)}
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                      >
                        <option value="activate">Activate</option>
                        <option value="deactivate">Deactivate</option>
                        <option value="export">Export to CSV</option>
                        <option value="delete">Delete</option>
                      </select>
                    </div>

                    <div className="mt-4 p-3 bg-gray-50 rounded-md">
                      <p className="text-sm font-medium text-gray-900">
                        Action: {getActionDescription()}
                      </p>
                      {isDestructive && (
                        <p className="text-sm text-red-600 mt-1">
                          ⚠️ This action cannot be undone!
                        </p>
                      )}
                    </div>
                  </div>
                </div>
                <div className="mt-5 sm:mt-4 sm:flex sm:flex-row-reverse">
                  <button
                    type="button"
                    disabled={loading}
                    className={`inline-flex w-full justify-center rounded-md px-3 py-2 text-sm font-semibold text-white shadow-sm sm:ml-3 sm:w-auto disabled:opacity-50 ${
                      isDestructive
                        ? 'bg-red-600 hover:bg-red-500'
                        : 'bg-primary-600 hover:bg-primary-500'
                    }`}
                    onClick={handleBulkAction}
                  >
                    {loading ? (
                      <LoadingSpinner size="sm" />
                    ) : (
                      'Confirm Action'
                    )}
                  </button>
                  <button
                    type="button"
                    className="mt-3 inline-flex w-full justify-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 sm:mt-0 sm:w-auto"
                    onClick={onClose}
                  >
                    Cancel
                  </button>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition.Root>
  )
}
