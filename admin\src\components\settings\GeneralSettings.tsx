import React, { useState } from 'react'
import { useForm } from 'react-hook-form'
import { LoadingSpinner } from '../LoadingSpinner'
import { PhotoIcon } from '@heroicons/react/24/outline'

interface GeneralSettingsForm {
  platformName: string
  tagline: string
  description: string
  contactEmail: string
  supportEmail: string
  phone: string
  address: string
  logo: string
  favicon: string
  primaryColor: string
  secondaryColor: string
  accentColor: string
  timezone: string
  dateFormat: string
  currency: string
  language: string
  maintenanceMode: boolean
  registrationEnabled: boolean
  guestAccess: boolean
}

export const GeneralSettings: React.FC = () => {
  const [loading, setLoading] = useState(false)
  const [logoPreview, setLogoPreview] = useState<string | null>(null)

  const { register, handleSubmit, formState: { errors }, watch } = useForm<GeneralSettingsForm>({
    defaultValues: {
      platformName: 'RJWU EduTech',
      tagline: 'Excellence in Education',
      description: 'Premier online education platform for competitive exam preparation',
      contactEmail: '<EMAIL>',
      supportEmail: '<EMAIL>',
      phone: '+91 9876543210',
      address: 'New Delhi, India',
      primaryColor: '#dc2626',
      secondaryColor: '#64748b',
      accentColor: '#3b82f6',
      timezone: 'Asia/Kolkata',
      dateFormat: 'DD/MM/YYYY',
      currency: 'INR',
      language: 'en',
      maintenanceMode: false,
      registrationEnabled: true,
      guestAccess: false,
    }
  })

  const watchedColors = {
    primary: watch('primaryColor'),
    secondary: watch('secondaryColor'),
    accent: watch('accentColor'),
  }

  const onSubmit = async (data: GeneralSettingsForm) => {
    setLoading(true)
    try {
      // API call to save settings
      console.log('General settings:', data)
      await new Promise(resolve => setTimeout(resolve, 1000)) // Mock API call
    } catch (error) {
      console.error('Error saving settings:', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      {/* Basic Information */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Basic Information</h3>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
          <div className="lg:col-span-2">
            <label htmlFor="platformName" className="block text-sm font-medium text-gray-700">
              Platform Name
            </label>
            <input
              {...register('platformName', { required: 'Platform name is required' })}
              type="text"
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
            />
            {errors.platformName && (
              <p className="mt-1 text-sm text-red-600">{errors.platformName.message}</p>
            )}
          </div>

          <div>
            <label htmlFor="tagline" className="block text-sm font-medium text-gray-700">
              Tagline
            </label>
            <input
              {...register('tagline')}
              type="text"
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
            />
          </div>

          <div>
            <label htmlFor="language" className="block text-sm font-medium text-gray-700">
              Default Language
            </label>
            <select
              {...register('language')}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
            >
              <option value="en">English</option>
              <option value="hi">Hindi</option>
              <option value="both">Both (English & Hindi)</option>
            </select>
          </div>

          <div className="lg:col-span-2">
            <label htmlFor="description" className="block text-sm font-medium text-gray-700">
              Platform Description
            </label>
            <textarea
              {...register('description')}
              rows={3}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
            />
          </div>
        </div>
      </div>

      {/* Contact Information */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Contact Information</h3>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
          <div>
            <label htmlFor="contactEmail" className="block text-sm font-medium text-gray-700">
              Contact Email
            </label>
            <input
              {...register('contactEmail', {
                required: 'Contact email is required',
                pattern: {
                  value: /^\S+@\S+$/i,
                  message: 'Invalid email address'
                }
              })}
              type="email"
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
            />
            {errors.contactEmail && (
              <p className="mt-1 text-sm text-red-600">{errors.contactEmail.message}</p>
            )}
          </div>

          <div>
            <label htmlFor="supportEmail" className="block text-sm font-medium text-gray-700">
              Support Email
            </label>
            <input
              {...register('supportEmail', {
                required: 'Support email is required',
                pattern: {
                  value: /^\S+@\S+$/i,
                  message: 'Invalid email address'
                }
              })}
              type="email"
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
            />
            {errors.supportEmail && (
              <p className="mt-1 text-sm text-red-600">{errors.supportEmail.message}</p>
            )}
          </div>

          <div>
            <label htmlFor="phone" className="block text-sm font-medium text-gray-700">
              Phone Number
            </label>
            <input
              {...register('phone')}
              type="tel"
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
            />
          </div>

          <div>
            <label htmlFor="address" className="block text-sm font-medium text-gray-700">
              Address
            </label>
            <input
              {...register('address')}
              type="text"
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
            />
          </div>
        </div>
      </div>

      {/* Branding */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Branding</h3>
        <div className="space-y-4">
          {/* Logo Upload */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Platform Logo</label>
            <div className="flex items-center space-x-4">
              <div className="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center border">
                {logoPreview ? (
                  <img src={logoPreview} alt="Logo" className="w-full h-full object-contain rounded-lg" />
                ) : (
                  <PhotoIcon className="h-8 w-8 text-gray-400" />
                )}
              </div>
              <div>
                <button
                  type="button"
                  className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                >
                  Upload Logo
                </button>
                <p className="text-xs text-gray-500 mt-1">PNG, JPG up to 2MB. Recommended: 200x60px</p>
              </div>
            </div>
          </div>

          {/* Color Scheme */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Color Scheme</label>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label htmlFor="primaryColor" className="block text-xs font-medium text-gray-500 mb-1">
                  Primary Color
                </label>
                <div className="flex items-center space-x-2">
                  <input
                    {...register('primaryColor')}
                    type="color"
                    className="w-12 h-8 rounded border border-gray-300"
                  />
                  <input
                    {...register('primaryColor')}
                    type="text"
                    className="flex-1 text-sm rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                  />
                </div>
              </div>

              <div>
                <label htmlFor="secondaryColor" className="block text-xs font-medium text-gray-500 mb-1">
                  Secondary Color
                </label>
                <div className="flex items-center space-x-2">
                  <input
                    {...register('secondaryColor')}
                    type="color"
                    className="w-12 h-8 rounded border border-gray-300"
                  />
                  <input
                    {...register('secondaryColor')}
                    type="text"
                    className="flex-1 text-sm rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                  />
                </div>
              </div>

              <div>
                <label htmlFor="accentColor" className="block text-xs font-medium text-gray-500 mb-1">
                  Accent Color
                </label>
                <div className="flex items-center space-x-2">
                  <input
                    {...register('accentColor')}
                    type="color"
                    className="w-12 h-8 rounded border border-gray-300"
                  />
                  <input
                    {...register('accentColor')}
                    type="text"
                    className="flex-1 text-sm rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                  />
                </div>
              </div>
            </div>

            {/* Color Preview */}
            <div className="mt-4 p-4 bg-gray-50 rounded-lg">
              <p className="text-sm font-medium text-gray-700 mb-2">Preview</p>
              <div className="flex space-x-2">
                <div 
                  className="w-12 h-8 rounded"
                  style={{ backgroundColor: watchedColors.primary }}
                ></div>
                <div 
                  className="w-12 h-8 rounded"
                  style={{ backgroundColor: watchedColors.secondary }}
                ></div>
                <div 
                  className="w-12 h-8 rounded"
                  style={{ backgroundColor: watchedColors.accent }}
                ></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Regional Settings */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Regional Settings</h3>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
          <div>
            <label htmlFor="timezone" className="block text-sm font-medium text-gray-700">
              Timezone
            </label>
            <select
              {...register('timezone')}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
            >
              <option value="Asia/Kolkata">Asia/Kolkata (IST)</option>
              <option value="UTC">UTC</option>
              <option value="America/New_York">America/New_York (EST)</option>
              <option value="Europe/London">Europe/London (GMT)</option>
            </select>
          </div>

          <div>
            <label htmlFor="dateFormat" className="block text-sm font-medium text-gray-700">
              Date Format
            </label>
            <select
              {...register('dateFormat')}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
            >
              <option value="DD/MM/YYYY">DD/MM/YYYY</option>
              <option value="MM/DD/YYYY">MM/DD/YYYY</option>
              <option value="YYYY-MM-DD">YYYY-MM-DD</option>
            </select>
          </div>

          <div>
            <label htmlFor="currency" className="block text-sm font-medium text-gray-700">
              Currency
            </label>
            <select
              {...register('currency')}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
            >
              <option value="INR">INR (₹)</option>
              <option value="USD">USD ($)</option>
              <option value="EUR">EUR (€)</option>
              <option value="GBP">GBP (£)</option>
            </select>
          </div>
        </div>
      </div>

      {/* Platform Controls */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Platform Controls</h3>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <label htmlFor="maintenanceMode" className="text-sm font-medium text-gray-900">
                Maintenance Mode
              </label>
              <p className="text-sm text-gray-500">
                Put the platform in maintenance mode to prevent user access
              </p>
            </div>
            <input
              {...register('maintenanceMode')}
              type="checkbox"
              className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
            />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <label htmlFor="registrationEnabled" className="text-sm font-medium text-gray-900">
                User Registration
              </label>
              <p className="text-sm text-gray-500">
                Allow new users to register on the platform
              </p>
            </div>
            <input
              {...register('registrationEnabled')}
              type="checkbox"
              className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
            />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <label htmlFor="guestAccess" className="text-sm font-medium text-gray-900">
                Guest Access
              </label>
              <p className="text-sm text-gray-500">
                Allow guests to browse content without registration
              </p>
            </div>
            <input
              {...register('guestAccess')}
              type="checkbox"
              className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
            />
          </div>
        </div>
      </div>

      {/* Save Button */}
      <div className="flex justify-end">
        <button
          type="submit"
          disabled={loading}
          className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"
        >
          {loading ? (
            <LoadingSpinner size="sm" />
          ) : (
            'Save Settings'
          )}
        </button>
      </div>
    </form>
  )
}
