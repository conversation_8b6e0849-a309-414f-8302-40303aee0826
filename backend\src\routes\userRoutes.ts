import express from 'express';
import { body } from 'express-validator';
import { userController } from '@/controllers';
import { authenticate, authorize, validate } from '@/middleware';

const router = express.Router();

const createUserValidation = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email'),
  body('password')
    .isLength({ min: 6 })
    .withMessage('Password must be at least 6 characters long'),
  body('firstName')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('First name must be between 2 and 50 characters'),
  body('lastName')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Last name must be between 2 and 50 characters'),
  body('role')
    .isIn(['student', 'teacher', 'admin'])
    .withMessage('Invalid role'),
  body('phone')
    .optional()
    .isMobilePhone('any')
    .withMessage('Please provide a valid phone number')
];

const updateUserValidation = [
  body('firstName')
    .optional()
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('First name must be between 2 and 50 characters'),
  body('lastName')
    .optional()
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Last name must be between 2 and 50 characters'),
  body('phone')
    .optional()
    .isMobilePhone('any')
    .withMessage('Please provide a valid phone number'),
  body('role')
    .optional()
    .isIn(['student', 'teacher', 'admin'])
    .withMessage('Invalid role')
];

const assignBatchValidation = [
  body('userId')
    .isMongoId()
    .withMessage('Invalid user ID'),
  body('batchId')
    .isMongoId()
    .withMessage('Invalid batch ID')
];

router.use(authenticate);

router.get('/', authorize('admin', 'super_admin'), userController.getAllUsers);
router.get('/stats', authorize('admin', 'super_admin'), userController.getUserStats);
router.get('/:id', authorize('admin', 'super_admin'), userController.getUserById);

router.post('/', authorize('admin', 'super_admin'), validate(createUserValidation), userController.createUser);
router.put('/:id', authorize('admin', 'super_admin'), validate(updateUserValidation), userController.updateUser);
router.delete('/:id', authorize('admin', 'super_admin'), userController.deleteUser);

router.put('/:id/activate', authorize('admin', 'super_admin'), userController.activateUser);
router.post('/assign-batch', authorize('admin', 'super_admin'), validate(assignBatchValidation), userController.assignBatch);
router.post('/remove-batch', authorize('admin', 'super_admin'), validate(assignBatchValidation), userController.removeBatch);

export default router;
