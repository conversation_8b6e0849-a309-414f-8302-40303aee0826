import { Request } from 'express';
import { Document } from 'mongoose';

export interface IUser extends Document {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  role: 'student' | 'teacher' | 'admin' | 'super_admin';
  isActive: boolean;
  isVerified: boolean;
  avatar?: string;
  phone?: string;
  dateOfBirth?: Date;
  address?: {
    street: string;
    city: string;
    state: string;
    pincode: string;
  };
  institutionId?: string;
  batches?: string[];
  subjects?: string[];
  permissions?: string[];
  lastLogin?: Date;
  verifyToken?: string;
  resetPasswordToken?: string;
  resetPasswordExpire?: Date;
  createdAt: Date;
  updatedAt: Date;
  comparePassword(password: string): Promise<boolean>;
  generateAuthToken(): string;
  generateRefreshToken(): string;
}

export interface IBatch extends Document {
  name: string;
  description: string;
  institutionId: string;
  subjects: string[];
  teachers: string[];
  students: string[];
  startDate: Date;
  endDate: Date;
  isActive: boolean;
  maxStudents: number;
  currentStudents: number;
  fee: number;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface ISubject extends Document {
  name: string;
  description: string;
  code: string;
  institutionId: string;
  batchId: string;
  teachers: string[];
  chapters: string[];
  isActive: boolean;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface IChapter extends Document {
  title: string;
  description: string;
  subjectId: string;
  order: number;
  videos: string[];
  notes: string[];
  tests: string[];
  isActive: boolean;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface IVideo extends Document {
  title: string;
  description: string;
  url: string;
  duration: number;
  thumbnail?: string;
  chapterId: string;
  subjectId: string;
  order: number;
  isActive: boolean;
  watermark?: boolean;
  drmProtected?: boolean;
  accessLevel: 'free' | 'premium' | 'batch_only';
  views: number;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface INote extends Document {
  title: string;
  content: string;
  type: 'pdf' | 'text' | 'image';
  url?: string;
  chapterId: string;
  subjectId: string;
  order: number;
  isActive: boolean;
  accessLevel: 'free' | 'premium' | 'batch_only';
  downloads: number;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface ITest extends Document {
  title: string;
  description: string;
  type: 'mcq' | 'descriptive' | 'mixed';
  chapterId?: string;
  subjectId: string;
  questions: {
    question: string;
    type: 'mcq' | 'descriptive';
    options?: string[];
    correctAnswer: string | number;
    marks: number;
    explanation?: string;
  }[];
  duration: number;
  totalMarks: number;
  passingMarks: number;
  attempts: number;
  isActive: boolean;
  accessLevel: 'free' | 'premium' | 'batch_only';
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface IDoubt extends Document {
  title: string;
  description: string;
  studentId: string;
  subjectId: string;
  chapterId?: string;
  status: 'pending' | 'in_progress' | 'resolved' | 'closed';
  priority: 'low' | 'medium' | 'high';
  assignedTo?: string;
  responses: {
    userId: string;
    message: string;
    attachments?: string[];
    createdAt: Date;
  }[];
  attachments?: string[];
  tags?: string[];
  createdAt: Date;
  updatedAt: Date;
}

export interface IPayment extends Document {
  userId: string;
  batchId?: string;
  amount: number;
  currency: string;
  status: 'pending' | 'success' | 'failed' | 'refunded';
  paymentMethod: 'razorpay' | 'paytm' | 'upi' | 'bank_transfer';
  transactionId?: string;
  razorpayOrderId?: string;
  razorpayPaymentId?: string;
  description: string;
  metadata?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

export interface AuthenticatedRequest extends Request {
  user?: IUser;
}

export interface PaginationOptions {
  page: number;
  limit: number;
}

export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  error?: string;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}
