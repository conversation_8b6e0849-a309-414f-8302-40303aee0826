import React, { useState } from 'react'
import { motion } from 'framer-motion'
import {
  ChevronUpIcon,
  ChevronDownIcon,
  EllipsisVerticalIcon,
} from '@heroicons/react/24/outline'
import { Menu } from '@headlessui/react'

interface Column {
  key: string
  header: string
  sortable?: boolean
  render?: (item: any) => React.ReactNode
}

interface DataTableProps {
  columns: Column[]
  data: any[]
  onRowSelect?: (selectedIds: string[]) => void
  selectedRows?: string[]
  onRowEdit?: (item: any) => void
  onRowDelete?: (item: any) => void
}

export const DataTable: React.FC<DataTableProps> = ({
  columns,
  data,
  onRowSelect,
  selectedRows = [],
  onRowEdit,
  onRowDelete,
}) => {
  const [sortConfig, setSortConfig] = useState<{
    key: string
    direction: 'asc' | 'desc'
  } | null>(null)

  const handleSort = (key: string) => {
    let direction: 'asc' | 'desc' = 'asc'
    if (sortConfig && sortConfig.key === key && sortConfig.direction === 'asc') {
      direction = 'desc'
    }
    setSortConfig({ key, direction })
  }

  const sortedData = React.useMemo(() => {
    if (!sortConfig) return data

    return [...data].sort((a, b) => {
      const aValue = a[sortConfig.key]
      const bValue = b[sortConfig.key]

      if (aValue < bValue) return sortConfig.direction === 'asc' ? -1 : 1
      if (aValue > bValue) return sortConfig.direction === 'asc' ? 1 : -1
      return 0
    })
  }, [data, sortConfig])

  const handleSelectAll = (checked: boolean) => {
    if (onRowSelect) {
      onRowSelect(checked ? data.map(item => item.id) : [])
    }
  }

  const handleSelectRow = (id: string, checked: boolean) => {
    if (onRowSelect) {
      const newSelection = checked
        ? [...selectedRows, id]
        : selectedRows.filter(selectedId => selectedId !== id)
      onRowSelect(newSelection)
    }
  }

  const isAllSelected = selectedRows.length === data.length && data.length > 0
  const isIndeterminate = selectedRows.length > 0 && selectedRows.length < data.length

  const renderCell = (item: any, column: Column) => {
    if (column.render) {
      return column.render(item)
    }

    if (column.key === 'actions') {
      return (
        <Menu as="div" className="relative inline-block text-left">
          <Menu.Button className="p-2 rounded-full hover:bg-gray-100">
            <EllipsisVerticalIcon className="h-5 w-5 text-gray-400" />
          </Menu.Button>
          <Menu.Items className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none z-10">
            <div className="py-1">
              {onRowEdit && (
                <Menu.Item>
                  {({ active }) => (
                    <button
                      onClick={() => onRowEdit(item)}
                      className={`${
                        active ? 'bg-gray-100' : ''
                      } block w-full text-left px-4 py-2 text-sm text-gray-700`}
                    >
                      Edit
                    </button>
                  )}
                </Menu.Item>
              )}
              <Menu.Item>
                {({ active }) => (
                  <button
                    className={`${
                      active ? 'bg-gray-100' : ''
                    } block w-full text-left px-4 py-2 text-sm text-gray-700`}
                  >
                    View Details
                  </button>
                )}
              </Menu.Item>
              <Menu.Item>
                {({ active }) => (
                  <button
                    className={`${
                      active ? 'bg-gray-100' : ''
                    } block w-full text-left px-4 py-2 text-sm text-gray-700`}
                  >
                    {item.status === 'Active' ? 'Deactivate' : 'Activate'}
                  </button>
                )}
              </Menu.Item>
              {onRowDelete && (
                <Menu.Item>
                  {({ active }) => (
                    <button
                      onClick={() => onRowDelete(item)}
                      className={`${
                        active ? 'bg-gray-100' : ''
                      } block w-full text-left px-4 py-2 text-sm text-red-600`}
                    >
                      Delete
                    </button>
                  )}
                </Menu.Item>
              )}
            </div>
          </Menu.Items>
        </Menu>
      )
    }

    if (column.key === 'status') {
      const statusColors = {
        Active: 'bg-green-100 text-green-800',
        Inactive: 'bg-gray-100 text-gray-800',
        Suspended: 'bg-red-100 text-red-800',
      }
      return (
        <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${statusColors[item[column.key] as keyof typeof statusColors] || 'bg-gray-100 text-gray-800'}`}>
          {item[column.key]}
        </span>
      )
    }

    return item[column.key]
  }

  return (
    <div className="overflow-x-auto">
      <table className="min-w-full divide-y divide-gray-200">
        <thead className="bg-gray-50">
          <tr>
            {onRowSelect && (
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                <input
                  type="checkbox"
                  checked={isAllSelected}
                  ref={(input) => {
                    if (input) input.indeterminate = isIndeterminate
                  }}
                  onChange={(e) => handleSelectAll(e.target.checked)}
                  className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                />
              </th>
            )}
            {columns.map((column) => (
              <th
                key={column.key}
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
              >
                {column.sortable ? (
                  <button
                    onClick={() => handleSort(column.key)}
                    className="flex items-center space-x-1 hover:text-gray-700"
                  >
                    <span>{column.header}</span>
                    {sortConfig?.key === column.key ? (
                      sortConfig.direction === 'asc' ? (
                        <ChevronUpIcon className="h-4 w-4" />
                      ) : (
                        <ChevronDownIcon className="h-4 w-4" />
                      )
                    ) : (
                      <div className="h-4 w-4" />
                    )}
                  </button>
                ) : (
                  column.header
                )}
              </th>
            ))}
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-200">
          {sortedData.map((item, index) => (
            <motion.tr
              key={item.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.05 }}
              className="hover:bg-gray-50"
            >
              {onRowSelect && (
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  <input
                    type="checkbox"
                    checked={selectedRows.includes(item.id)}
                    onChange={(e) => handleSelectRow(item.id, e.target.checked)}
                    className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                  />
                </td>
              )}
              {columns.map((column) => (
                <td
                  key={column.key}
                  className="px-6 py-4 whitespace-nowrap text-sm text-gray-900"
                >
                  {renderCell(item, column)}
                </td>
              ))}
            </motion.tr>
          ))}
        </tbody>
      </table>
    </div>
  )
}
