# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

node_modules
dist
dist-ssr
*.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Build files
build/
out/

# Testing
coverage/
.nyc_output/

# Cache
.cache/
.parcel-cache/

# OS generated files
Thumbs.db
ehthumbs.db
Desktop.ini

# IDE
*.swp
*.swo
*~

# Temporary files
*.tmp
*.temp
