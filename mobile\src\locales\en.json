{"common": {"loading": "Loading...", "error": "Error", "success": "Success", "cancel": "Cancel", "confirm": "Confirm", "save": "Save", "delete": "Delete", "edit": "Edit", "done": "Done", "retry": "Retry", "back": "Back", "next": "Next", "skip": "<PERSON><PERSON>", "search": "Search", "filter": "Filter", "sort": "Sort", "refresh": "Refresh"}, "auth": {"login": "<PERSON><PERSON>", "register": "Register", "logout": "Logout", "email": "Email", "password": "Password", "confirmPassword": "Confirm Password", "name": "Full Name", "forgotPassword": "Forgot Password?", "loginWithBiometric": "Login with Biometric", "enableBiometric": "Enable Biometric Login", "biometricPrompt": "Use your biometric to authenticate", "loginSuccess": "Login successful", "loginError": "<PERSON><PERSON> failed", "registerSuccess": "Registration successful", "registerError": "Registration failed"}, "dashboard": {"welcome": "Welcome back", "continueLearning": "Continue Learning", "featuredCourses": "Featured Courses", "recentlyWatched": "Recently Watched", "progress": "Progress", "achievements": "Achievements", "notifications": "Notifications"}, "courses": {"allCourses": "All Courses", "myCourses": "My Courses", "categories": "Categories", "featured": "Featured", "recent": "Recent", "popular": "Popular", "completed": "Completed", "inProgress": "In Progress", "notStarted": "Not Started", "duration": "Duration", "lessons": "Lessons", "rating": "Rating", "instructor": "<PERSON><PERSON><PERSON><PERSON>", "description": "Description", "syllabus": "Syllabus", "reviews": "Reviews", "enroll": "Enroll Now", "start": "Start Course", "continue": "Continue", "download": "Download", "share": "Share"}, "video": {"play": "Play", "pause": "Pause", "replay": "Replay", "forward": "Forward", "backward": "Backward", "speed": "Speed", "quality": "Quality", "subtitles": "Subtitles", "fullscreen": "Fullscreen", "exitFullscreen": "Exit Fullscreen", "nextLesson": "Next Lesson", "previousLesson": "Previous Lesson", "completedLesson": "Lesson Completed!"}, "downloads": {"myDownloads": "My Downloads", "downloading": "Downloading", "completed": "Completed", "failed": "Failed", "paused": "Paused", "downloadAll": "Download All", "pauseAll": "Pause All", "resumeAll": "Resume All", "clearCompleted": "Clear Completed", "storage": "Storage Used", "availableSpace": "Available Space", "wifiOnly": "WiFi Only", "downloadQuality": "Download Quality"}, "profile": {"myProfile": "My Profile", "editProfile": "Edit Profile", "settings": "Settings", "achievements": "Achievements", "certificates": "Certificates", "learningStats": "Learning Statistics", "coursesCompleted": "Courses Completed", "hoursLearned": "Hours Learned", "streakDays": "Streak Days", "changePassword": "Change Password", "accountSettings": "Account <PERSON><PERSON>", "privacy": "Privacy", "help": "Help & Support", "about": "About"}, "settings": {"general": "General", "theme": "Theme", "language": "Language", "notifications": "Notifications", "downloads": "Downloads", "video": "Video", "security": "Security", "privacy": "Privacy", "about": "About", "lightMode": "Light Mode", "darkMode": "Dark Mode", "systemDefault": "System Default", "pushNotifications": "Push Notifications", "emailNotifications": "Email Notifications", "courseUpdates": "Course Updates", "promotions": "Promotions", "autoDownload": "Auto Download", "downloadOnWiFi": "Download on WiFi Only", "videoQuality": "Video Quality", "autoPlay": "Auto Play", "biometricLogin": "Biometric Login", "screenProtection": "Screen Protection"}, "notifications": {"noNotifications": "No notifications", "markAllRead": "<PERSON>", "clearAll": "Clear All", "courseUpdate": "Course Update", "newCourse": "New Course Available", "achievement": "Achievement Unlocked", "reminder": "Learning Reminder", "system": "System Notification"}, "errors": {"networkError": "Network connection failed", "serverError": "Server error occurred", "unauthorized": "Unauthorized access", "notFound": "Content not found", "validationError": "Validation error", "securityViolation": "Security violation detected", "downloadFailed": "Download failed", "playbackError": "Video playback error", "biometricError": "Biometric authentication failed", "storageError": "Storage error occurred"}, "onboarding": {"welcome": "Welcome to RJWU EduTech", "subtitle": "Your gateway to quality education", "features": {"learn": {"title": "Learn Anywhere", "description": "Access courses offline and learn at your own pace"}, "secure": {"title": "Secure Learning", "description": "Protected content with advanced security features"}, "progress": {"title": "Track Progress", "description": "Monitor your learning journey and achievements"}}, "getStarted": "Get Started", "alreadyMember": "Already a member? Login"}}