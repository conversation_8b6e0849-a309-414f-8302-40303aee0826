import React from 'react'
import { Dialog, Transition } from '@headlessui/react'
import { XMarkIcon, BellIcon } from '@heroicons/react/24/outline'
import { format } from 'date-fns'

interface NotificationCenterProps {
  open: boolean
  onClose: () => void
}

const notifications = [
  {
    id: '1',
    type: 'info',
    title: 'New user registration',
    message: '5 new students registered in the last hour',
    timestamp: new Date(Date.now() - 5 * 60 * 1000),
    read: false,
  },
  {
    id: '2',
    type: 'warning',
    title: 'Payment gateway issue',
    message: 'Razorpay gateway experiencing delays',
    timestamp: new Date(Date.now() - 15 * 60 * 1000),
    read: false,
  },
  {
    id: '3',
    type: 'success',
    title: 'Content approved',
    message: '12 new videos approved and published',
    timestamp: new Date(Date.now() - 30 * 60 * 1000),
    read: true,
  },
]

export const NotificationCenter: React.FC<NotificationCenterProps> = ({ open, onClose }) => {
  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'success':
        return 'bg-green-100 text-green-600'
      case 'warning':
        return 'bg-yellow-100 text-yellow-600'
      case 'error':
        return 'bg-red-100 text-red-600'
      default:
        return 'bg-blue-100 text-blue-600'
    }
  }

  return (
    <Transition.Root show={open} as={React.Fragment}>
      <Dialog as="div" className="relative z-50" onClose={onClose}>
        <Transition.Child
          as={React.Fragment}
          enter="ease-in-out duration-500"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in-out duration-500"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-hidden">
          <div className="absolute inset-0 overflow-hidden">
            <div className="pointer-events-none fixed inset-y-0 right-0 flex max-w-full pl-10">
              <Transition.Child
                as={React.Fragment}
                enter="transform transition ease-in-out duration-500 sm:duration-700"
                enterFrom="translate-x-full"
                enterTo="translate-x-0"
                leave="transform transition ease-in-out duration-500 sm:duration-700"
                leaveFrom="translate-x-0"
                leaveTo="translate-x-full"
              >
                <Dialog.Panel className="pointer-events-auto w-screen max-w-md">
                  <div className="flex h-full flex-col overflow-y-scroll bg-white shadow-xl">
                    <div className="flex-1 overflow-y-auto px-4 py-6 sm:px-6">
                      <div className="flex items-start justify-between">
                        <Dialog.Title className="text-lg font-medium text-gray-900">
                          Notifications
                        </Dialog.Title>
                        <div className="ml-3 flex h-7 items-center">
                          <button
                            type="button"
                            className="relative -m-2 p-2 text-gray-400 hover:text-gray-500"
                            onClick={onClose}
                          >
                            <XMarkIcon className="h-6 w-6" />
                          </button>
                        </div>
                      </div>

                      <div className="mt-8">
                        <div className="flow-root">
                          <ul className="-my-6 divide-y divide-gray-200">
                            {notifications.map((notification) => (
                              <li key={notification.id} className="flex py-6">
                                <div className={`flex-shrink-0 rounded-full p-2 ${getNotificationIcon(notification.type)}`}>
                                  <BellIcon className="h-5 w-5" />
                                </div>
                                <div className="ml-4 flex flex-1 flex-col">
                                  <div>
                                    <div className="flex justify-between text-base font-medium text-gray-900">
                                      <h3>{notification.title}</h3>
                                      {!notification.read && (
                                        <div className="h-2 w-2 bg-blue-600 rounded-full" />
                                      )}
                                    </div>
                                    <p className="mt-1 text-sm text-gray-500">
                                      {notification.message}
                                    </p>
                                  </div>
                                  <div className="flex flex-1 items-end justify-between text-sm">
                                    <p className="text-gray-500">
                                      {format(notification.timestamp, 'MMM d, HH:mm')}
                                    </p>
                                    <button className="font-medium text-primary-600 hover:text-primary-500">
                                      Mark as read
                                    </button>
                                  </div>
                                </div>
                              </li>
                            ))}
                          </ul>
                        </div>
                      </div>
                    </div>

                    <div className="border-t border-gray-200 px-4 py-6 sm:px-6">
                      <div className="flex justify-between text-sm">
                        <button className="font-medium text-primary-600 hover:text-primary-500">
                          Mark all as read
                        </button>
                        <button className="font-medium text-primary-600 hover:text-primary-500">
                          View all notifications
                        </button>
                      </div>
                    </div>
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </div>
      </Dialog>
    </Transition.Root>
  )
}
