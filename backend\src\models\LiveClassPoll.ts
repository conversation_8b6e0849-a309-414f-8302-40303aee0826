import { Schema, model, Document, Types } from 'mongoose';

export interface ILiveClassPoll extends Document {
  _id: Types.ObjectId;
  liveClass: Types.ObjectId;
  createdBy: Types.ObjectId;
  question: string;
  type: 'multiple-choice' | 'single-choice' | 'text' | 'rating' | 'yes-no';
  options: [{
    text: string;
    votes: number;
    voters: Types.ObjectId[];
  }];
  settings: {
    allowMultipleAnswers: boolean;
    showResultsImmediately: boolean;
    allowAnonymous: boolean;
    timeLimit?: number; // in seconds
    correctAnswer?: number; // index of correct option for quiz
    points?: number; // points for correct answer
  };
  status: 'draft' | 'active' | 'closed' | 'archived';
  responses: [{
    user: Types.ObjectId;
    answer: string | number | number[];
    submittedAt: Date;
    isCorrect?: boolean;
    pointsEarned?: number;
  }];
  analytics: {
    totalResponses: number;
    responseRate: number;
    averageResponseTime: number;
    correctAnswers: number;
  };
  startTime?: Date;
  endTime?: Date;
  createdAt: Date;
  updatedAt: Date;
}

const LiveClassPollSchema = new Schema<ILiveClassPoll>({
  liveClass: {
    type: Schema.Types.ObjectId,
    ref: 'LiveClass',
    required: true
  },
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  question: {
    type: String,
    required: true,
    maxlength: 500
  },
  type: {
    type: String,
    enum: ['multiple-choice', 'single-choice', 'text', 'rating', 'yes-no'],
    required: true
  },
  options: [{
    text: {
      type: String,
      required: true,
      maxlength: 200
    },
    votes: {
      type: Number,
      default: 0
    },
    voters: [{
      type: Schema.Types.ObjectId,
      ref: 'User'
    }]
  }],
  settings: {
    allowMultipleAnswers: {
      type: Boolean,
      default: false
    },
    showResultsImmediately: {
      type: Boolean,
      default: true
    },
    allowAnonymous: {
      type: Boolean,
      default: false
    },
    timeLimit: Number,
    correctAnswer: Number,
    points: {
      type: Number,
      default: 0
    }
  },
  status: {
    type: String,
    enum: ['draft', 'active', 'closed', 'archived'],
    default: 'draft'
  },
  responses: [{
    user: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    answer: Schema.Types.Mixed,
    submittedAt: {
      type: Date,
      default: Date.now
    },
    isCorrect: Boolean,
    pointsEarned: {
      type: Number,
      default: 0
    }
  }],
  analytics: {
    totalResponses: {
      type: Number,
      default: 0
    },
    responseRate: {
      type: Number,
      default: 0
    },
    averageResponseTime: {
      type: Number,
      default: 0
    },
    correctAnswers: {
      type: Number,
      default: 0
    }
  },
  startTime: Date,
  endTime: Date
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for performance
LiveClassPollSchema.index({ liveClass: 1, status: 1, createdAt: -1 });
LiveClassPollSchema.index({ createdBy: 1, createdAt: -1 });
LiveClassPollSchema.index({ 'responses.user': 1, liveClass: 1 });

// Virtual for duration
LiveClassPollSchema.virtual('duration').get(function() {
  if (this.startTime && this.endTime) {
    return Math.floor((this.endTime.getTime() - this.startTime.getTime()) / 1000);
  }
  return null;
});

export default model<ILiveClassPoll>('LiveClassPoll', LiveClassPollSchema);
