export interface User {
  _id: string;
  name: string;
  email: string;
  phone?: string;
  role: 'student' | 'teacher' | 'admin';
  avatar?: string;
  isActive: boolean;
  lastLogin?: Date;
  createdAt: Date;
  updatedAt: Date;
  preferences?: {
    language: 'en' | 'hi';
    theme: 'light' | 'dark';
    notifications: boolean;
  };
}

export interface Course {
  _id: string;
  title: string;
  description: string;
  instructor: User;
  category: string;
  level: 'beginner' | 'intermediate' | 'advanced';
  duration: number;
  price: number;
  currency: string;
  thumbnail: string;
  tags: string[];
  isPublished: boolean;
  totalLessons: number;
  totalDuration: number;
  enrollmentCount: number;
  rating: number;
  reviewCount: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface Lesson {
  _id: string;
  courseId: string;
  title: string;
  description: string;
  videoUrl?: string;
  duration: number;
  order: number;
  isPublished: boolean;
  resources: Resource[];
  createdAt: Date;
  updatedAt: Date;
}

export interface Resource {
  _id: string;
  name: string;
  type: 'pdf' | 'document' | 'link' | 'video' | 'audio';
  url: string;
  size?: number;
  createdAt: Date;
}

export interface Test {
  _id: string;
  courseId: string;
  lessonId?: string;
  title: string;
  description: string;
  duration: number;
  totalMarks: number;
  passingMarks: number;
  questions: Question[];
  isPublished: boolean;
  attempts: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface Question {
  _id: string;
  type: 'mcq' | 'true-false' | 'fill-blank' | 'essay';
  question: string;
  options?: string[];
  correctAnswer: string | string[];
  explanation?: string;
  marks: number;
  difficulty: 'easy' | 'medium' | 'hard';
}

export interface TestAttempt {
  _id: string;
  testId: string;
  userId: string;
  answers: Answer[];
  score: number;
  percentage: number;
  status: 'ongoing' | 'completed' | 'submitted';
  startedAt: Date;
  completedAt?: Date;
  timeSpent: number;
}

export interface Answer {
  questionId: string;
  answer: string | string[];
  isCorrect?: boolean;
  marksObtained?: number;
}

export interface Enrollment {
  _id: string;
  userId: string;
  courseId: string;
  enrollmentDate: Date;
  completionDate?: Date;
  progress: number;
  status: 'active' | 'completed' | 'paused';
  lastAccessedAt: Date;
}

export interface Payment {
  _id: string;
  userId: string;
  courseId: string;
  amount: number;
  currency: string;
  status: 'pending' | 'completed' | 'failed' | 'refunded';
  paymentMethod: string;
  transactionId: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Notification {
  _id: string;
  userId: string;
  title: string;
  message: string;
  type: 'info' | 'success' | 'warning' | 'error';
  isRead: boolean;
  data?: any;
  createdAt: Date;
}

export interface LiveClass {
  _id: string;
  courseId: string;
  title: string;
  description: string;
  scheduledAt: Date;
  duration: number;
  meetingUrl: string;
  maxParticipants: number;
  status: 'scheduled' | 'live' | 'ended';
  recordingUrl?: string;
  createdAt: Date;
}

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message: string;
  errors?: string[];
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
}

export interface CourseProgress {
  courseId: string;
  completedLessons: string[];
  currentLesson?: string;
  percentage: number;
  timeSpent: number;
  lastAccessedAt: Date;
}

export interface DashboardStats {
  totalCourses: number;
  completedCourses: number;
  totalHours: number;
  certificatesEarned: number;
  currentStreak: number;
}

export interface SearchFilters {
  category?: string;
  level?: string;
  priceRange?: [number, number];
  duration?: [number, number];
  rating?: number;
  language?: string;
  sortBy?: 'popularity' | 'rating' | 'price' | 'duration' | 'newest';
  sortOrder?: 'asc' | 'desc';
}
