# RJWU EduTech Platform - Frontend

A modern, responsive React.js application for the RJWU EduTech platform, built with TypeScript, Tailwind CSS, and Vite.

## Features

- 🎨 **Modern UI/UX** - Clean, responsive design with Tailwind CSS
- 🌍 **Multilingual Support** - English and Hindi with RTL support
- 🔐 **Authentication** - Secure login/register with JWT
- 📱 **Responsive Design** - Mobile-first approach
- ⚡ **Real-time Features** - Socket.io integration for live updates
- 🎥 **Video Player** - Custom video player with watermarking
- 📊 **Dashboard** - Role-based dashboards for students, teachers, and admins
- 🧪 **Testing Interface** - Interactive test taking with timer
- 📚 **Course Management** - Comprehensive course browsing and enrollment
- 🔔 **Notifications** - Real-time notifications system
- 🎯 **Accessibility** - WCAG compliant components
- 🚀 **Performance** - Optimized with React Query and lazy loading

## Tech Stack

- **Framework**: React 18 with TypeScript
- **Build Tool**: Vite
- **Styling**: Tailwind CSS
- **State Management**: Zustand
- **Data Fetching**: React Query
- **Routing**: React Router v6
- **Forms**: React Hook Form
- **Internationalization**: React i18next
- **Icons**: Lucide React
- **Animations**: Framer Motion
- **Video Player**: Video.js / React Player
- **Notifications**: React Hot Toast
- **Real-time**: Socket.io Client

## Project Structure

```
src/
├── components/          # Reusable UI components
│   ├── auth/           # Authentication components
│   ├── common/         # Common utilities components
│   ├── layout/         # Layout components
│   └── ui/             # Base UI components
├── hooks/              # Custom React hooks
├── i18n/               # Internationalization
│   └── locales/        # Translation files
├── pages/              # Page components
│   ├── auth/           # Authentication pages
│   ├── courses/        # Course-related pages
│   ├── dashboard/      # Dashboard pages
│   ├── profile/        # Profile pages
│   └── tests/          # Test pages
├── services/           # API services
├── styles/             # Global styles
├── types/              # TypeScript type definitions
└── utils/              # Utility functions
```

## Getting Started

### Prerequisites

- Node.js 16+ 
- npm, yarn, or pnpm

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd rjwu-frontend
   ```

2. **Install dependencies**
   ```bash
   npm install
   # or
   yarn install
   # or
   pnpm install
   ```

3. **Environment Setup**
   ```bash
   cp .env.example .env
   ```
   
   Edit `.env` with your configuration:
   ```env
   VITE_API_URL=http://localhost:5000/api
   VITE_SOCKET_URL=http://localhost:5000
   ```

4. **Start development server**
   ```bash
   npm run dev
   ```

   The application will be available at `http://localhost:3000`

## Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint
- `npm test` - Run tests
- `npm run test:watch` - Run tests in watch mode

## Development Guidelines

### Code Style

- Use TypeScript for all new files
- Follow ESLint and Prettier configurations
- Use functional components with hooks
- Prefer async/await over Promise chains
- Use descriptive variable and function names

### Component Structure

```tsx
// Component imports
import { useState } from 'react';
import { useTranslation } from 'react-i18next';

// Type imports
import { ComponentProps } from '@/types';

// Service imports
import { apiService } from '@/services/api';

interface ComponentNameProps {
  // Props definition
}

const ComponentName = ({ prop1, prop2 }: ComponentNameProps) => {
  // Hooks
  const { t } = useTranslation();
  const [state, setState] = useState();

  // Event handlers
  const handleClick = () => {
    // Handler logic
  };

  // Render
  return (
    <div className="component-wrapper">
      {/* Component JSX */}
    </div>
  );
};

export default ComponentName;
```

### Styling Guidelines

- Use Tailwind CSS utility classes
- Create custom components in globals.css when needed
- Follow mobile-first responsive design
- Use semantic color tokens (primary, secondary, etc.)

### State Management

- Use Zustand for global state
- Use React Query for server state
- Use local useState for component state
- Avoid prop drilling with context when needed

### API Integration

- Use the centralized `apiService` for all API calls
- Implement proper error handling
- Use React Query for caching and background updates
- Show loading and error states

### Internationalization

- Use the `useTranslation` hook for all text
- Add new translations to both `en.json` and `hi.json`
- Support RTL layout for Arabic/Hebrew if needed
- Use semantic translation keys

### Testing

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage
```

## Building for Production

```bash
# Build the application
npm run build

# Preview the build
npm run preview
```

The build output will be in the `dist/` directory.

## Deployment

### Environment Variables

Set the following environment variables for production:

```env
VITE_API_URL=https://api.rjwu-edutech.com/api
VITE_SOCKET_URL=https://api.rjwu-edutech.com
VITE_NODE_ENV=production
```

### Build and Deploy

```bash
# Build for production
npm run build

# Deploy the dist/ directory to your hosting provider
```

## Browser Support

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)
- Mobile browsers (iOS Safari, Chrome Mobile)

## Performance Optimizations

- **Code Splitting**: Automatic route-based code splitting
- **Image Optimization**: Responsive images with proper formats
- **Lazy Loading**: Components and routes loaded on demand
- **Caching**: React Query for intelligent data caching
- **Bundle Analysis**: Use `npm run analyze` to analyze bundle size

## Security Features

- **XSS Protection**: Sanitized user inputs
- **CSRF Protection**: Token-based authentication
- **Content Security Policy**: Implemented in production
- **Secure Headers**: Security headers configuration
- **Input Validation**: Client and server-side validation

## Accessibility

- **WCAG 2.1 AA Compliance**: Accessible to users with disabilities
- **Keyboard Navigation**: Full keyboard accessibility
- **Screen Reader Support**: ARIA labels and semantic HTML
- **Color Contrast**: Meets accessibility standards
- **Focus Management**: Proper focus handling

## Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/new-feature`
3. Commit changes: `git commit -am 'Add new feature'`
4. Push to branch: `git push origin feature/new-feature`
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support, email <EMAIL> or create an issue in the repository.
